# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs (except dist)
.next/
out/

# Environment files (will be set in Vercel dashboard)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Temporary files
tmp/
temp/

# Documentation and development files
docs/
*.md
!README.md

# Test files (exclude test directories but keep components/test for admin tools)
test/
tests/
__tests__/
*.test.*
*.spec.*

# Keep admin test components that are used in production
!components/test/

# Development scripts (exclude most but keep build-related ones)
scripts/debug-*.js
scripts/test-*.js
scripts/fix-*.js
scripts/setup-*.js
scripts/create-*.js
scripts/analyze-*.js
scripts/migrate-*.js
scripts/run-*.js
scripts/reset-*.js
scripts/simple-*.js
scripts/sponsor-*.js
scripts/telegram-*.js
scripts/update-*.js
scripts/quick-*.js
scripts/ensure-*.js
scripts/final-*.js
scripts/dynamic-*.js
scripts/dividend-*.js

# Keep essential build scripts
!scripts/check-env-vars.js
!scripts/verify-build.js
!scripts/copy-server-files.js
!scripts/package-for-deployment.js
!scripts/test-build-integrity.js
!scripts/health-check.js
!scripts/startup.js
!scripts/validate-css-classes.js

# Database files
*.sql
*.db
*.sqlite

# Backup files
*.backup
*_backup.*
*.bak

# Development artifacts
.cache/
coverage/
.nyc_output/

# Large files that aren't needed for deployment
dist.zip
*.zip
*.tar.gz

# Bot files (not needed for web deployment)
aureus_bot/
