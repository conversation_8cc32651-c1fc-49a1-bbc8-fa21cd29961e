import React, { useState, useEffect } from 'react'
import { supabase, getServiceRoleClient } from '../../lib/supabase'
import {
  hashPassword,
  validatePasswordStrength,
  getPasswordStrengthScore,
  generateSecurePassword
} from '../../lib/passwordSecurity'
import { logAdminAction } from '../../lib/adminAuth'
import { TelegramUserManager } from './TelegramUserManager'

interface User {
  id: number
  username: string
  email: string
  password_hash: string
  full_name: string | null
  phone: string | null
  address: string | null
  is_active: boolean
  is_verified: boolean
  telegram_id: number | null
  country_of_residence: string | null
  role: string
  is_admin: boolean
  created_at: string
  updated_at: string
  telegram_users?: {
    id: string
    telegram_id: number
    username: string
    first_name: string
    last_name: string
  }[]
}

interface UserEditModalProps {
  user: User
  isOpen: boolean
  onClose: () => void
  onSave: () => void
  adminUser?: any
}

interface FormData {
  username: string
  email: string
  full_name: string
  phone: string
  address: string
  is_active: boolean
  is_verified: boolean
  is_admin: boolean
  role: string
  country_of_residence: string
  password: string
  confirmPassword: string
}

interface ValidationErrors {
  [key: string]: string
}

export const UserEditModal: React.FC<UserEditModalProps> = ({
  user,
  isOpen,
  onClose,
  onSave,
  adminUser
}) => {
  const [formData, setFormData] = useState<FormData>({
    username: '',
    email: '',
    full_name: '',
    phone: '',
    address: '',
    is_active: true,
    is_verified: false,
    is_admin: false,
    role: 'user',
    country_of_residence: '',
    password: '',
    confirmPassword: ''
  })

  const [errors, setErrors] = useState<ValidationErrors>({})
  const [loading, setSaving] = useState(false)
  const [showPasswordFields, setShowPasswordFields] = useState(false)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)
  const [passwordStrength, setPasswordStrength] = useState<any>(null)
  const [showGeneratedPassword, setShowGeneratedPassword] = useState(false)
  const [showTelegramManager, setShowTelegramManager] = useState(false)
  const [showPassword, setShowPassword] = useState(true)
  const [showConfirmPassword, setShowConfirmPassword] = useState(true)

  useEffect(() => {
    if (user) {
      setFormData({
        username: user.username || '',
        email: user.email || '',
        full_name: user.full_name || '',
        phone: user.phone || '',
        address: user.address || '',
        is_active: user.is_active,
        is_verified: user.is_verified,
        is_admin: user.is_admin,
        role: user.role || 'user',
        country_of_residence: user.country_of_residence || '',
        password: '',
        confirmPassword: ''
      })
    }
  }, [user])

  const validateForm = (): boolean => {
    const newErrors: ValidationErrors = {}

    // Username validation
    if (!formData.username.trim()) {
      newErrors.username = 'Username is required'
    } else if (formData.username.length < 3) {
      newErrors.username = 'Username must be at least 3 characters'
    } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
      newErrors.username = 'Username can only contain letters, numbers, and underscores'
    }

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    // Full name validation
    if (formData.full_name && formData.full_name.length > 255) {
      newErrors.full_name = 'Full name must be less than 255 characters'
    }

    // Phone validation
    if (formData.phone && !/^[\+]?[1-9][\d]{0,15}$/.test(formData.phone.replace(/[\s\-\(\)]/g, ''))) {
      newErrors.phone = 'Please enter a valid phone number'
    }

    // Password validation (only if changing password)
    if (showPasswordFields) {
      if (!formData.password) {
        newErrors.password = 'Password is required when changing password'
      } else {
        const passwordValidation = validatePasswordStrength(formData.password)
        if (!passwordValidation.valid) {
          newErrors.password = passwordValidation.errors[0] // Show first error
        }
      }

      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match'
      }
    }

    // Country code validation
    if (formData.country_of_residence && formData.country_of_residence.length !== 3) {
      newErrors.country_of_residence = 'Country code must be 3 characters (ISO 3166-1 alpha-3)'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (field: keyof FormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))

    // Update password strength meter for password field
    if (field === 'password' && typeof value === 'string') {
      const score = getPasswordStrengthScore(value)
      setPasswordStrength({
        score,
        label: score >= 80 ? 'Strong' : score >= 60 ? 'Good' : score >= 40 ? 'Fair' : 'Weak',
        color: score >= 80 ? 'green' : score >= 60 ? 'yellow' : score >= 40 ? 'orange' : 'red'
      })
    }

    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    // Show confirmation dialog for sensitive changes
    const sensitiveChanges = 
      formData.is_admin !== user.is_admin ||
      formData.is_active !== user.is_active ||
      showPasswordFields

    if (sensitiveChanges && !showConfirmDialog) {
      setShowConfirmDialog(true)
      return
    }

    await saveUser()
  }

  const saveUser = async () => {
    try {
      setSaving(true)
      setShowConfirmDialog(false)

      // Prepare update data
      const updateData: any = {
        username: formData.username,
        email: formData.email,
        full_name: formData.full_name || null,
        phone: formData.phone || null,
        address: formData.address || null,
        is_active: formData.is_active,
        is_verified: formData.is_verified,
        is_admin: formData.is_admin,
        role: formData.role,
        country_of_residence: formData.country_of_residence || null,
        updated_at: new Date().toISOString()
      }

      // Hash password if changing
      if (showPasswordFields && formData.password) {
        console.log('🔐 Admin changing user password - hashing with bcrypt...')
        updateData.password_hash = await hashPassword(formData.password)
        console.log('✅ Password hashed successfully for admin password change')
      }

      // Use service role client to bypass RLS policies for admin operations
      const serviceRoleClient = getServiceRoleClient()

      // Check for duplicate username/email
      const { data: existingUsers, error: checkError } = await serviceRoleClient
        .from('users')
        .select('id, username, email')
        .or(`username.eq.${formData.username},email.eq.${formData.email}`)
        .neq('id', user.id)

      if (checkError) throw checkError

      if (existingUsers && existingUsers.length > 0) {
        const duplicateUsername = existingUsers.find(u => u.username === formData.username)
        const duplicateEmail = existingUsers.find(u => u.email === formData.email)

        if (duplicateUsername) {
          setErrors({ username: 'Username already exists' })
          return
        }
        if (duplicateEmail) {
          setErrors({ email: 'Email already exists' })
          return
        }
      }

      // Update user
      const { error: updateError } = await serviceRoleClient
        .from('users')
        .update(updateData)
        .eq('id', user.id)

      if (updateError) throw updateError

      // Log the change (audit trail)
      await logAdminAction(
        '<EMAIL>', // Would get from context
        'UPDATE_USER',
        'user',
        user.id.toString(),
        {
          fields_changed: Object.keys(updateData),
          password_changed: showPasswordFields && formData.password ? true : false
        },
        {
          username: user.username,
          email: user.email,
          is_active: user.is_active,
          is_admin: user.is_admin
        },
        {
          username: updateData.username,
          email: updateData.email,
          is_active: updateData.is_active,
          is_admin: updateData.is_admin
        }
      )

      onSave()
      onClose()
    } catch (err: any) {
      console.error('Error saving user:', err)
      setErrors({ general: err.message || 'Failed to save user' })
    } finally {
      setSaving(false)
    }
  }

  const generatePassword = () => {
    const newPassword = generateSecurePassword(16)
    setFormData(prev => ({
      ...prev,
      password: newPassword,
      confirmPassword: newPassword
    }))
    const score = getPasswordStrengthScore(newPassword)
    setPasswordStrength({
      score,
      label: 'Strong',
      color: 'green'
    })
    setShowGeneratedPassword(true)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-gray-900 border-b border-gray-700 px-6 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-bold text-white">
                ✏️ Edit User: {user.full_name || user.username}
              </h2>
              <p className="text-gray-400 text-sm">
                User ID: {user.id} | Created: {new Date(user.created_at).toLocaleDateString()}
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white text-2xl"
            >
              ×
            </button>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* General Error */}
          {errors.general && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
              <p className="text-red-400">❌ {errors.general}</p>
            </div>
          )}

          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white border-b border-gray-700 pb-2">
              👤 Basic Information
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Username */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Username *
                </label>
                <input
                  type="text"
                  value={formData.username}
                  onChange={(e) => handleInputChange('username', e.target.value)}
                  className={`w-full px-3 py-2 bg-gray-800 border rounded-lg text-white placeholder-gray-400 focus:outline-none ${
                    errors.username ? 'border-red-500' : 'border-gray-600 focus:border-yellow-500'
                  }`}
                  placeholder="Enter username"
                />
                {errors.username && (
                  <p className="text-red-400 text-sm mt-1">{errors.username}</p>
                )}
              </div>

              {/* Email */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Email Address *
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className={`w-full px-3 py-2 bg-gray-800 border rounded-lg text-white placeholder-gray-400 focus:outline-none ${
                    errors.email ? 'border-red-500' : 'border-gray-600 focus:border-yellow-500'
                  }`}
                  placeholder="Enter email address"
                />
                {errors.email && (
                  <p className="text-red-400 text-sm mt-1">{errors.email}</p>
                )}
              </div>

              {/* Full Name */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Full Name
                </label>
                <input
                  type="text"
                  value={formData.full_name}
                  onChange={(e) => handleInputChange('full_name', e.target.value)}
                  className={`w-full px-3 py-2 bg-gray-800 border rounded-lg text-white placeholder-gray-400 focus:outline-none ${
                    errors.full_name ? 'border-red-500' : 'border-gray-600 focus:border-yellow-500'
                  }`}
                  placeholder="Enter full name"
                />
                {errors.full_name && (
                  <p className="text-red-400 text-sm mt-1">{errors.full_name}</p>
                )}
              </div>

              {/* Phone */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Phone Number
                </label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className={`w-full px-3 py-2 bg-gray-800 border rounded-lg text-white placeholder-gray-400 focus:outline-none ${
                    errors.phone ? 'border-red-500' : 'border-gray-600 focus:border-yellow-500'
                  }`}
                  placeholder="Enter phone number"
                />
                {errors.phone && (
                  <p className="text-red-400 text-sm mt-1">{errors.phone}</p>
                )}
              </div>
            </div>

            {/* Address */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Address
              </label>
              <textarea
                value={formData.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-yellow-500"
                placeholder="Enter address"
              />
            </div>

            {/* Country */}
            <div className="md:w-1/2">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Country Code (ISO 3166-1 alpha-3)
              </label>
              <input
                type="text"
                value={formData.country_of_residence}
                onChange={(e) => handleInputChange('country_of_residence', e.target.value.toUpperCase())}
                maxLength={3}
                className={`w-full px-3 py-2 bg-gray-800 border rounded-lg text-white placeholder-gray-400 focus:outline-none ${
                  errors.country_of_residence ? 'border-red-500' : 'border-gray-600 focus:border-yellow-500'
                }`}
                placeholder="e.g., USA, ZAF, GBR"
              />
              {errors.country_of_residence && (
                <p className="text-red-400 text-sm mt-1">{errors.country_of_residence}</p>
              )}
            </div>
          </div>

          {/* Account Status */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white border-b border-gray-700 pb-2">
              🔐 Account Status & Permissions
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                {/* Active Status */}
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={formData.is_active}
                    onChange={(e) => handleInputChange('is_active', e.target.checked)}
                    className="w-4 h-4 text-yellow-500 bg-gray-800 border-gray-600 rounded focus:ring-yellow-500"
                  />
                  <span className="text-gray-300">Account Active</span>
                </label>

                {/* Verified Status */}
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={formData.is_verified}
                    onChange={(e) => handleInputChange('is_verified', e.target.checked)}
                    className="w-4 h-4 text-yellow-500 bg-gray-800 border-gray-600 rounded focus:ring-yellow-500"
                  />
                  <span className="text-gray-300">Email Verified</span>
                </label>

                {/* Admin Status */}
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={formData.is_admin}
                    onChange={(e) => handleInputChange('is_admin', e.target.checked)}
                    className="w-4 h-4 text-yellow-500 bg-gray-800 border-gray-600 rounded focus:ring-yellow-500"
                  />
                  <span className="text-yellow-400 font-medium">Admin Privileges</span>
                </label>
              </div>

              <div>
                {/* Role */}
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  User Role
                </label>
                <select
                  value={formData.role}
                  onChange={(e) => handleInputChange('role', e.target.value)}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
                >
                  <option value="user">User</option>
                  <option value="admin">Admin</option>
                  <option value="super_admin">Super Admin</option>
                  <option value="moderator">Moderator</option>
                </select>
              </div>
            </div>
          </div>

          {/* Password Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-white border-b border-gray-700 pb-2">
                🔑 Password Management
              </h3>
              <button
                type="button"
                onClick={() => setShowPasswordFields(!showPasswordFields)}
                className="text-yellow-400 hover:text-yellow-300 text-sm font-medium"
              >
                {showPasswordFields ? 'Cancel Password Change' : 'Change Password'}
              </button>
            </div>

            {showPasswordFields && (
              <div className="space-y-4 p-4 bg-gray-800/30 rounded-lg">
                {/* Password Generation */}
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-300">Need a secure password?</span>
                  <button
                    type="button"
                    onClick={generatePassword}
                    className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-500 transition-colors"
                  >
                    🎲 Generate Secure Password
                  </button>
                </div>

                {showGeneratedPassword && (
                  <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-3">
                    <p className="text-green-400 text-sm">
                      ✅ Secure password generated! Make sure to save it securely.
                    </p>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* New Password */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      New Password *
                    </label>
                    <div className="relative">
                      <input
                        type={showPassword ? "text" : "password"}
                        value={formData.password}
                        onChange={(e) => handleInputChange('password', e.target.value)}
                        className={`w-full px-3 py-2 pr-10 bg-gray-800 border rounded-lg text-white placeholder-gray-400 focus:outline-none ${
                          errors.password ? 'border-red-500' : 'border-gray-600 focus:border-yellow-500'
                        }`}
                        placeholder="Enter new password"
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-white"
                      >
                        {showPassword ? (
                          <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                          </svg>
                        ) : (
                          <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        )}
                      </button>
                    </div>
                    {errors.password && (
                      <p className="text-red-400 text-sm mt-1">{errors.password}</p>
                    )}

                    {/* Password Strength Meter */}
                    {passwordStrength && formData.password && (
                      <div className="mt-2">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-xs text-gray-400">Password Strength</span>
                          <span className={`text-xs font-medium ${
                            passwordStrength.strength === 'strong' ? 'text-green-400' :
                            passwordStrength.strength === 'medium' ? 'text-yellow-400' : 'text-red-400'
                          }`}>
                            {passwordStrength.text}
                          </span>
                        </div>
                        <div className="w-full bg-gray-700 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full transition-all duration-300 ${passwordStrength.color}`}
                            style={{ width: `${passwordStrength.percentage}%` }}
                          ></div>
                        </div>
                        {passwordStrength.errors && Array.isArray(passwordStrength.errors) && passwordStrength.errors.length > 0 && (
                          <div className="mt-1">
                            {passwordStrength.errors.map((error, index) => (
                              <p key={index} className="text-xs text-red-400">• {error}</p>
                            ))}
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                {/* Confirm Password */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Confirm Password *
                  </label>
                  <div className="relative">
                    <input
                      type={showConfirmPassword ? "text" : "password"}
                      value={formData.confirmPassword}
                      onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                      className={`w-full px-3 py-2 pr-10 bg-gray-800 border rounded-lg text-white placeholder-gray-400 focus:outline-none ${
                        errors.confirmPassword ? 'border-red-500' : 'border-gray-600 focus:border-yellow-500'
                      }`}
                      placeholder="Confirm new password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-white"
                    >
                      {showConfirmPassword ? (
                        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                        </svg>
                      ) : (
                        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      )}
                    </button>
                  </div>
                  {errors.confirmPassword && (
                    <p className="text-red-400 text-sm mt-1">{errors.confirmPassword}</p>
                  )}
                </div>

                </div>

                <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3">
                  <h4 className="text-sm font-medium text-blue-400 mb-2">🔐 Password Requirements</h4>
                  <ul className="text-xs text-gray-400 space-y-1">
                    <li>• At least 8 characters long</li>
                    <li>• Contains uppercase and lowercase letters</li>
                    <li>• Contains at least one number</li>
                    <li>• Contains at least one special character</li>
                    <li>• Avoid common passwords and patterns</li>
                  </ul>
                </div>
              </div>
            )}
          </div>

          {/* Telegram Information */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-white border-b border-gray-700 pb-2">
                💬 Telegram Connections
              </h3>
              <button
                type="button"
                onClick={() => setShowTelegramManager(true)}
                className="text-blue-400 hover:text-blue-300 text-sm font-medium"
              >
                Manage Telegram Accounts
              </button>
            </div>

            {user.telegram_users && Array.isArray(user.telegram_users) && user.telegram_users.length > 0 ? (
              <div className="bg-gray-800/30 rounded-lg p-4">
                <div className="space-y-3">
                  {user.telegram_users.map((tgUser, index) => (
                    <div key={index} className="grid grid-cols-1 md:grid-cols-3 gap-4 p-3 bg-gray-700/30 rounded">
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          Username
                        </label>
                        <p className="text-blue-400">@{tgUser.username}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          Name
                        </label>
                        <p className="text-white">
                          {tgUser.first_name} {tgUser.last_name}
                        </p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          Telegram ID
                        </label>
                        <p className="text-gray-400 font-mono">{tgUser.telegram_id}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="bg-gray-800/30 rounded-lg p-4 text-center">
                <p className="text-gray-400">No Telegram accounts linked to this user</p>
                <button
                  type="button"
                  onClick={() => setShowTelegramManager(true)}
                  className="mt-2 text-blue-400 hover:text-blue-300 text-sm font-medium"
                >
                  Link Telegram Account
                </button>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4 pt-6 border-t border-gray-700">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-2 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-6 py-2 bg-yellow-500 text-black rounded-lg hover:bg-yellow-400 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {loading && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black"></div>
              )}
              <span>{loading ? 'Saving...' : 'Save Changes'}</span>
            </button>
          </div>
        </form>
      </div>

      {/* Confirmation Dialog */}
      {showConfirmDialog && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-60">
          <div className="bg-gray-900 rounded-lg p-6 w-full max-w-md mx-4 border border-yellow-500/30">
            <h3 className="text-lg font-bold text-yellow-400 mb-4">
              ⚠️ Confirm Sensitive Changes
            </h3>
            <p className="text-gray-300 mb-6">
              You are about to make sensitive changes to this user account. This may affect:
            </p>
            <ul className="text-gray-400 text-sm space-y-1 mb-6">
              {formData.is_admin !== user.is_admin && (
                <li>• Admin privileges will be {formData.is_admin ? 'granted' : 'revoked'}</li>
              )}
              {formData.is_active !== user.is_active && (
                <li>• Account will be {formData.is_active ? 'activated' : 'deactivated'}</li>
              )}
              {showPasswordFields && (
                <li>• User password will be changed</li>
              )}
            </ul>
            <div className="flex justify-end space-x-4">
              <button
                onClick={() => setShowConfirmDialog(false)}
                className="px-4 py-2 bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
              >
                Cancel
              </button>
              <button
                onClick={saveUser}
                className="px-4 py-2 bg-yellow-500 text-black rounded hover:bg-yellow-400"
              >
                Confirm Changes
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Telegram User Manager Modal */}
      {showTelegramManager && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-60">
          <div className="bg-gray-900 rounded-lg p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
            <TelegramUserManager
              userId={user.id}
              onClose={() => setShowTelegramManager(false)}
              onUpdate={() => {
                // Refresh user data would be handled by parent
                onSave()
              }}
              adminUser={adminUser}
            />
          </div>
        </div>
      )}
    </div>
  )
}
