import React from 'react';
import { ComprehensiveDividendsCalculator } from '../components/dividends/ComprehensiveDividendsCalculator';

interface CalculatorPageProps {
  onNavigate: (page: string) => void;
}

const CalculatorPage: React.FC<CalculatorPageProps> = ({ onNavigate }) => {
  return (
    <div className="page">
      {/* Page Header */}
      <section className="page-header">
        <div className="container">
          <div className="breadcrumb">
            <button onClick={() => onNavigate('home')} className="breadcrumb-link">
              Home
            </button>
            <span className="breadcrumb-separator">→</span>
            <span className="breadcrumb-current">Returns Calculator</span>
          </div>
          
          <h1 className="page-title">Dividend & Returns Calculator</h1>
          <p className="page-subtitle">
            Calculate your potential returns based on share ownership, gold production forecasts, 
            and our 5-year expansion plan
          </p>
        </div>
      </section>

      {/* Calculator Introduction */}
      <section className="calculator-intro">
        <div className="container">
          <div className="intro-grid">
            <div className="intro-card">
              <div className="intro-icon">📊</div>
              <h3>Comprehensive Analysis</h3>
              <p>
                Our calculator uses real operational data, current gold prices, and proven 
                mining parameters to provide accurate dividend projections.
              </p>
            </div>

            <div className="intro-card">
              <div className="intro-icon">🎯</div>
              <h3>Multiple Scenarios</h3>
              <p>
                Model different investment amounts, entry phases, and market conditions 
                to find the strategy that works best for you.
              </p>
            </div>

            <div className="intro-card">
              <div className="intro-icon">📈</div>
              <h3>5-Year Projections</h3>
              <p>
                See how your dividends grow as operations scale from 10 plants in 2026 
                to 200+ plants by 2030.
              </p>
            </div>

            <div className="intro-card">
              <div className="intro-icon">🔍</div>
              <h3>Detailed Breakdown</h3>
              <p>
                Understand exactly how dividends are calculated from gold production, 
                recovery rates, and operational costs.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Key Assumptions */}
      <section className="calculator-assumptions">
        <div className="container">
          <div className="section-header">
            <h2>Calculation Parameters</h2>
            <p>Key assumptions and data sources used in our projections</p>
          </div>

          <div className="assumptions-grid">
            <div className="assumption-group">
              <h3>🏭 Operational Parameters</h3>
              <ul>
                <li><strong>Plant Capacity:</strong> 200 tonnes per hour</li>
                <li><strong>Operating Hours:</strong> 20 hours per day</li>
                <li><strong>Operating Days:</strong> 330 days per year</li>
                <li><strong>Land per Plant:</strong> 25 hectares</li>
                <li><strong>Recovery Rate:</strong> 70% (targeting 95%)</li>
              </ul>
            </div>

            <div className="assumption-group">
              <h3>💰 Financial Parameters</h3>
              <ul>
                <li><strong>Gold Price:</strong> $109,026 per kg (current)</li>
                <li><strong>In-Situ Grade:</strong> 0.9 g/m³</li>
                <li><strong>Operating Costs:</strong> 45% of revenue</li>
                <li><strong>Dividend Payout:</strong> 100% of EBIT</li>
                <li><strong>Total Shares:</strong> 1,400,000</li>
              </ul>
            </div>

            <div className="assumption-group">
              <h3>📅 Expansion Timeline</h3>
              <ul>
                <li><strong>2026:</strong> 10 plants, 250 hectares</li>
                <li><strong>2027:</strong> 25 plants, 625 hectares</li>
                <li><strong>2028:</strong> 50 plants, 1,250 hectares</li>
                <li><strong>2029:</strong> 100 plants, 2,500 hectares</li>
                <li><strong>2030:</strong> 200 plants, 6,000 hectares</li>
              </ul>
            </div>

            <div className="assumption-group">
              <h3>🎯 Conservative Approach</h3>
              <ul>
                <li><strong>Recovery Rate:</strong> Using 70% vs target 95%</li>
                <li><strong>Gold Price:</strong> Current market rates</li>
                <li><strong>Operating Costs:</strong> Industry standard 45%</li>
                <li><strong>No Speculation:</strong> Based on proven data</li>
                <li><strong>Transparent:</strong> All assumptions disclosed</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Calculator Tool */}
      <section className="calculator-tool">
        <div className="container">
          <div className="section-header">
            <h2>Interactive Calculator</h2>
            <p>Model your investment scenario and explore potential returns</p>
          </div>

          <div className="calculator-wrapper">
            <ComprehensiveDividendsCalculator 
              userShares={1}
              currentPhase={null}
            />
          </div>
        </div>
      </section>

      {/* Gold Production Based Scenarios */}
      <section className="example-scenarios">
        <div className="container">
          <div className="section-header">
            <h2>Gold Production Based Scenarios</h2>
            <p>Realistic projections based on actual gold mining operations and expansion plan</p>
          </div>

          <div className="scenarios-grid">
            <div className="scenario-card">
              <div className="scenario-header">
                <h3>Conservative Shareholder</h3>
                <div className="scenario-investment">$1,000 Share Purchase</div>
              </div>
              <div className="scenario-details">
                <div className="scenario-shares">200 shares @ $5 (Presale)</div>
                <div className="scenario-projections">
                  <div className="projection-year">
                    <span className="year">2026:</span>
                    <span className="dividend">$15,830 annual dividend</span>
                  </div>
                  <div className="projection-year">
                    <span className="year">2030:</span>
                    <span className="dividend">$316,612 annual dividend</span>
                  </div>
                </div>
                <div className="scenario-total">
                  <strong>Based on: $79.15 to $1,583.06 per share</strong>
                </div>
              </div>
            </div>

            <div className="scenario-card">
              <div className="scenario-header">
                <h3>Growth Shareholder</h3>
                <div className="scenario-investment">$5,000 Share Purchase</div>
              </div>
              <div className="scenario-details">
                <div className="scenario-shares">1,000 shares @ $5 (Presale)</div>
                <div className="scenario-projections">
                  <div className="projection-year">
                    <span className="year">2026:</span>
                    <span className="dividend">$79,150 annual dividend</span>
                  </div>
                  <div className="projection-year">
                    <span className="year">2030:</span>
                    <span className="dividend">$1,583,060 annual dividend</span>
                  </div>
                </div>
                <div className="scenario-total">
                  <strong>Based on: 1,848kg to 36,960kg gold output</strong>
                </div>
              </div>
            </div>

            <div className="scenario-card">
              <div className="scenario-header">
                <h3>Serious Shareholder</h3>
                <div className="scenario-investment">$25,000 Share Purchase</div>
              </div>
              <div className="scenario-details">
                <div className="scenario-shares">5,000 shares @ $5 (Presale)</div>
                <div className="scenario-projections">
                  <div className="projection-year">
                    <span className="year">2026:</span>
                    <span className="dividend">$395,750 annual dividend</span>
                  </div>
                  <div className="projection-year">
                    <span className="year">2030:</span>
                    <span className="dividend">$7,915,300 annual dividend</span>
                  </div>
                </div>
                <div className="scenario-total">
                  <strong>Based on: 10 to 200 wash plants (100% EBIT)</strong>
                </div>
              </div>
            </div>
          </div>

          <div className="scenarios-disclaimer">
            <p>
              <strong>Important Notice:</strong> These projections are based on Aureus Alliance Holdings'
              5-year expansion plan and actual gold production calculations. Dividends are distributed from
              real mining profits (100% of EBIT). Actual results depend on gold prices, operational efficiency,
              weather conditions, and regulatory factors. Gold mining involves inherent risks and past
              performance does not guarantee future results.
            </p>
          </div>
        </div>
      </section>

      {/* Next Steps */}
      <section className="next-steps">
        <div className="container">
          <div className="section-header">
            <h2>Ready to Proceed?</h2>
            <p>Explore more details or start your investment journey</p>
          </div>

          <div className="next-steps-grid">
            <button 
              className="next-step-card"
              onClick={() => onNavigate('financial-data')}
            >
              <div className="step-icon">📊</div>
              <h3>View Financial Data</h3>
              <p>Detailed tables and production forecasts</p>
            </button>

            <button 
              className="next-step-card"
              onClick={() => onNavigate('investment-phases')}
            >
              <div className="step-icon">📈</div>
              <h3>Review Phases</h3>
              <p>Complete breakdown of all investment phases</p>
            </button>

            <button 
              className="next-step-card"
              onClick={() => alert('Coming Soon - Registration')}
            >
              <div className="step-icon">🚀</div>
              <h3>Start Investing</h3>
              <p>Begin your shareholder registration</p>
            </button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default CalculatorPage;
