import React from 'react';
import { ComprehensivePhaseDisplay } from '../components/ComprehensivePhaseDisplay';

interface InvestmentPhasesPageProps {
  onNavigate: (page: string) => void;
}

const InvestmentPhasesPage: React.FC<InvestmentPhasesPageProps> = ({ onNavigate }) => {
  // Phase summary data for quick overview
  const phaseSummary = [
    { 
      group: "Presale", 
      priceRange: "$5.00", 
      shares: "200,000", 
      totalFunds: "$1M",
      highlight: "Launch First Wash Plant + Feed 2,000+ Children"
    },
    { 
      group: "Phase 1-5", 
      priceRange: "$10 - $30", 
      shares: "500,000", 
      totalFunds: "$8.75M",
      highlight: "5 Wash Plants + Feed 12,000+ Children"
    },
    { 
      group: "Phase 6-10", 
      priceRange: "$35 - $100", 
      shares: "250,000", 
      totalFunds: "$16.25M",
      highlight: "10 Plants + Mutare Expansion + 30,000+ Children"
    },
    { 
      group: "Phase 11-15", 
      priceRange: "$200 - $600", 
      shares: "250,000", 
      totalFunds: "$100M",
      highlight: "87 Plants + South Africa + 150,000+ Children"
    },
    { 
      group: "Phase 16-19", 
      priceRange: "$700 - $1,000", 
      shares: "200,000", 
      totalFunds: "$170M",
      highlight: "257 Plants + Multi-Country + 250,000+ Children"
    }
  ];

  return (
    <div className="page">
      {/* Page Header */}
      <section className="page-header">
        <div className="container">
          <div className="breadcrumb">
            <button onClick={() => onNavigate('home')} className="breadcrumb-link">
              Home
            </button>
            <span className="breadcrumb-separator">→</span>
            <span className="breadcrumb-current">Investment Phases</span>
          </div>
          
          <h1 className="page-title">20-Phase Investment Structure</h1>
          <p className="page-subtitle">
            Progressive pricing from $5 presale to $1,000 per share with detailed fund allocation 
            for operations and community impact initiatives
          </p>
        </div>
      </section>

      {/* Phase Overview Summary */}
      <section className="phase-overview">
        <div className="container">
          <div className="section-header">
            <h2>Phase Groups Overview</h2>
            <p>Strategic grouping of phases showing progression and impact</p>
          </div>

          <div className="phase-summary-grid">
            {phaseSummary.map((phase, index) => (
              <div key={index} className="phase-summary-card">
                <div className="phase-summary-header">
                  <h3 className="phase-group">{phase.group}</h3>
                  <div className="price-range">{phase.priceRange}</div>
                </div>
                
                <div className="phase-summary-details">
                  <div className="detail-row">
                    <span className="detail-label">Shares Available:</span>
                    <span className="detail-value">{phase.shares}</span>
                  </div>
                  <div className="detail-row">
                    <span className="detail-label">Total Funding:</span>
                    <span className="detail-value">{phase.totalFunds}</span>
                  </div>
                </div>

                <div className="phase-highlight">
                  <div className="highlight-icon">✨</div>
                  <p>{phase.highlight}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Key Investment Points */}
      <section className="investment-points">
        <div className="container">
          <div className="section-header">
            <h2>Why Progressive Pricing?</h2>
            <p>Understanding our phase-based investment structure</p>
          </div>

          <div className="points-grid">
            <div className="point-card">
              <div className="point-icon">📈</div>
              <h3>Increasing Value</h3>
              <p>
                Share prices increase as operations scale and risk decreases. Early investors 
                benefit from the lowest prices with highest potential returns.
              </p>
            </div>

            <div className="point-card">
              <div className="point-icon">🎯</div>
              <h3>Milestone-Based</h3>
              <p>
                Each phase funds specific operational milestones and community projects, 
                ensuring transparent use of investor capital.
              </p>
            </div>

            <div className="point-card">
              <div className="point-icon">🔒</div>
              <h3>Risk Mitigation</h3>
              <p>
                Progressive funding reduces overall project risk while allowing investors 
                to participate at their preferred risk-reward level.
              </p>
            </div>

            <div className="point-card">
              <div className="point-icon">🌍</div>
              <h3>Impact Scaling</h3>
              <p>
                Community impact grows with each phase, from feeding 2,000 children in 
                presale to 250,000+ children by Phase 19.
              </p>
            </div>
          </div>
        </div>
      </section>



      {/* Investment Strategy */}
      <section className="investment-strategy">
        <div className="container">
          <div className="section-header">
            <h2>Investment Strategy Recommendations</h2>
            <p>Guidance for different investor profiles</p>
          </div>

          <div className="strategy-grid">
            <div className="strategy-card">
              <div className="strategy-badge early">Early Adopter</div>
              <h3>Presale & Phase 1-3</h3>
              <div className="strategy-price">$5 - $20 per share</div>
              <ul className="strategy-benefits">
                <li>Lowest entry prices</li>
                <li>Highest potential returns</li>
                <li>Ground-floor opportunity</li>
                <li>Maximum dividend potential</li>
              </ul>
              <div className="strategy-risk">
                <strong>Risk Level:</strong> Higher (pre-operational)
              </div>
            </div>

            <div className="strategy-card">
              <div className="strategy-badge growth">Growth Investor</div>
              <h3>Phase 4-10</h3>
              <div className="strategy-price">$25 - $100 per share</div>
              <ul className="strategy-benefits">
                <li>Operations underway</li>
                <li>Proven production model</li>
                <li>Balanced risk-reward</li>
                <li>Strong growth potential</li>
              </ul>
              <div className="strategy-risk">
                <strong>Risk Level:</strong> Moderate (operational)
              </div>
            </div>

            <div className="strategy-card">
              <div className="strategy-badge stable">Stable Returns</div>
              <h3>Phase 11-19</h3>
              <div className="strategy-price">$200 - $1,000 per share</div>
              <ul className="strategy-benefits">
                <li>Established operations</li>
                <li>Multi-country presence</li>
                <li>Stable dividend stream</li>
                <li>Lower volatility</li>
              </ul>
              <div className="strategy-risk">
                <strong>Risk Level:</strong> Lower (mature operations)
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Next Steps */}
      <section className="next-steps">
        <div className="container">
          <div className="section-header">
            <h2>Ready to Invest?</h2>
            <p>Explore our tools and resources to make informed decisions</p>
          </div>

          <div className="next-steps-grid">
            <button 
              className="next-step-card"
              onClick={() => onNavigate('calculator')}
            >
              <div className="step-icon">🧮</div>
              <h3>Calculate Returns</h3>
              <p>Use our comprehensive calculator to project your potential dividends</p>
            </button>

            <button 
              className="next-step-card"
              onClick={() => onNavigate('financial-data')}
            >
              <div className="step-icon">📊</div>
              <h3>Review Financial Data</h3>
              <p>Examine detailed projections and yield tables</p>
            </button>

            <button 
              className="next-step-card"
              onClick={() => alert('Coming Soon - Registration')}
            >
              <div className="step-icon">📝</div>
              <h3>Start Registration</h3>
              <p>Begin your shareholder registration process</p>
            </button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default InvestmentPhasesPage;
