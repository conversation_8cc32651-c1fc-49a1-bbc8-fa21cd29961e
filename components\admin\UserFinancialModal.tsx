import React from 'react'

interface SharePurchase {
  id: string
  package_name: string
  shares_purchased: number
  total_amount: number
  commission_used: number
  remaining_payment: number
  payment_method: string
  status: string
  created_at: string
  updated_at: string
}

interface PaymentTransaction {
  id: string
  amount: number
  currency: string
  network: string
  status: string
  created_at: string
  payment_method?: string
  sender_wallet?: string
  receiver_wallet?: string
  transaction_hash?: string
  screenshot_url?: string
  admin_notes?: string
}

interface CommissionBalance {
  usdt_balance: number
  share_balance: number
  total_earned_usdt: number
  total_earned_shares: number
  total_withdrawn_usdt: number
}

interface ReferralData {
  id: string
  referral_code: string
  commission_rate: number
  total_commission: number
  status: string
  created_at: string
  referrer?: {
    id: number
    username: string
    full_name: string | null
  }
  referred?: {
    id: number
    username: string
    full_name: string | null
  }
}

interface KYCInformation {
  id: string
  first_name: string
  last_name: string
  full_legal_name: string
  id_type: string
  phone_number: string
  email_address: string
  street_address: string
  city: string
  postal_code: string
  country_code: string
  country_name: string
  data_consent_given: boolean
  privacy_policy_accepted: boolean
  kyc_completed_at: string
  kyc_status: string
  certificate_requested: boolean
  certificate_generated_at: string | null
  certificate_sent_at: string | null
  created_at: string
}

interface UserFinancialModalProps {
  isOpen: boolean
  onClose: () => void
  user: {
    id: number
    username: string
    full_name: string | null
    email: string
    share_purchases?: SharePurchase[]
    payment_transactions?: PaymentTransaction[]
    commission_balances?: CommissionBalance
    referrals_made?: ReferralData[]
    referred_by?: ReferralData
    kyc_information?: KYCInformation
    total_shares?: number
    total_invested?: number
    total_commissions?: number
    total_referrals?: number
  }
}

export const UserFinancialModal: React.FC<UserFinancialModalProps> = ({
  isOpen,
  onClose,
  user
}) => {
  if (!isOpen) return null

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
      case 'confirmed':
      case 'approved':
        return 'bg-green-500/20 text-green-400'
      case 'pending':
      case 'pending_payment':
      case 'pending_approval':
        return 'bg-yellow-500/20 text-yellow-400'
      case 'rejected':
      case 'cancelled':
        return 'bg-red-500/20 text-red-400'
      default:
        return 'bg-gray-500/20 text-gray-400'
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-700">
          <div>
            <h2 className="text-2xl font-bold text-white">
              💰 Financial Overview - {user.full_name || user.username}
            </h2>
            <p className="text-gray-400 mt-1">@{user.username} (ID: {user.id})</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white text-2xl"
          >
            ×
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="glass-card p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Total Shares</p>
                  <p className="text-2xl font-bold text-yellow-400">
                    {(user.total_shares || 0).toLocaleString()}
                  </p>
                  <p className="text-xs text-gray-500">
                    {user.share_purchases?.length || 0} purchases
                  </p>
                </div>
                <div className="text-3xl">📈</div>
              </div>
            </div>

            <div className="glass-card p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Total Invested</p>
                  <p className="text-2xl font-bold text-green-400">
                    ${(user.total_invested || 0).toLocaleString()}
                  </p>
                  <p className="text-xs text-gray-500">
                    {user.payment_transactions?.length || 0} payments
                  </p>
                </div>
                <div className="text-3xl">💰</div>
              </div>
            </div>

            <div className="glass-card p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Total Commissions</p>
                  <p className="text-2xl font-bold text-blue-400">
                    ${(user.total_commissions || 0).toLocaleString()}
                  </p>
                  <p className="text-xs text-gray-500">
                    Available: ${user.commission_balances?.usdt_balance || 0}
                  </p>
                </div>
                <div className="text-3xl">🤝</div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Share Purchases */}
            <div className="glass-card p-4">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                📈 Share Purchases ({user.share_purchases?.length || 0})
              </h3>
              <div className="space-y-3 max-h-64 overflow-y-auto">
                {user.share_purchases && user.share_purchases.length > 0 ? (
                  user.share_purchases.map((purchase) => (
                    <div key={purchase.id} className="bg-gray-800/50 p-3 rounded-lg">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <p className="font-medium text-white">{purchase.package_name}</p>
                          <p className="text-sm text-gray-400">
                            {purchase.shares_purchased.toLocaleString()} shares
                          </p>
                        </div>
                        <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(purchase.status)}`}>
                          {purchase.status}
                        </span>
                      </div>
                      <div className="grid grid-cols-2 gap-2 text-xs">
                        <div>
                          <span className="text-gray-400">Amount: </span>
                          <span className="text-green-400">${purchase.total_amount}</span>
                        </div>
                        <div>
                          <span className="text-gray-400">Method: </span>
                          <span className="text-gray-300">{purchase.payment_method}</span>
                        </div>
                        <div>
                          <span className="text-gray-400">Commission Used: </span>
                          <span className="text-blue-400">${purchase.commission_used}</span>
                        </div>
                        <div>
                          <span className="text-gray-400">Date: </span>
                          <span className="text-gray-300">
                            {new Date(purchase.created_at).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-500 text-center py-4">No share purchases found</p>
                )}
              </div>
            </div>

            {/* Payment Transactions */}
            <div className="glass-card p-4">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                💳 Payment Transactions ({user.payment_transactions?.length || 0})
              </h3>
              <div className="space-y-3 max-h-64 overflow-y-auto">
                {user.payment_transactions && user.payment_transactions.length > 0 ? (
                  user.payment_transactions.map((payment) => (
                    <div key={payment.id} className="bg-gray-800/50 p-3 rounded-lg">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <p className="font-medium text-white">
                            ${payment.amount} {payment.currency}
                          </p>
                          <p className="text-sm text-gray-400">{payment.network}</p>
                        </div>
                        <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(payment.status)}`}>
                          {payment.status}
                        </span>
                      </div>
                      <div className="text-xs space-y-1">
                        {payment.transaction_hash && (
                          <div>
                            <span className="text-gray-400">Hash: </span>
                            <span className="text-blue-400 font-mono">
                              {payment.transaction_hash.substring(0, 20)}...
                            </span>
                          </div>
                        )}
                        <div>
                          <span className="text-gray-400">Date: </span>
                          <span className="text-gray-300">
                            {new Date(payment.created_at).toLocaleDateString()}
                          </span>
                        </div>
                        {payment.admin_notes && (
                          <div>
                            <span className="text-gray-400">Notes: </span>
                            <span className="text-gray-300">{payment.admin_notes}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-500 text-center py-4">No payment transactions found</p>
                )}
              </div>
            </div>

            {/* Referral Network */}
            <div className="glass-card p-4">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                👥 Referral Network ({user.total_referrals || 0})
              </h3>

              {/* Who referred this user */}
              {user.referred_by && (
                <div className="mb-4 p-3 bg-purple-900/20 rounded-lg border border-purple-500/20">
                  <h4 className="text-sm font-medium text-purple-400 mb-2">Referred By</h4>
                  <div className="text-sm">
                    <p className="text-white font-medium">
                      {user.referred_by.referrer?.full_name || user.referred_by.referrer?.username}
                    </p>
                    <p className="text-gray-400 text-xs">
                      Code: {user.referred_by.referral_code}
                    </p>
                    <p className="text-gray-400 text-xs">
                      Rate: {user.referred_by.commission_rate}%
                    </p>
                    <p className="text-gray-400 text-xs">
                      Date: {new Date(user.referred_by.created_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              )}

              {/* Users referred by this user */}
              <div className="space-y-3 max-h-48 overflow-y-auto">
                <h4 className="text-sm font-medium text-purple-400">Users Referred</h4>
                {user.referrals_made && user.referrals_made.length > 0 ? (
                  user.referrals_made.map((referral) => (
                    <div key={referral.id} className="bg-gray-800/50 p-3 rounded-lg">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <p className="font-medium text-white">
                            {referral.referred?.full_name || referral.referred?.username}
                          </p>
                          <p className="text-sm text-gray-400">Code: {referral.referral_code}</p>
                        </div>
                        <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                          referral.status === 'active'
                            ? 'bg-green-500/20 text-green-400'
                            : 'bg-gray-500/20 text-gray-400'
                        }`}>
                          {referral.status}
                        </span>
                      </div>
                      <div className="grid grid-cols-2 gap-2 text-xs">
                        <div>
                          <span className="text-gray-400">Rate: </span>
                          <span className="text-purple-400">{referral.commission_rate}%</span>
                        </div>
                        <div>
                          <span className="text-gray-400">Commission: </span>
                          <span className="text-green-400">${referral.total_commission}</span>
                        </div>
                        <div className="col-span-2">
                          <span className="text-gray-400">Date: </span>
                          <span className="text-gray-300">
                            {new Date(referral.created_at).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-500 text-center py-4">No referrals made</p>
                )}
              </div>
            </div>
          </div>

          {/* Commission Details */}
          {user.commission_balances && (
            <div className="glass-card p-4 mt-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                🤝 Commission Details
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div className="text-center">
                  <p className="text-xs text-gray-400 uppercase tracking-wider">USDT Balance</p>
                  <p className="text-lg font-bold text-blue-400">
                    ${user.commission_balances.usdt_balance.toLocaleString()}
                  </p>
                </div>
                <div className="text-center">
                  <p className="text-xs text-gray-400 uppercase tracking-wider">Share Balance</p>
                  <p className="text-lg font-bold text-yellow-400">
                    {user.commission_balances.share_balance.toLocaleString()}
                  </p>
                </div>
                <div className="text-center">
                  <p className="text-xs text-gray-400 uppercase tracking-wider">Total Earned USDT</p>
                  <p className="text-lg font-bold text-green-400">
                    ${user.commission_balances.total_earned_usdt.toLocaleString()}
                  </p>
                </div>
                <div className="text-center">
                  <p className="text-xs text-gray-400 uppercase tracking-wider">Total Earned Shares</p>
                  <p className="text-lg font-bold text-yellow-400">
                    {user.commission_balances.total_earned_shares.toLocaleString()}
                  </p>
                </div>
                <div className="text-center">
                  <p className="text-xs text-gray-400 uppercase tracking-wider">Total Withdrawn</p>
                  <p className="text-lg font-bold text-red-400">
                    ${user.commission_balances.total_withdrawn_usdt.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* KYC Information */}
          {user.kyc_information && (
            <div className="glass-card p-4 mt-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                🆔 KYC Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-sm font-medium text-orange-400 mb-3">Personal Details</h4>
                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="text-gray-400">Legal Name: </span>
                      <span className="text-white">{user.kyc_information.full_legal_name}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">ID Type: </span>
                      <span className="text-gray-300">
                        {user.kyc_information.id_type === 'national_id' ? 'National ID' : 'Passport'}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-400">Phone: </span>
                      <span className="text-gray-300">{user.kyc_information.phone_number}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Email: </span>
                      <span className="text-gray-300">{user.kyc_information.email_address}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-orange-400 mb-3">Address & Status</h4>
                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="text-gray-400">Address: </span>
                      <span className="text-gray-300">
                        {user.kyc_information.street_address}, {user.kyc_information.city}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-400">Postal Code: </span>
                      <span className="text-gray-300">{user.kyc_information.postal_code}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Country: </span>
                      <span className="text-gray-300">{user.kyc_information.country_name}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Status: </span>
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                        user.kyc_information.kyc_status === 'completed'
                          ? 'bg-green-500/20 text-green-400'
                          : user.kyc_information.kyc_status === 'pending'
                          ? 'bg-yellow-500/20 text-yellow-400'
                          : 'bg-red-500/20 text-red-400'
                      }`}>
                        {user.kyc_information.kyc_status}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-400">Completed: </span>
                      <span className="text-gray-300">
                        {new Date(user.kyc_information.kyc_completed_at).toLocaleDateString()}
                      </span>
                    </div>
                    {user.kyc_information.certificate_requested && (
                      <div>
                        <span className="text-gray-400">Certificate: </span>
                        <span className="text-green-400">Requested</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
