import { supabase, getServiceRoleClient } from './supabase'

export interface NotificationData {
  user_id: number
  type: 'payment_approved' | 'payment_rejected' | 'commission_earned' | 'system' | 'referral' | 'withdrawal' | 'share_purchase' | 'phase_change' | 'account_update' | 'security_alert' | 'info' | 'success' | 'warning' | 'error' | 'payment' | 'commission'
  title: string
  message: string
  metadata?: Record<string, any>
  action_url?: string
}

export interface NotificationTemplate {
  id: string
  template_key: string
  notification_type: string
  title_template: string
  message_template: string
  variables: string[]
  is_active: boolean
}

export interface UserNotification {
  id: string
  user_id: number
  type: string
  title: string
  message: string
  metadata: Record<string, any>
  is_read: boolean
  is_archived: boolean
  action_url?: string
  created_at: string
  updated_at: string
}

class NotificationService {
  /**
   * Create a new notification for a user
   */
  async createNotification(notificationData: NotificationData): Promise<UserNotification | null> {
    try {
      const serviceClient = getServiceRoleClient()
      const { data, error } = await serviceClient
        .from('user_notifications')
        .insert({
          user_id: notificationData.user_id,
          type: notificationData.type,
          title: notificationData.title,
          message: notificationData.message,
          metadata: notificationData.metadata || {},
          action_url: notificationData.action_url
        })
        .select()
        .single()

      if (error) {
        console.error('Error creating notification:', error)
        return null
      }

      console.log(`✅ Notification created for user ${notificationData.user_id}:`, data.title)
      return data
    } catch (error) {
      console.error('Error in createNotification:', error)
      return null
    }
  }

  /**
   * Create notification from template with variable substitution
   */
  async createNotificationFromTemplate(
    templateKey: string,
    userId: number,
    variables: Record<string, any>,
    options?: {
      priority?: 'low' | 'normal' | 'high' | 'urgent'
      payment_id?: string
      commission_id?: string
      referral_id?: string
    }
  ): Promise<UserNotification | null> {
    try {
      // Get the template
      const { data: template, error: templateError } = await supabase
        .from('notification_templates')
        .select('*')
        .eq('template_key', templateKey)
        .eq('is_active', true)
        .single()

      if (templateError || !template) {
        console.error(`Template not found: ${templateKey}`, templateError)
        return null
      }

      // Substitute variables in title and message
      const title = this.substituteVariables(template.title_template, variables)
      const message = this.substituteVariables(template.message_template, variables)

      // Create the notification
      return await this.createNotification({
        user_id: userId,
        type: template.notification_type as any,
        title,
        message,
        metadata: { template_key: templateKey, variables, ...variables }
      })
    } catch (error) {
      console.error('Error creating notification from template:', error)
      return null
    }
  }

  /**
   * Send payment approval notification
   */
  async sendPaymentApprovalNotification(
    userId: number,
    paymentData: {
      payment_id: string
      amount: number
      shares: number
      price_per_share: number
      network: string
      processed_date: string
    }
  ): Promise<UserNotification | null> {
    return await this.createNotificationFromTemplate(
      'payment_approved',
      userId,
      {
        amount: paymentData.amount.toFixed(2),
        shares: paymentData.shares.toLocaleString(),
        price_per_share: paymentData.price_per_share.toFixed(2),
        payment_id: paymentData.payment_id,
        processed_date: new Date(paymentData.processed_date).toLocaleString()
      },
      {
        priority: 'high',
        payment_id: paymentData.payment_id
      }
    )
  }

  /**
   * Send payment rejection notification
   */
  async sendPaymentRejectionNotification(
    userId: number,
    paymentData: {
      payment_id: string
      amount: number
      network: string
      tx_hash: string
      rejection_reason: string
      rejected_date: string
    }
  ): Promise<UserNotification | null> {
    return await this.createNotificationFromTemplate(
      'payment_rejected',
      userId,
      {
        amount: paymentData.amount.toFixed(2),
        network: paymentData.network,
        tx_hash: paymentData.tx_hash || 'Not provided',
        rejection_reason: paymentData.rejection_reason,
        payment_id: paymentData.payment_id,
        rejected_date: new Date(paymentData.rejected_date).toLocaleString()
      },
      {
        priority: 'high',
        payment_id: paymentData.payment_id
      }
    )
  }

  /**
   * Send commission earned notification
   */
  async sendCommissionEarnedNotification(
    referrerId: number,
    commissionData: {
      commission_id: string
      referred_username: string
      investment_amount: number
      shares_purchased: number
      usdt_commission: number
      share_commission: number
      total_usdt_balance: number
      total_share_balance: number
      earned_date: string
    }
  ): Promise<UserNotification | null> {
    const totalCommission = commissionData.usdt_commission + (commissionData.share_commission * 5) // Assuming $5 per share value

    return await this.createNotificationFromTemplate(
      'commission_earned',
      referrerId,
      {
        total_commission: totalCommission.toFixed(2),
        share_commission: commissionData.share_commission.toFixed(2),
        referred_username: commissionData.referred_username,
        investment_amount: commissionData.investment_amount.toFixed(2),
        shares_purchased: commissionData.shares_purchased.toLocaleString(),
        usdt_commission: commissionData.usdt_commission.toFixed(2),
        total_usdt_balance: commissionData.total_usdt_balance.toFixed(2),
        total_share_balance: commissionData.total_share_balance.toFixed(2),
        commission_id: commissionData.commission_id,
        earned_date: new Date(commissionData.earned_date).toLocaleString()
      },
      {
        priority: 'normal',
        commission_id: commissionData.commission_id
      }
    )
  }

  /**
   * Get user notifications with pagination and filtering
   */
  async getUserNotifications(
    userId: number,
    options?: {
      limit?: number
      offset?: number
      type?: string
      unread_only?: boolean
      include_archived?: boolean
    }
  ): Promise<{ notifications: UserNotification[], total_count: number }> {
    try {
      const serviceClient = getServiceRoleClient()
      let query = serviceClient
        .from('user_notifications')
        .select('*', { count: 'exact' })
        .eq('user_id', userId)

      // Apply filters
      if (options?.type) {
        query = query.eq('type', options.type)
      }

      if (options?.unread_only) {
        query = query.eq('is_read', false)
      }

      if (!options?.include_archived) {
        query = query.eq('is_archived', false)
      }

      // Apply pagination
      if (options?.limit) {
        query = query.limit(options.limit)
      }

      if (options?.offset) {
        query = query.range(options.offset, (options.offset + (options.limit || 10)) - 1)
      }

      // Order by creation date (newest first)
      query = query.order('created_at', { ascending: false })

      const { data, error, count } = await query

      if (error) {
        console.error('Error fetching user notifications:', error)
        return { notifications: [], total_count: 0 }
      }

      return {
        notifications: data || [],
        total_count: count || 0
      }
    } catch (error) {
      console.error('Error in getUserNotifications:', error)
      return { notifications: [], total_count: 0 }
    }
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId: string, userId: number): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_notifications')
        .update({
          is_read: true,
          read_at: new Date().toISOString()
        })
        .eq('id', notificationId)
        .eq('user_id', userId) // Security: ensure user can only mark their own notifications

      if (error) {
        console.error('Error marking notification as read:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Error in markAsRead:', error)
      return false
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  async markAllAsRead(userId: number): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_notifications')
        .update({
          is_read: true,
          read_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .eq('is_read', false)

      if (error) {
        console.error('Error marking all notifications as read:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Error in markAllAsRead:', error)
      return false
    }
  }

  /**
   * Archive notification
   */
  async archiveNotification(notificationId: string, userId: number): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_notifications')
        .update({
          is_archived: true,
          archived_at: new Date().toISOString()
        })
        .eq('id', notificationId)
        .eq('user_id', userId)

      if (error) {
        console.error('Error archiving notification:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Error in archiveNotification:', error)
      return false
    }
  }

  /**
   * Get notification statistics for a user
   */
  async getNotificationStats(userId: number): Promise<{
    total: number
    unread: number
    by_type: Record<string, number>
  }> {
    try {
      const serviceClient = getServiceRoleClient()
      const { data, error } = await serviceClient
        .from('user_notification_summary')
        .select('*')
        .eq('user_id', userId)
        .single()

      if (error || !data) {
        return { total: 0, unread: 0, by_type: {} }
      }

      return {
        total: data.total_notifications || 0,
        unread: data.unread_count || 0,
        by_type: {
          payment_approved: data.payment_approved_count || 0,
          payment_rejected: data.payment_rejected_count || 0,
          commission_earned: data.commission_count || 0
        }
      }
    } catch (error) {
      console.error('Error getting notification stats:', error)
      return { total: 0, unread: 0, by_type: {} }
    }
  }

  /**
   * Private method to substitute variables in templates
   */
  private substituteVariables(template: string, variables: Record<string, any>): string {
    let result = template

    // Replace {{variable}} patterns
    Object.keys(variables).forEach(key => {
      const regex = new RegExp(`{{${key}}}`, 'g')
      result = result.replace(regex, String(variables[key] || ''))
    })

    return result
  }
}

// Export singleton instance
export const notificationService = new NotificationService()
export default notificationService
