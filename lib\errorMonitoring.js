/**
 * COMPREHENSIVE ERROR MONITORING AND FIXING SYSTEM
 * 
 * This module provides real-time error detection, suppression, and automatic fixing
 * for common issues in the Aureus Africa application.
 */

class ErrorMonitor {
  constructor() {
    this.errorCounts = new Map();
    this.suppressedErrors = new Set();
    this.fixedErrors = new Map();
    this.isInitialized = false;
    
    // Error patterns to suppress
    this.suppressPatterns = [
      /attribute d: Expected number/i,
      /tc0\.2,0,0\.4-0\.2,0/,
      /malformed path data/i,
      /invalid path command/i,
      /SVG.*path.*error/i
    ];
    
    // CSP patterns to monitor (but not suppress)
    this.cspPatterns = [
      /Content Security Policy/i,
      /refused to execute/i,
      /refused to load/i,
      /refused to apply/i,
      /default-src/i,
      /style-src/i,
      /script-src/i
    ];
  }

  /**
   * Initialize the error monitoring system
   */
  initialize() {
    if (this.isInitialized) return;
    
    console.log('🛡️ Initializing comprehensive error monitoring...');
    
    // Global error handler
    window.addEventListener('error', (event) => this.handleError(event), true);
    
    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => this.handlePromiseRejection(event));
    
    // Console error interceptor
    this.interceptConsoleErrors();
    
    // SVG path interceptor
    this.setupSVGPathInterceptor();
    
    // jQuery interceptor (if available)
    this.setupJQueryInterceptor();
    
    // Periodic health check
    this.startHealthCheck();
    
    this.isInitialized = true;
    console.log('✅ Error monitoring system initialized');
  }

  /**
   * Handle JavaScript errors
   */
  handleError(event) {
    const message = (event.error && event.error.message) || event.message || '';
    const filename = event.filename || '';
    const source = event.source || '';
    
    // Check if this is a suppressible error
    const shouldSuppress = this.suppressPatterns.some(pattern => pattern.test(message));
    
    if (shouldSuppress) {
      this.recordSuppressedError(message);
      event.preventDefault();
      event.stopPropagation();
      if (typeof event.stopImmediatePropagation === 'function') {
        event.stopImmediatePropagation();
      }
      return true;
    }
    
    // Check for CSP errors
    const isCSPError = this.cspPatterns.some(pattern => pattern.test(message));
    if (isCSPError) {
      this.recordCSPError(message);
      // Don't suppress CSP errors, just log them
    }
    
    // Record other errors for monitoring
    this.recordError(message, 'javascript');
  }

  /**
   * Handle unhandled promise rejections
   */
  handlePromiseRejection(event) {
    const reason = event.reason || '';
    const message = typeof reason === 'string' ? reason : reason.message || 'Unknown promise rejection';
    
    console.warn('🚨 Unhandled promise rejection:', message);
    this.recordError(message, 'promise');
  }

  /**
   * Intercept console errors
   */
  interceptConsoleErrors() {
    const originalError = console.error;
    console.error = (...args) => {
      const message = args.join(' ');
      
      // Check if this is a suppressible error
      const shouldSuppress = this.suppressPatterns.some(pattern => pattern.test(message));
      
      if (!shouldSuppress) {
        originalError.apply(console, args);
        this.recordError(message, 'console');
      } else {
        console.log('🔧 Suppressed console error:', message);
        this.recordSuppressedError(message);
      }
    };
  }

  /**
   * Setup SVG path interceptor
   */
  setupSVGPathInterceptor() {
    const originalSetAttribute = Element.prototype.setAttribute;
    Element.prototype.setAttribute = function(name, value) {
      if (name === 'd' && this.tagName.toLowerCase() === 'path') {
        try {
          if (typeof value === 'string') {
            const fixedValue = window.errorMonitor.fixSVGPath(value);
            if (fixedValue !== value) {
              window.errorMonitor.recordFixedError('SVG path fixed', value, fixedValue);
            }
            return originalSetAttribute.call(this, name, fixedValue);
          }
        } catch (error) {
          console.log('🔧 SVG path fix failed, using fallback');
          return originalSetAttribute.call(this, name, 'M 0 0 L 10 10');
        }
      }
      return originalSetAttribute.call(this, name, value);
    };
  }

  /**
   * Setup jQuery interceptor
   */
  setupJQueryInterceptor() {
    const checkForJQuery = () => {
      if (typeof $ !== 'undefined' && $.fn && $.fn.attr) {
        const originalAttr = $.fn.attr;
        $.fn.attr = function(name, value) {
          if (name === 'd' && this.is('path') && typeof value === 'string') {
            const fixedValue = window.errorMonitor.fixSVGPath(value);
            if (fixedValue !== value) {
              window.errorMonitor.recordFixedError('jQuery SVG path fixed', value, fixedValue);
            }
            return originalAttr.call(this, name, fixedValue);
          }
          return originalAttr.apply(this, arguments);
        };
        
        // Override jQuery error handling
        const originalError = $.error || function() {};
        $.error = function(msg) {
          const shouldSuppress = window.errorMonitor.suppressPatterns.some(pattern => pattern.test(String(msg)));
          if (shouldSuppress) {
            console.log('🔧 Suppressed jQuery error:', msg);
            window.errorMonitor.recordSuppressedError(String(msg));
            return;
          }
          return originalError.apply(this, arguments);
        };
        
        console.log('✅ jQuery interceptor installed');
      } else {
        // Check again in 100ms
        setTimeout(checkForJQuery, 100);
      }
    };
    
    checkForJQuery();
  }

  /**
   * Fix SVG path data
   */
  fixSVGPath(pathData) {
    if (!pathData || typeof pathData !== 'string') {
      return 'M 0 0 L 10 10';
    }

    try {
      // Check for problematic patterns
      if (pathData.includes('tc0.2,0,0.4-0.2,0') || pathData.includes('tc') || pathData.includes('               tc')) {
        let cleanPath = pathData
          // Remove all 'tc' commands and surrounding whitespace
          .replace(/\s*tc[\d\.\-,\s]*\s*/g, ' ')
          // Remove any remaining invalid characters
          .replace(/[^\d\.\-\s,MmLlHhVvCcSsQqTtAaZz]/g, '')
          // Fix spacing around commands
          .replace(/([MmLlHhVvCcSsQqTtAaZz])(\d)/g, '$1 $2')
          .replace(/(\d)([MmLlHhVvCcSsQqTtAaZz])/g, '$1 $2')
          // Normalize whitespace
          .replace(/\s+/g, ' ')
          .trim();

        // Ensure valid path structure
        if (!cleanPath.match(/^[Mm]/) || cleanPath.length < 5) {
          cleanPath = 'M 0 0 L 10 10';
        }

        return cleanPath;
      }

      return pathData;
    } catch (error) {
      console.log('🔧 SVG path fix failed:', error.message);
      return 'M 0 0 L 10 10';
    }
  }

  /**
   * Record suppressed error
   */
  recordSuppressedError(message) {
    this.suppressedErrors.add(message);
    this.incrementErrorCount('suppressed');
  }

  /**
   * Record CSP error
   */
  recordCSPError(message) {
    console.warn('⚠️ CSP Issue detected:', message);
    this.incrementErrorCount('csp');
  }

  /**
   * Record general error
   */
  recordError(message, type) {
    this.incrementErrorCount(type);
  }

  /**
   * Record fixed error
   */
  recordFixedError(type, original, fixed) {
    if (!this.fixedErrors.has(type)) {
      this.fixedErrors.set(type, []);
    }
    this.fixedErrors.get(type).push({ original, fixed, timestamp: Date.now() });
    this.incrementErrorCount('fixed');
  }

  /**
   * Increment error count
   */
  incrementErrorCount(type) {
    this.errorCounts.set(type, (this.errorCounts.get(type) || 0) + 1);
  }

  /**
   * Start periodic health check
   */
  startHealthCheck() {
    setInterval(() => {
      this.performHealthCheck();
    }, 30000); // Every 30 seconds
  }

  /**
   * Perform health check
   */
  performHealthCheck() {
    const stats = this.getErrorStats();
    
    if (stats.total > 0) {
      console.log('📊 Error Monitor Stats:', stats);
    }
    
    // Auto-fix any SVG paths that might have been added dynamically
    this.autoFixSVGPaths();
  }

  /**
   * Auto-fix SVG paths on the page
   */
  autoFixSVGPaths() {
    try {
      const svgPaths = document.querySelectorAll('svg path');
      let fixedCount = 0;
      
      svgPaths.forEach(path => {
        const currentPath = path.getAttribute('d');
        if (currentPath) {
          const fixedPath = this.fixSVGPath(currentPath);
          if (fixedPath !== currentPath) {
            path.setAttribute('d', fixedPath);
            fixedCount++;
          }
        }
      });
      
      if (fixedCount > 0) {
        console.log(`🔧 Auto-fixed ${fixedCount} SVG paths during health check`);
        this.recordFixedError('auto-fix', `${fixedCount} paths`, 'fixed');
      }
    } catch (error) {
      console.error('❌ Auto-fix SVG failed:', error);
    }
  }

  /**
   * Get error statistics
   */
  getErrorStats() {
    const stats = {};
    let total = 0;
    
    for (const [type, count] of this.errorCounts) {
      stats[type] = count;
      total += count;
    }
    
    stats.total = total;
    stats.suppressedCount = this.suppressedErrors.size;
    stats.fixedTypes = this.fixedErrors.size;
    
    return stats;
  }

  /**
   * Get detailed error report
   */
  getErrorReport() {
    return {
      stats: this.getErrorStats(),
      suppressedErrors: Array.from(this.suppressedErrors),
      fixedErrors: Object.fromEntries(this.fixedErrors),
      timestamp: new Date().toISOString()
    };
  }
}

// Create global instance
window.errorMonitor = new ErrorMonitor();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => window.errorMonitor.initialize());
} else {
  window.errorMonitor.initialize();
}

export default window.errorMonitor;
