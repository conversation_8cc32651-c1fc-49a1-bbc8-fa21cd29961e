import React, { useState, useEffect, useCallback } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';
import CertificateGenerator, { GenerationOptions, GenerationResult } from '../../lib/certificateGenerator';
import { CertificateData } from '../../lib/certificateTemplate';
import { PROFESSIONAL_CERTIFICATE_TEMPLATE } from '../../lib/professionalCertificateTemplate';
import { ProfessionalCertificatePreview } from '../certificate/ProfessionalCertificatePreview';
import JSZip from 'jszip';

interface SharePurchase {
  id: string;
  user_id: number;
  shares_purchased: number;
  total_amount: number;
  package_name: string;
  status: string;
  created_at: string;
  user: {
    id: number;
    full_name: string;
    email: string;
    phone: string;
    country_of_residence: string;
  };
  kyc_info?: {
    kyc_status: string;
    kyc_completed_at: string;
  };
  existing_certificate?: {
    id: string;
    certificate_number: string;
    status: string;
  };
}

interface BatchProgress {
  total: number;
  completed: number;
  failed: number;
  current: string;
  errors: string[];
}

export const AdminCertificateCreator: React.FC = () => {
  const [purchases, setPurchases] = useState<SharePurchase[]>([]);
  const [selectedPurchases, setSelectedPurchases] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [batchProgress, setBatchProgress] = useState<BatchProgress | null>(null);
  const [previewData, setPreviewData] = useState<CertificateData | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [generationOptions, setGenerationOptions] = useState<GenerationOptions>({
    format: 'pdf',
    quality: 'high',
    includeMetadata: true,
    watermark: true,
    compression: true
  });
  const [filter, setFilter] = useState<'all' | 'pending' | 'approved' | 'has_certificate'>('pending');
  const [searchTerm, setSearchTerm] = useState('');

  const generator = new CertificateGenerator(); // Use default template for testing

  useEffect(() => {
    loadPurchases();

    // Add timeout to prevent infinite loading
    const timeout = setTimeout(() => {
      if (loading) {
        console.warn('Certificate generator loading timeout');
        setLoading(false);
      }
    }, 10000); // 10 second timeout

    return () => clearTimeout(timeout);
  }, [filter]);

  const loadPurchases = async () => {
    setLoading(true);
    try {
      console.log('🔄 Loading purchases with filter:', filter);

      // Use service role client for admin operations
      const adminClient = getServiceRoleClient();

      // First get all purchases
      let query = adminClient
        .from('aureus_share_purchases')
        .select('*')
        .order('created_at', { ascending: false });

      // Apply status filter
      if (filter === 'pending' || filter === 'approved') {
        query = query.eq('status', 'active');
        console.log('📊 Filtering for active status');
      }

      const { data: purchasesData, error } = await query;

      if (error) {
        console.error('❌ Error loading purchases:', error);
        setPurchases([]);
        setLoading(false);
        return;
      }

      console.log('✅ Raw purchases data:', purchasesData?.length || 0, 'records');

      if (!purchasesData || purchasesData.length === 0) {
        console.log('⚠️ No purchases found');
        setPurchases([]);
        setLoading(false);
        return;
      }

      // Get all certificates to check which purchases already have them
      const { data: certificatesData, error: certError } = await adminClient
        .from('certificates')
        .select('purchase_id, id, certificate_number, status');

      if (certError) {
        console.log('⚠️ Error loading certificates (table may not exist):', certError);
      } else {
        console.log('📜 Found', certificatesData?.length || 0, 'existing certificates');
      }

      const certificateMap = new Map(
        certificatesData?.map(cert => [cert.purchase_id, cert]) || []
      );

      // Load user and KYC data for each purchase
      const purchasesWithData = await Promise.all(
        purchasesData.map(async (purchase) => {
          // Get user data
          const { data: userData } = await adminClient
            .from('users')
            .select('id, full_name, email, phone, country_of_residence')
            .eq('id', purchase.user_id)
            .single();

          // Get KYC data
          const { data: kycData } = await adminClient
            .from('kyc_information')
            .select('kyc_status, kyc_completed_at')
            .eq('user_id', purchase.user_id)
            .single();

          // Get existing certificate
          const existingCertificate = certificateMap.get(purchase.id);

          return {
            ...purchase,
            user: userData || { id: purchase.user_id, full_name: 'Unknown', email: 'Unknown' },
            kyc_info: kycData,
            existing_certificate: existingCertificate
          };
        })
      );

      console.log('👥 Purchases with user data:', purchasesWithData.length);

      // Apply certificate-based filters
      let filteredPurchases = purchasesWithData;
      switch (filter) {
        case 'pending':
          filteredPurchases = purchasesWithData.filter(p => !p.existing_certificate);
          console.log('🔍 Filtered to pending certificates:', filteredPurchases.length);
          break;
        case 'has_certificate':
          filteredPurchases = purchasesWithData.filter(p => p.existing_certificate);
          console.log('🔍 Filtered to has certificates:', filteredPurchases.length);
          break;
        default:
          console.log('🔍 No certificate filter applied:', filteredPurchases.length);
      }

      console.log('✅ Final filtered purchases:', filteredPurchases.length);
      setPurchases(filteredPurchases);
    } catch (error) {
      console.error('Error loading purchases:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredPurchases = purchases.filter(purchase => {
    if (!searchTerm) return true;
    const searchLower = searchTerm.toLowerCase();
    return (
      purchase.user.full_name.toLowerCase().includes(searchLower) ||
      purchase.user.email.toLowerCase().includes(searchLower) ||
      purchase.id.toLowerCase().includes(searchLower)
    );
  });

  const handleSelectPurchase = (purchaseId: string) => {
    const newSelected = new Set(selectedPurchases);
    if (newSelected.has(purchaseId)) {
      newSelected.delete(purchaseId);
    } else {
      newSelected.add(purchaseId);
    }
    setSelectedPurchases(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedPurchases.size === filteredPurchases.length) {
      setSelectedPurchases(new Set());
    } else {
      setSelectedPurchases(new Set(filteredPurchases.map(p => p.id)));
    }
  };

  const prepareCertificateData = (purchase: SharePurchase): CertificateData => {
    const certificateNumber = purchase.existing_certificate?.certificate_number ||
      `AUR-${new Date().getFullYear()}-${String(Math.floor(Math.random() * 999999)).padStart(6, '0')}`;
    const pricePerShare = purchase.total_amount / purchase.shares_purchased;

    // Format user address
    const userAddress = [
      purchase.user.full_name,
      purchase.user.country_of_residence,
      purchase.user.email
    ].filter(Boolean).join('\n');

    return {
      certificateNumber,
      userFullName: purchase.user.full_name,
      sharesQuantity: purchase.shares_purchased,
      purchaseAmount: purchase.total_amount,
      pricePerShare,
      issueDate: purchase.existing_certificate?.issue_date || new Date().toISOString(),
      kycVerified: purchase.kyc_info?.kyc_status === 'completed',
      userAddress,
      userId: purchase.user.id,
      purchaseId: purchase.id,
      verificationUrl: `${window.location.origin}/verify-certificate/${certificateNumber}?user=${purchase.user.id}`,
      userIdNumber: purchase.user.id_number || 'N/A',
      sunNumber: purchase.user.sun_number || 'N/A'
    };
  };

  const generateSingleCertificate = async (purchase: SharePurchase): Promise<GenerationResult> => {
    const certificateData = prepareCertificateData(purchase);
    return await generator.generateCertificate(certificateData, generationOptions);
  };

  const saveCertificateToDatabase = async (
    purchase: SharePurchase,
    result: GenerationResult,
    fileUrl: string
  ): Promise<boolean> => {
    try {
      // Create certificate record
      const { error: certError } = await supabase
        .from('certificates')
        .insert({
          user_id: purchase.user.id,
          purchase_id: purchase.id,
          certificate_number: result.certificateNumber,
          shares_count: purchase.shares_purchased,
          status: 'issued',
          certificate_data: {
            file_url: fileUrl,
            generation_metadata: result.metadata,
            generation_options: generationOptions,
            generated_at: new Date().toISOString()
          }
        });

      if (certError) {
        console.error('Database save error:', certError);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error saving certificate to database:', error);
      return false;
    }
  };

  const uploadCertificateFile = async (
    certificateNumber: string,
    userId: number,
    buffer: Buffer,
    format: string
  ): Promise<string | null> => {
    try {
      const fileName = `certificate_${certificateNumber}_${userId}.${format}`;
      const { data, error } = await supabase.storage
        .from('proof')
        .upload(fileName, buffer, {
          cacheControl: '3600',
          upsert: true,
          contentType: format === 'pdf' ? 'application/pdf' : 'image/png'
        });

      if (error) {
        console.error('Upload error:', error);
        return null;
      }

      const { data: { publicUrl } } = supabase.storage
        .from('proof')
        .getPublicUrl(fileName);

      return publicUrl;
    } catch (error) {
      console.error('Error uploading certificate:', error);
      return null;
    }
  };

  const handleGenerateSingle = async (purchase: SharePurchase) => {
    setGenerating(true);
    try {
      // Check if certificate already exists for this purchase
      if (purchase.existing_certificate) {
        // Download existing certificate instead of creating new one
        const certificateData = purchase.existing_certificate.certificate_data;
        if (certificateData?.file_url) {
          // Fetch and download the existing certificate
          try {
            const response = await fetch(certificateData.file_url);
            if (response.ok) {
              const blob = await response.blob();
              downloadFile(blob, `${purchase.existing_certificate.certificate_number}.pdf`, 'application/pdf');
              return;
            }
          } catch (fetchError) {
            console.warn('Could not fetch existing certificate, generating new one:', fetchError);
          }
        }
      }

      const result = await generateSingleCertificate(purchase);

      if (result.success && result.pdfBuffer) {
        const fileUrl = await uploadCertificateFile(
          result.certificateNumber,
          purchase.user.id,
          result.pdfBuffer,
          'pdf'
        );

        if (fileUrl) {
          const saved = await saveCertificateToDatabase(purchase, result, fileUrl);
          if (saved) {
            // Download the certificate
            downloadFile(result.pdfBuffer, `${result.certificateNumber}.pdf`, 'application/pdf');
            // Reload purchases to update UI
            await loadPurchases();
          }
        }
      }
    } catch (error) {
      console.error('Error generating certificate:', error);
    } finally {
      setGenerating(false);
    }
  };

  const handleGenerateBatch = async () => {
    const selectedPurchasesList = filteredPurchases.filter(p => selectedPurchases.has(p.id));
    
    if (selectedPurchasesList.length === 0) {
      alert('Please select purchases to generate certificates for.');
      return;
    }

    setGenerating(true);
    setBatchProgress({
      total: selectedPurchasesList.length,
      completed: 0,
      failed: 0,
      current: '',
      errors: []
    });

    const zip = new JSZip();
    const results: GenerationResult[] = [];

    for (let i = 0; i < selectedPurchasesList.length; i++) {
      const purchase = selectedPurchasesList[i];
      
      setBatchProgress(prev => prev ? {
        ...prev,
        current: `Generating certificate for ${purchase.user.full_name}...`
      } : null);

      try {
        const result = await generateSingleCertificate(purchase);
        
        if (result.success && result.pdfBuffer) {
          // Add to ZIP
          zip.file(`${result.certificateNumber}.pdf`, result.pdfBuffer);
          
          // Upload to storage
          const fileUrl = await uploadCertificateFile(
            result.certificateNumber,
            purchase.user.id,
            result.pdfBuffer,
            'pdf'
          );

          if (fileUrl) {
            await saveCertificateToDatabase(purchase, result, fileUrl);
          }

          results.push(result);
          
          setBatchProgress(prev => prev ? {
            ...prev,
            completed: prev.completed + 1
          } : null);
        } else {
          setBatchProgress(prev => prev ? {
            ...prev,
            failed: prev.failed + 1,
            errors: [...prev.errors, `Failed to generate certificate for ${purchase.user.full_name}: ${result.error}`]
          } : null);
        }
      } catch (error) {
        setBatchProgress(prev => prev ? {
          ...prev,
          failed: prev.failed + 1,
          errors: [...prev.errors, `Error generating certificate for ${purchase.user.full_name}: ${error.message}`]
        } : null);
      }
    }

    // Download ZIP file
    if (results.length > 0) {
      setBatchProgress(prev => prev ? {
        ...prev,
        current: 'Creating download package...'
      } : null);

      const zipBlob = await zip.generateAsync({ type: 'blob' });
      downloadFile(zipBlob, `certificates_batch_${new Date().toISOString().split('T')[0]}.zip`, 'application/zip');
    }

    // Reload purchases
    await loadPurchases();
    setSelectedPurchases(new Set());
    setGenerating(false);
    setBatchProgress(null);
  };

  const handlePreview = (purchase: SharePurchase) => {
    const certificateData = prepareCertificateData(purchase);
    setPreviewData(certificateData);
    setShowPreview(true);
  };

  const downloadFile = (data: Buffer | Blob, filename: string, mimeType: string) => {
    const blob = data instanceof Buffer ? new Blob([data], { type: mimeType }) : data;
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getStatusBadge = (purchase: SharePurchase) => {
    if (purchase.existing_certificate) {
      return <span className="px-2 py-1 bg-green-900/30 text-green-400 rounded text-xs">Has Certificate</span>;
    }
    if (purchase.kyc_info?.kyc_status === 'completed') {
      return <span className="px-2 py-1 bg-blue-900/30 text-blue-400 rounded text-xs">Ready</span>;
    }
    return <span className="px-2 py-1 bg-yellow-900/30 text-yellow-400 rounded text-xs">Pending KYC</span>;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-white">Certificate Generator</h2>
          <p className="text-gray-400">Generate professional share certificates instantly</p>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* Generation Options */}
          <div className="flex items-center space-x-2">
            <label className="text-sm text-gray-400">Format:</label>
            <select
              value={generationOptions.format}
              onChange={(e) => setGenerationOptions(prev => ({ ...prev, format: e.target.value as any }))}
              className="bg-gray-700 text-white rounded px-2 py-1 text-sm"
            >
              <option value="pdf">PDF</option>
              <option value="png">PNG</option>
              <option value="both">Both</option>
            </select>
          </div>
          
          <div className="flex items-center space-x-2">
            <label className="text-sm text-gray-400">Quality:</label>
            <select
              value={generationOptions.quality}
              onChange={(e) => setGenerationOptions(prev => ({ ...prev, quality: e.target.value as any }))}
              className="bg-gray-700 text-white rounded px-2 py-1 text-sm"
            >
              <option value="standard">Standard</option>
              <option value="high">High</option>
              <option value="print">Print</option>
            </select>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <label className="text-sm text-gray-400">Filter:</label>
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value as any)}
              className="bg-gray-700 text-white rounded px-3 py-2"
            >
              <option value="all">All Purchases</option>
              <option value="pending">Pending Certificates</option>
              <option value="approved">Approved Purchases</option>
              <option value="has_certificate">Has Certificate</option>
            </select>
          </div>
          
          <input
            type="text"
            placeholder="Search by name, email, or ID..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="bg-gray-700 text-white rounded px-3 py-2 w-64"
          />
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={handleSelectAll}
            className="px-4 py-2 bg-gray-700 text-white rounded hover:bg-gray-600"
          >
            {selectedPurchases.size === filteredPurchases.length ? 'Deselect All' : 'Select All'}
          </button>
          
          <button
            onClick={handleGenerateBatch}
            disabled={selectedPurchases.size === 0 || generating}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {generating ? 'Generating...' : `Generate ${selectedPurchases.size} Certificates`}
          </button>
        </div>
      </div>

      {/* Batch Progress */}
      {batchProgress && (
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-white font-medium">Batch Generation Progress</h3>
            <span className="text-gray-400 text-sm">
              {batchProgress.completed + batchProgress.failed} / {batchProgress.total}
            </span>
          </div>
          
          <div className="w-full bg-gray-700 rounded-full h-2 mb-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((batchProgress.completed + batchProgress.failed) / batchProgress.total) * 100}%` }}
            />
          </div>
          
          <p className="text-gray-300 text-sm">{batchProgress.current}</p>
          
          {batchProgress.errors.length > 0 && (
            <div className="mt-2 p-2 bg-red-900/30 rounded border border-red-700">
              <p className="text-red-400 text-sm font-medium">Errors:</p>
              {batchProgress.errors.map((error, index) => (
                <p key={index} className="text-red-300 text-xs">{error}</p>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Purchases Table */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-700">
              <tr>
                <th className="px-4 py-3 text-left text-white">
                  <input
                    type="checkbox"
                    checked={selectedPurchases.size === filteredPurchases.length && filteredPurchases.length > 0}
                    onChange={handleSelectAll}
                    className="rounded"
                  />
                </th>
                <th className="px-4 py-3 text-left text-white">User</th>
                <th className="px-4 py-3 text-left text-white">Shares</th>
                <th className="px-4 py-3 text-left text-white">Amount</th>
                <th className="px-4 py-3 text-left text-white">Status</th>
                <th className="px-4 py-3 text-left text-white">Actions</th>
              </tr>
            </thead>
            <tbody>
              {loading ? (
                <tr>
                  <td colSpan={6} className="px-4 py-8 text-center text-gray-400">
                    Loading purchases...
                  </td>
                </tr>
              ) : filteredPurchases.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-4 py-8 text-center text-gray-400">
                    No purchases found
                  </td>
                </tr>
              ) : (
                filteredPurchases.map((purchase) => (
                  <tr key={purchase.id} className="border-t border-gray-700 hover:bg-gray-700/30">
                    <td className="px-4 py-3">
                      <input
                        type="checkbox"
                        checked={selectedPurchases.has(purchase.id)}
                        onChange={() => handleSelectPurchase(purchase.id)}
                        className="rounded"
                      />
                    </td>
                    <td className="px-4 py-3">
                      <div>
                        <p className="text-white font-medium">{purchase.user.full_name}</p>
                        <p className="text-gray-400 text-sm">{purchase.user.email}</p>
                      </div>
                    </td>
                    <td className="px-4 py-3 text-white">
                      {purchase.shares_purchased.toLocaleString()}
                    </td>
                    <td className="px-4 py-3 text-white">
                      ${purchase.total_amount.toLocaleString()}
                    </td>
                    <td className="px-4 py-3">
                      {getStatusBadge(purchase)}
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handlePreview(purchase)}
                          className="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-500"
                        >
                          Preview
                        </button>
                        
                        <button
                          onClick={() => handleGenerateSingle(purchase)}
                          disabled={generating}
                          className={`px-3 py-1 text-white rounded text-sm disabled:opacity-50 ${
                            purchase.existing_certificate
                              ? 'bg-blue-600 hover:bg-blue-700'
                              : 'bg-green-600 hover:bg-green-700'
                          }`}
                        >
                          {purchase.existing_certificate ? 'Download' : 'Generate'}
                        </button>

                        {purchase.existing_certificate && (
                          <span className="px-3 py-1 bg-blue-900/30 text-blue-400 rounded text-sm">
                            {purchase.existing_certificate.certificate_number}
                          </span>
                        )}
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Certificate Preview Modal */}
      {showPreview && previewData && (
        <ProfessionalCertificatePreview
          data={previewData}
          onClose={() => setShowPreview(false)}
          onDownload={() => {
            // Find the purchase for this preview
            const purchase = purchases.find(p =>
              p.user.full_name === previewData.userFullName &&
              p.shares_purchased === previewData.sharesQuantity
            );
            if (purchase) {
              handleGenerateSingle(purchase);
            }
          }}
        />
      )}
    </div>
  );
};

export default AdminCertificateCreator;
