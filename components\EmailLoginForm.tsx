import React, { useState, useEffect, useRef } from 'react'
import { signInWithEmailEnhanced, supabase } from '../lib/supabase'
import { verifyPassword } from '../lib/passwordSecurity'

interface EmailLoginFormProps {
  onLoginSuccess: (user: any) => void
  onSwitchToRegister: () => void
}

export const EmailLoginForm: React.FC<EmailLoginFormProps> = ({
  onLoginSuccess,
  onSwitchToRegister
}) => {
  const [loginMode, setLoginMode] = useState<'email' | 'telegram'>('email')
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    telegramId: ''
  })
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<{ [key: string]: string }>({})
  const [generalError, setGeneralError] = useState('')
  const [showPassword, setShowPassword] = useState(true)
  const [telegramVerification, setTelegramVerification] = useState<{
    verified: boolean
    telegramUser: any
    needsProfileCompletion: boolean
    loading: boolean
    error: string
  }>({
    verified: false,
    telegramUser: null,
    needsProfileCompletion: false,
    loading: false,
    error: ''
  })

  // Ref for debouncing Telegram ID verification
  const telegramVerificationTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (telegramVerificationTimeoutRef.current) {
        clearTimeout(telegramVerificationTimeoutRef.current)
      }
    }
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))

    // Clear field-specific error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }

    // Clear general error when user starts typing
    if (generalError) {
      setGeneralError('')
    }

    // Auto-verify Telegram ID when user finishes typing
    if (name === 'telegramId') {
      // Clear existing timeout
      if (telegramVerificationTimeoutRef.current) {
        clearTimeout(telegramVerificationTimeoutRef.current)
      }

      // Only verify if ID is long enough
      if (value.length >= 8) {
        telegramVerificationTimeoutRef.current = setTimeout(() => {
          verifyTelegramId(value)
        }, 500) // Debounce for 500ms
      } else {
        // Clear verification state for short IDs
        setTelegramVerification(prev => ({
          ...prev,
          verified: false,
          telegramUser: null,
          error: ''
        }))
      }
    }
  }

  const verifyTelegramId = async (telegramId: string) => {
    if (!telegramId || telegramId.length < 8) {
      setTelegramVerification(prev => ({
        ...prev,
        verified: false,
        telegramUser: null,
        error: ''
      }))
      return
    }

    // Prevent duplicate verification calls for the same ID
    if (telegramVerification.loading ||
        (telegramVerification.verified && telegramVerification.telegramUser?.telegram_id?.toString() === telegramId)) {
      return
    }

    setTelegramVerification(prev => ({ ...prev, loading: true, error: '' }))

    try {
      const telegramIdNum = parseInt(telegramId)

      console.log('🔍 Looking for Telegram ID:', telegramIdNum)
      console.log('🔧 Using service role client to bypass RLS policies')

      // Use singleton service role client to avoid multiple instances
      const { getServiceRoleClient } = await import('../lib/supabase')
      const serviceRoleClient = getServiceRoleClient()

      console.log('🔧 Service role client created, making query...')

      // FIXED: Always check telegram_users table first (that's where telegram IDs are stored)
      console.log('🔍 Checking telegram_users table for telegram_id:', telegramIdNum)

      const { data: telegramUser, error: telegramError } = await serviceRoleClient
        .from('telegram_users')
        .select('*')
        .eq('telegram_id', telegramIdNum)
        .maybeSingle()

      console.log('🔍 Telegram user query result:', { data: telegramUser, error: telegramError })

      if (telegramError || !telegramUser) {
        console.log('❌ Telegram ID not found in telegram_users table')
        setTelegramVerification({
          loading: false,
          error: 'Telegram ID not found. Please check your ID or contact support.',
          verified: false,
          telegramUser: null,
          needsProfileCompletion: false
        })
        return
      }

      console.log('✅ Found user in telegram_users table:', telegramUser)

      // Now check if this telegram user has a completed profile in users table
      let userWithCompleteProfile = null
      if (telegramUser.user_id) {
        console.log('🔗 Telegram user has linked user_id, checking users table:', telegramUser.user_id)

        const { data: linkedUser, error: linkedUserError } = await serviceRoleClient
          .from('users')
          .select('*')
          .eq('id', telegramUser.user_id)
          .maybeSingle()

        if (!linkedUserError && linkedUser) {
          userWithCompleteProfile = linkedUser
          console.log('✅ Found linked user in users table:', linkedUser.email)
        }
      } else {
        // Also check if user exists in users table by telegram_id (new linking method)
        console.log('🔍 No user_id link, checking users table by telegram_id')

        const { data: userByTelegramId, error: userByTelegramIdError } = await serviceRoleClient
          .from('users')
          .select('*')
          .eq('telegram_id', telegramIdNum)
          .maybeSingle()

        if (!userByTelegramIdError && userByTelegramId) {
          userWithCompleteProfile = userByTelegramId
          console.log('✅ Found user in users table by telegram_id:', userByTelegramId.email)
        }
      }

      // Determine which user data to use and if profile completion is needed
      let userToUse, needsCompletion

      if (userWithCompleteProfile) {
        // Use the complete profile from users table
        userToUse = userWithCompleteProfile

        // Check if profile is actually complete
        needsCompletion = !userToUse.email ||
                         !userToUse.password_hash ||
                         !userToUse.full_name ||
                         !userToUse.phone ||
                         !userToUse.country_of_residence

        console.log('🔍 Using complete profile, needs completion:', needsCompletion)
      } else {
        // Use telegram_users data, profile completion needed
        userToUse = {
          id: telegramUser.telegram_id,
          username: telegramUser.username || `telegram_${telegramIdNum}`,
          email: telegramUser.temp_email || `telegram_${telegramIdNum}@temp.local`,
          telegram_id: telegramUser.telegram_id,
          password_hash: null,
          created_at: telegramUser.created_at
        }
        needsCompletion = true
        console.log('🔍 Using telegram_users data, needs completion: true')
      }

      setTelegramVerification({
        loading: false,
        error: '',
        verified: true,
        telegramUser: userToUse,
        needsProfileCompletion: needsCompletion
      })

    } catch (error) {
      console.error('❌ Telegram verification error:', error)
      setTelegramVerification({
        loading: false,
        error: 'Verification failed. Please try again.',
        verified: false,
        telegramUser: null,
        needsProfileCompletion: false
      })
    }
  }

  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {}

    if (loginMode === 'email') {
      // Email or Username validation
      if (!formData.email.trim()) {
        newErrors.email = 'Email address or username is required'
      }
      // Note: We don't validate format here since it could be either email or username

      // Password validation
      if (!formData.password) {
        newErrors.password = 'Password is required'
      }
    } else {
      // Telegram ID validation
      if (!formData.telegramId.trim()) {
        newErrors.telegramId = 'Telegram ID is required'
      } else if (!/^\d{8,12}$/.test(formData.telegramId)) {
        newErrors.telegramId = 'Telegram ID must be 8-12 digits'
      } else if (!telegramVerification.verified) {
        newErrors.telegramId = 'Please verify your Telegram ID first'
      }

      // Password validation for verified Telegram users (security requirement)
      if (telegramVerification.verified && !telegramVerification.needsProfileCompletion) {
        if (!formData.password) {
          newErrors.password = 'Password is required for security verification'
        }
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setGeneralError('')

    if (!validateForm()) {
      return
    }

    setLoading(true)

    try {
      if (loginMode === 'email') {
        // Email login
        const { user, error } = await signInWithEmailEnhanced(formData.email, formData.password)

        if (error) {
          setGeneralError(error.message || 'Login failed')
        } else if (user) {
          console.log('✅ Email login successful:', user)
          console.log('🔍 Checking Supabase auth session after login...')

          // Check if Supabase auth session is established
          const { data: { session }, error: sessionError } = await supabase.auth.getSession()
          console.log('📋 Session status:', {
            hasSession: !!session,
            sessionError: sessionError?.message,
            userId: session?.user?.id,
            userEmail: session?.user?.email
          })

          // CRITICAL FIX: Store user session data in localStorage for dashboard access
          console.log('💾 Storing email login session data in localStorage...')
          console.log('🔍 DEBUG: User object structure:', user)
          console.log('🔍 DEBUG: Database user:', user.database_user)
          console.log('🔍 DEBUG: User metadata:', user.user_metadata)

          const dbUser = user.database_user || user
          console.log('🔍 DEBUG: Final dbUser for storage:', dbUser)
          console.log('🔍 DEBUG: dbUser.id:', dbUser.id, 'Type:', typeof dbUser.id)

          const sessionData = {
            userId: dbUser.id,
            username: dbUser.username,
            email: dbUser.email,
            fullName: dbUser.full_name,
            phone: dbUser.phone,
            country: dbUser.country_of_residence,
            isActive: dbUser.is_active,
            isAdmin: dbUser.is_admin || false,
            telegramId: dbUser.telegram_id,
            telegramUsername: dbUser.username,
            telegramConnected: !!dbUser.telegram_id,
            loginMethod: 'email',
            sessionStart: new Date().toISOString(),
            lastActivity: new Date().toISOString()
          }

          // Store session data that dashboard expects
          localStorage.setItem('aureus_session', JSON.stringify(sessionData))
          localStorage.setItem('aureus_user', JSON.stringify(dbUser))

          // If user has telegram_id, also store telegram user data
          if (dbUser.telegram_id) {
            const telegramUserData = {
              id: 'email_' + dbUser.id,
              email: dbUser.email,
              database_user: {
                ...dbUser,
                telegram_connected: true
              },
              account_type: 'email_with_telegram',
              user_metadata: {
                telegram_id: dbUser.telegram_id,
                telegram_username: dbUser.username,
                full_name: dbUser.full_name,
                username: dbUser.username,
                telegram_connected: true,
                telegram_registered: true
              }
            }
            localStorage.setItem('aureus_telegram_user', JSON.stringify(telegramUserData))
          }

          console.log('✅ Session data stored successfully')

          // Small delay to ensure session is fully established
          await new Promise(resolve => setTimeout(resolve, 100))

          onLoginSuccess(user)
        }
      } else {
        // Telegram login
        if (!telegramVerification.needsProfileCompletion) {
          // Direct login for users with complete profiles - verify password for security
          console.log('🔄 Telegram login with password verification:', telegramVerification.telegramUser)

          // FIXED: Get the actual user data with password hash from users table
          const { getServiceRoleClient } = await import('../lib/supabase')
          const serviceRoleClient = getServiceRoleClient()

          // Find the user with the password hash (either linked user or user by telegram_id)
          let userWithPassword = null

          if (telegramVerification.telegramUser.user_id) {
            // Check linked user first
            const { data: linkedUser } = await serviceRoleClient
              .from('users')
              .select('*')
              .eq('id', telegramVerification.telegramUser.user_id)
              .single()
            userWithPassword = linkedUser
          } else {
            // Check user by telegram_id
            const { data: userByTelegramId } = await serviceRoleClient
              .from('users')
              .select('*')
              .eq('telegram_id', parseInt(formData.telegramId))
              .single()
            userWithPassword = userByTelegramId
          }

          if (!userWithPassword || !userWithPassword.password_hash) {
            setGeneralError('Account setup incomplete. Please contact support.')
            return
          }

          // Verify password against the correct password hash
          const passwordValid = await verifyPassword(formData.password, userWithPassword.password_hash)

          if (!passwordValid) {
            setGeneralError('Invalid password. Please check your password and try again.')
            return
          }

          // CRITICAL FIX: Establish proper Telegram session connection
          console.log('🔧 Establishing Telegram session connection...')

          // Get telegram user data for session
          const { data: telegramUserData } = await serviceRoleClient
            .from('telegram_users')
            .select('*')
            .eq('telegram_id', parseInt(formData.telegramId))
            .single()

          if (telegramUserData) {
            // Create complete session data using the correct user data
            const completeSessionData = {
              userId: userWithPassword.id,
              username: userWithPassword.username,
              email: userWithPassword.email,
              fullName: userWithPassword.full_name,
              phone: userWithPassword.phone,
              address: userWithPassword.address,
              country: userWithPassword.country_of_residence,
              isActive: userWithPassword.is_active,
              isVerified: userWithPassword.is_verified,
              isAdmin: userWithPassword.is_admin,

              // Telegram connection data
              telegramId: telegramUserData.telegram_id,
              telegramUsername: telegramUserData.username,
              telegramConnected: true,
              telegramRegistered: telegramUserData.is_registered,

              // Session metadata
              loginMethod: 'telegram',
              sessionStart: new Date().toISOString(),
              lastActivity: new Date().toISOString()
            }

            // Store complete session data in localStorage
            localStorage.setItem('aureus_session', JSON.stringify(completeSessionData))
            localStorage.setItem('aureus_user', JSON.stringify({
              ...userWithPassword,
              telegram_id: telegramUserData.telegram_id,
              telegram_username: telegramUserData.username,
              telegram_connected: true
            }))
            localStorage.setItem('telegram_user', JSON.stringify({
              telegram_id: telegramUserData.telegram_id,
              username: telegramUserData.username,
              user_id: telegramUserData.user_id,
              is_registered: telegramUserData.is_registered,
              connected: true
            }))

            console.log('✅ Telegram session connection established')
          }

          console.log('✅ Password verification successful for Telegram user')

          // Create authenticated user object with complete Telegram connection using correct data
          const authenticatedUser = {
            id: `telegram_${telegramUserData.telegram_id}`,
            email: userWithPassword.email,
            database_user: {
              ...userWithPassword,
              telegram_id: telegramUserData.telegram_id,
              telegram_username: telegramUserData.username,
              telegram_connected: true
            },
            account_type: 'telegram_direct',
            user_metadata: {
              telegram_id: telegramUserData.telegram_id,
              telegram_username: telegramUserData.username,
              full_name: userWithPassword.full_name,
              username: userWithPassword.username,
              telegram_connected: true,
              telegram_registered: telegramUserData.is_registered
            }
          }

          // Store user in localStorage for session persistence
          console.log('💾 Storing Telegram user in localStorage for session persistence')
          localStorage.setItem('aureus_telegram_user', JSON.stringify(authenticatedUser))

          console.log('✅ Telegram direct login successful:', authenticatedUser)
          onLoginSuccess(authenticatedUser)
        } else {
          // Handle users that need profile completion (missing email/password)
          console.log('🔄 Telegram user needs profile completion:', telegramVerification.telegramUser)

          const incompleteUser = {
            id: `telegram_${telegramVerification.telegramUser.telegram_id}`,
            email: null,
            needsProfileCompletion: true,
            telegramUser: telegramVerification.telegramUser,
            user_metadata: {
              telegram_id: telegramVerification.telegramUser.telegram_id,
              full_name: telegramVerification.telegramUser.full_name,
              username: telegramVerification.telegramUser.username,
              profile_completion_required: true
            }
          }

          console.log('✅ Telegram user authenticated, redirecting to profile completion')
          onLoginSuccess(incompleteUser)
        }
      }
    } catch (err) {
      console.error('Login error:', err)
      setGeneralError('Login failed. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="bg-gray-900/50 backdrop-blur-xl rounded-3xl p-8 border border-gray-700/30 shadow-2xl mb-8">
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center">
          <h3 className="text-2xl font-bold text-white mb-2">
            Sign In to Your Account
          </h3>
          <p className="text-gray-400">
            Access your Aureus Alliance Holdings dashboard
          </p>
        </div>

        {/* Login Mode Selection */}
        <div>
          <label className="block text-sm font-semibold text-gray-300 mb-4">
            Choose your login method:
          </label>
          <div className="space-y-3">
            <label className="flex items-center space-x-3 cursor-pointer">
              <input
                type="radio"
                name="loginMode"
                value="email"
                checked={loginMode === 'email'}
                onChange={(e) => setLoginMode(e.target.value as 'email' | 'telegram')}
                className="w-4 h-4 text-blue-500 bg-gray-800 border-gray-600 focus:ring-blue-500"
              />
              <span className="text-white">Email & Password</span>
            </label>
            <label className="flex items-center space-x-3 cursor-pointer">
              <input
                type="radio"
                name="loginMode"
                value="telegram"
                checked={loginMode === 'telegram'}
                onChange={(e) => setLoginMode(e.target.value as 'email' | 'telegram')}
                className="w-4 h-4 text-blue-500 bg-gray-800 border-gray-600 focus:ring-blue-500"
              />
              <span className="text-white">🚀 Quick Login with Telegram ID</span>
            </label>
          </div>
          {loginMode === 'telegram' && (
            <div className="mt-3 p-3 bg-blue-900/20 border border-blue-500/30 rounded-lg">
              <p className="text-sm text-blue-300">
                💡 <strong>Quick Access:</strong> Get your Telegram ID from @AureusAllianceBot using the "Connect to Website" button, then paste it below to instantly access your account.
              </p>
            </div>
          )}
        </div>

        {/* Login Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Email Login Fields */}
          {loginMode === 'email' && (
            <>
              {/* Email or Username */}
              <div>
                <label htmlFor="email" className="block text-sm font-semibold text-gray-300 mb-3">
                  Email Address or Username
                </label>
                <input
                  id="email"
                  name="email"
                  type="text"
                  value={formData.email}
                  onChange={handleInputChange}
                  autoComplete="username"
                  className={`w-full px-4 py-4 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200 ${
                    errors.email ? 'border-red-500/50' : 'border-gray-600/50'
                  }`}
                  placeholder="<EMAIL> or username"
                />
                {errors.email && (
                  <p className="mt-2 text-sm text-red-400">{errors.email}</p>
                )}
              </div>
            </>
          )}

          {/* Telegram Login Fields */}
          {loginMode === 'telegram' && (
            <div>
              <label htmlFor="telegramId" className="block text-sm font-semibold text-gray-300 mb-3">
                Telegram ID
              </label>
              <div className="space-y-2">
                <input
                  id="telegramId"
                  name="telegramId"
                  type="text"
                  value={formData.telegramId}
                  onChange={handleInputChange}
                  autoComplete="username"
                  className={`w-full px-4 py-4 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200 ${
                    errors.telegramId ? 'border-red-500/50' : 'border-gray-600/50'
                  }`}
                  placeholder="Paste your Telegram ID from the bot (e.g., 1393852532)"
                />
                {errors.telegramId && (
                  <p className="mt-2 text-sm text-red-400">{errors.telegramId}</p>
                )}
                <div className="p-3 bg-gray-800/40 border border-gray-600/30 rounded-lg">
                  <p className="text-xs text-gray-400 mb-2">
                    <strong>How to find your Telegram ID:</strong>
                  </p>
                  <ol className="text-xs text-gray-400 space-y-1 list-decimal list-inside">
                    <li>Open Telegram and go to <strong>@AureusAllianceBot</strong></li>
                    <li>Click the <strong>"Connect to Website"</strong> button</li>
                    <li>The bot will display your Telegram ID</li>
                    <li>Copy the ID number and paste it here</li>
                  </ol>
                </div>
              </div>

              {/* Telegram Verification Status */}
              {telegramVerification.loading && (
                <div className="p-3 bg-blue-900/20 border border-blue-500/30 rounded-lg">
                  <div className="flex items-center space-x-2 text-blue-300">
                    <div className="w-4 h-4 border-2 border-blue-300/30 border-t-blue-300 rounded-full animate-spin"></div>
                    <span>Verifying Telegram ID...</span>
                  </div>
                </div>
              )}

              {telegramVerification.error && (
                <div className="p-3 bg-red-900/20 border border-red-500/30 rounded-lg">
                  <div className="flex items-center space-x-2 text-red-300">
                    <span>⚠️</span>
                    <span className="text-sm">{telegramVerification.error}</span>
                  </div>
                </div>
              )}

              {telegramVerification.verified && telegramVerification.telegramUser && (
                <div className="p-3 bg-green-900/20 border border-green-500/30 rounded-lg">
                  <div className="flex items-center space-x-2 text-green-300 mb-2">
                    <span className="text-lg">✅</span>
                    <span className="font-semibold">Telegram Account Found</span>
                  </div>
                  <p className="text-sm text-gray-300">
                    <strong>{telegramVerification.telegramUser.full_name || 'User'}</strong>
                    {telegramVerification.telegramUser.username && (
                      <span> (@{telegramVerification.telegramUser.username})</span>
                    )}
                  </p>
                  {!telegramVerification.needsProfileCompletion && (
                    <p className="text-xs text-green-400 mt-1">
                      🚀 Ready to access your dashboard with commission data!
                    </p>
                  )}
                  {telegramVerification.needsProfileCompletion && (
                    <p className="text-xs text-yellow-400 mt-1">
                      ⚠️ Profile completion required after login
                    </p>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Password - Required for both email and Telegram login */}
          {(loginMode === 'email' || (loginMode === 'telegram' && telegramVerification.verified && !telegramVerification.needsProfileCompletion)) && (
            <div>
              <label htmlFor="password" className="block text-sm font-semibold text-gray-300 mb-3">
                Password
              </label>
              <div className="relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={handleInputChange}
                  autoComplete="current-password"
                  className={`w-full px-4 py-4 pr-12 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200 ${
                    errors.password ? 'border-red-500/50' : 'border-gray-600/50'
                  }`}
                  placeholder="••••••••"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors duration-200"
                >
                  {showPassword ? (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                    </svg>
                  ) : (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-2 text-sm text-red-400">{errors.password}</p>
              )}
            </div>
          )}

          {/* General Error */}
          {generalError && (
            <div className="p-4 bg-red-900/30 border border-red-500/50 rounded-xl text-red-300 text-sm backdrop-blur-sm">
              <div className="flex items-center gap-2">
                <span className="text-red-400">⚠️</span>
                {generalError}
              </div>
            </div>
          )}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={loading}
            className="w-full py-4 px-6 bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 font-bold rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-lg shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]"
            style={{ color: '#000000 !important' }}
          >
            {loading ? (
              <div className="flex items-center justify-center gap-2" style={{ color: '#000000 !important' }}>
                <div className="w-5 h-5 border-2 border-black/30 border-t-black rounded-full animate-spin"></div>
                <span style={{ color: '#000000 !important' }}>
                  {loginMode === 'telegram' ? 'Accessing Account...' : 'Signing In...'}
                </span>
              </div>
            ) : (
              <span style={{ color: '#000000 !important' }}>
                {loginMode === 'telegram' ? 'Access Account' : 'Sign In'}
              </span>
            )}
          </button>
        </form>

        {/* Forgot Password - Disabled (now handled by UnifiedAuthPage) */}
        {loginMode === 'email' && (
          <div className="text-center">
            <button
              type="button"
              className="text-gray-400 hover:text-gray-300 text-sm transition-colors duration-200"
              onClick={() => {
                alert('Password reset is now handled through the main login page. Please go to the login page and click "Forgot your password?" there.');
              }}
            >
              Forgot your password?
            </button>
          </div>
        )}

        {/* Switch to Register */}
        <div className="text-center">
          <p className="text-gray-300 mb-4">Don't have an account yet?</p>
          <button
            onClick={onSwitchToRegister}
            className="w-full bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 font-bold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg"
            style={{ color: '#ffffff !important' }}
          >
            <span style={{ color: '#ffffff !important' }}>Create Account</span>
          </button>
        </div>



        {/* Security Notice */}
        <div className="text-center">
          <p className="text-xs text-gray-500">
            Secure authentication powered by Supabase
          </p>
        </div>
      </div>
    </div>
  )
}
