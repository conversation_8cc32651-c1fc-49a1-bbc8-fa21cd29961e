import React, { useState, useEffect } from 'react';
import { getServiceRoleClient } from '../lib/supabase';

interface PhaseData {
  id: number;
  phase_number: number;
  phase_name: string;
  price_per_share: number;
  total_shares_available: number;
  shares_sold: number;
  is_active: boolean;
  start_date?: string;
  end_date?: string;
}

interface ComprehensivePhaseDisplayProps {
  className?: string;
  onNavigateToRegister?: () => void;
}

export const ComprehensivePhaseDisplay: React.FC<ComprehensivePhaseDisplayProps> = ({ className = '', onNavigateToRegister }) => {
  const [phases, setPhases] = useState<PhaseData[]>([]);
  const [activePhase, setActivePhase] = useState<PhaseData | null>(null);
  const [selectedPhaseTab, setSelectedPhaseTab] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchPhases();
  }, []);

  // Data is loaded from the database only. No hardcoded fallback.

  const fetchPhases = async () => {
    try {
      setLoading(true);
      console.log('🔄 Fetching phases from investment_phases table...');

      // Test basic connection first
      console.log('🔍 Testing database connection...');

      // Use service role client - required for investment_phases table access
      let client;
      try {
        client = getServiceRoleClient();
        console.log('✅ Using service role client for investment_phases access');
      } catch (serviceError) {
        console.error('❌ Service role client not available:', serviceError);
        setError('Database access configuration error. Service role client required.');
        setPhases([]);
        setActivePhase(null);
        setSelectedPhaseTab(0);
        setLoading(false);
        return;
      }

      // Fetch phases with calculated shares sold from actual purchases
      const { data: phasesData, error: phasesError } = await client
        .from('investment_phases')
        .select('*')
        .order('phase_number');

      console.log('📡 Raw Supabase response:', { data: phasesData, error: phasesError });
      console.log('📊 Response data type:', typeof phasesData);
      console.log('📊 Response data length:', phasesData?.length);

      if (phasesError) {
        console.error('❌ Phases fetch error:', phasesError);
        console.error('❌ Error details:', {
          message: phasesError.message,
          details: phasesError.details,
          hint: phasesError.hint,
          code: phasesError.code
        });
        setError(`Database error: ${phasesError.message} (Code: ${phasesError.code})`);
        setPhases([]);
        setActivePhase(null);
        setSelectedPhaseTab(0);
      } else if (!phasesData || phasesData.length === 0) {
        console.error('❌ No phases data returned from database');
        console.error('❌ Checking if table exists and has data...');

        // Try a simple count query to verify table exists
        try {
          const { count, error: countError } = await client
            .from('investment_phases')
            .select('*', { count: 'exact', head: true });

          if (countError) {
            console.error('❌ Table access error:', countError);
            setError(`Table access error: ${countError.message}`);
          } else {
            console.log('📊 Table row count:', count);
            setError(`No phases found in database (Table has ${count} rows)`);
          }
        } catch (countErr) {
          console.error('❌ Count query failed:', countErr);
          setError('Database table verification failed');
        }

        setPhases([]);
        setActivePhase(null);
        setSelectedPhaseTab(0);
      } else {
        // Get actual shares sold per phase from aureus_share_purchases
        const { data: purchaseData, error: purchaseError } = await client
          .from('aureus_share_purchases')
          .select('package_name, shares_purchased, status')
          .in('status', ['active', 'approved', 'pending_approval']);

        let actualSharesSold: { [key: string]: number } = {};

        if (!purchaseError && purchaseData) {
          // Map package names to phase names and calculate shares sold
          purchaseData.forEach((purchase: any) => {
            const packageName = purchase.package_name;
            const shares = purchase.shares_purchased || 0;

            // Map package names to phase names
            let phaseName = null;
            if (packageName === 'Pre Sale Purchase' || packageName === 'Pre Sale Package' || packageName === 'Commission Conversion - Phase 0') {
              phaseName = 'Pre Sale';
            } else if (packageName.includes('Phase 1')) {
              phaseName = 'Phase 1';
            } else if (packageName.includes('Phase 2')) {
              phaseName = 'Phase 2';
            } else if (packageName.includes('Phase 3')) {
              phaseName = 'Phase 3';
            } else if (packageName.includes('Phase 4')) {
              phaseName = 'Phase 4';
            } else if (packageName.includes('Phase 5')) {
              phaseName = 'Phase 5';
            } else if (packageName.includes('Phase 6')) {
              phaseName = 'Phase 6';
            } else if (packageName.includes('Phase 7')) {
              phaseName = 'Phase 7';
            } else if (packageName.includes('Phase 8')) {
              phaseName = 'Phase 8';
            } else if (packageName.includes('Phase 9')) {
              phaseName = 'Phase 9';
            } else if (packageName.includes('Phase 10')) {
              phaseName = 'Phase 10';
            } else if (packageName.includes('Phase 11')) {
              phaseName = 'Phase 11';
            } else if (packageName.includes('Phase 12')) {
              phaseName = 'Phase 12';
            } else if (packageName.includes('Phase 13')) {
              phaseName = 'Phase 13';
            } else if (packageName.includes('Phase 14')) {
              phaseName = 'Phase 14';
            } else if (packageName.includes('Phase 15')) {
              phaseName = 'Phase 15';
            } else if (packageName.includes('Phase 16')) {
              phaseName = 'Phase 16';
            } else if (packageName.includes('Phase 17')) {
              phaseName = 'Phase 17';
            } else if (packageName.includes('Phase 18')) {
              phaseName = 'Phase 18';
            } else if (packageName.includes('Phase 19')) {
              phaseName = 'Phase 19';
            }
            // Skip share transfers and other non-phase purchases
            else if (packageName.includes('Share Transfer') || packageName === 'Commission Conversion') {
              return; // Skip these
            }

            if (phaseName) {
              if (!actualSharesSold[phaseName]) {
                actualSharesSold[phaseName] = 0;
              }
              actualSharesSold[phaseName] += shares;
            }
          });
        }

        // Normalize types and use database shares_sold values (they're already correct)
        const normalized = phasesData.map((p: any) => {
          const calculatedSold = actualSharesSold[p.phase_name] || 0;
          const databaseSold = p.shares_sold || 0;

          return {
            ...p,
            price_per_share: typeof p.price_per_share === 'string' ? parseFloat(p.price_per_share) : p.price_per_share,
            total_shares_available: typeof p.total_shares_available === 'string' ? parseInt(p.total_shares_available) : p.total_shares_available,
            shares_sold: databaseSold, // Use database value (already correctly calculated)
            calculated_shares_sold: calculatedSold, // Store calculated value for debugging
            is_active: !!p.is_active
          };
        });

        console.log('🔗 Package name mapping results:', actualSharesSold);
        console.log('✅ LIVE DATA from investment_phases:', normalized);
        console.log('📊 Total phases loaded:', normalized.length);
        console.log('🔍 Presale phase data:', normalized.find((p: any) => p.phase_number === 0));
        console.log('🔍 All shares_sold values:', normalized.map((p: any) => ({ phase: p.phase_number, sold: p.shares_sold, available: p.total_shares_available })));

        setPhases(normalized);
        const currentActive = normalized.find((p: any) => p.is_active) || normalized[0] || null;
        setActivePhase(currentActive);
        setSelectedPhaseTab(currentActive?.phase_number || 0);

        console.log('🎯 Active phase set to:', currentActive);
      }
    } catch (err) {
      console.error('❌ Database connection error:', err);
      setError(`Connection error: ${err}`);
      setPhases([]);
      setActivePhase(null);
      setSelectedPhaseTab(0);
    } finally {
      setLoading(false);
    }
  };



  const getPhaseStatus = (phase: PhaseData) => {
    const completionPercentage = (phase.shares_sold / phase.total_shares_available) * 100;

    if (phase.is_active) {
      return { status: 'Active', color: '#10b981', bgColor: 'rgba(16, 185, 129, 0.1)' };
    } else if (completionPercentage >= 100) {
      return { status: 'Sold Out', color: '#ef4444', bgColor: 'rgba(239, 68, 68, 0.1)' };
    } else if (phase.phase_number < (activePhase?.phase_number || 0)) {
      return { status: 'Completed', color: '#6b7280', bgColor: 'rgba(107, 114, 128, 0.1)' };
    } else {
      return { status: 'Upcoming', color: '#f59e0b', bgColor: 'rgba(245, 158, 11, 0.1)' };
    }
  };



  const handlePurchaseClick = () => {
    // Redirect to login/authentication flow
    const authSection = document.getElementById('auth');
    if (authSection) {
      authSection.scrollIntoView({ behavior: 'smooth' });
    } else {
      // Fallback: redirect to Telegram bot
      window.open('https://t.me/AureusAllianceBot', '_blank');
    }
  };

  if (loading) {
    return (
      <div className={`comprehensive-phase-display ${className}`} style={{ minHeight: '400px' }}>
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gold mx-auto mb-4"></div>
          <p className="text-gray-400">Loading phase information...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`comprehensive-phase-display ${className}`} style={{ minHeight: '400px' }}>
        <div className="text-center py-8">
          <p className="text-red-400 mb-4">⚠️ {error}</p>
          <button
            onClick={fetchPhases}
            className="btn-secondary"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!phases || phases.length === 0) {
    return (
      <div className={`comprehensive-phase-display ${className}`} style={{ minHeight: '400px' }}>
        <div className="text-center py-8">
          <p className="text-gray-400 mb-4">No phase information available</p>
          <button
            onClick={fetchPhases}
            className="btn-secondary"
          >
            Reload
          </button>
        </div>
      </div>
    );
  }

  const selectedPhase = phases.find(p => p.phase_number === selectedPhaseTab) || phases[0];
  const phaseStatus = selectedPhase ? getPhaseStatus(selectedPhase) : null;

  return (
    <div className={`comprehensive-phase-display ${className}`}>

      {/* Phase Tabs */}
      <div className="phase-tabs-container">
        <div className="phase-tabs-header">
          <h3 className="heading-md text-gold mb-sm">20-Phase Share Offering</h3>
          <p className="text-sm text-gray-400 mb-md">
            Select a phase to view pricing and availability details
          </p>
        </div>
        
        <div className="phase-tabs-wrapper">
          <div className="phase-tabs">
            {phases.map((phase) => {
              const isSelected = phase.phase_number === selectedPhaseTab;
              const status = getPhaseStatus(phase);
              
              return (
                <button
                  key={phase.id}
                  onClick={() => setSelectedPhaseTab(phase.phase_number)}
                  className={`phase-tab ${isSelected ? 'active' : ''} ${phase.phase_number === 0 ? 'presale' : ''}`}
                  style={{
                    borderColor: isSelected ? status.color : 'rgba(75, 85, 99, 0.3)',
                    backgroundColor: isSelected ? status.bgColor : 'rgba(31, 41, 55, 0.5)'
                  }}
                >
                  <div className="phase-tab-number">
                    {phase.phase_number === 0 ? 'PRE' : phase.phase_number}
                  </div>
                  <div className="phase-tab-price">
                    ${phase.price_per_share.toFixed(2)}
                  </div>
                  <div className="phase-tab-status" style={{ color: status.color }}>
                    {status.status}
                  </div>
                </button>
              );
            })}
          </div>
          

        </div>
      </div>

      {/* Selected Phase Details */}
      {selectedPhase && phaseStatus && (
        <div className="phase-details-card">
          <div className="phase-details-header">
            <div className="phase-title-section">
              <h4 className="phase-title">
                {selectedPhase.phase_name}
                {selectedPhase.phase_number === 0 && (
                  <span className="presale-badge">PRESALE</span>
                )}
              </h4>
              <div className="phase-status-badge" style={{
                backgroundColor: phaseStatus.bgColor,
                color: phaseStatus.color
              }}>
                {phaseStatus.status}
              </div>
            </div>

            <div className="phase-pricing">
              <span className="price-value">${selectedPhase.price_per_share.toFixed(2)}</span>
              <span className="price-label">per share</span>
            </div>
          </div>

          <div className="phase-details-content">
            {/* Share Availability */}
            <div className="phase-availability">
              <div className="availability-stats">
                <div className="stat">
                  <span className="stat-value">
                    {selectedPhase.total_shares_available.toLocaleString()}
                  </span>
                  <span className="stat-label">Total Shares</span>
                </div>
                <div className="stat">
                  <span className="stat-value">
                    {selectedPhase.shares_sold.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 })}
                  </span>
                  <span className="stat-label">Sold</span>
                </div>
                <div className="stat">
                  <span className="stat-value">
                    {(selectedPhase.total_shares_available - selectedPhase.shares_sold).toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 })}
                  </span>
                  <span className="stat-label">Available</span>
                </div>
              </div>

              <div className="progress-bar">
                <div
                  className="progress-fill"
                  style={{
                    width: `${(selectedPhase.shares_sold / selectedPhase.total_shares_available) * 100}%`
                  }}
                />
              </div>
            </div>

            {/* Capital Information */}
            <div className="phase-capital-info">
              <h5 className="capital-title">Capital Information</h5>
              <div className="capital-stats">
                <div className="capital-stat">
                  <span className="capital-label">Total Potential Capital</span>
                  <span className="capital-value">
                    ${(selectedPhase.total_shares_available * selectedPhase.price_per_share).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </span>
                </div>
                <div className="capital-stat">
                  <span className="capital-label">Capital Raised</span>
                  <span className="capital-value raised">
                    ${(selectedPhase.shares_sold * selectedPhase.price_per_share).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </span>
                </div>
                <div className="capital-stat">
                  <span className="capital-label">Remaining Capital</span>
                  <span className="capital-value remaining">
                    ${((selectedPhase.total_shares_available - selectedPhase.shares_sold) * selectedPhase.price_per_share).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </span>
                </div>
              </div>
            </div>

            {/* Shareholder Benefits */}
            <div className="phase-benefits">
              <h5 className="benefits-title">Shareholder Benefits</h5>
              <div className="benefits-details">
                <div className="benefit-item">
                  <span className="benefit-icon">🏆</span>
                  <span className="benefit-label">Digital Share Certificate</span>
                  <span className="benefit-value">Blockchain Verified</span>
                </div>
                <div className="benefit-item">
                  <span className="benefit-icon">💎</span>
                  <span className="benefit-label">Mining Operation Rights</span>
                  <span className="benefit-value">Direct Participation</span>
                </div>
                {selectedPhase.phase_number === 0 && (
                  <div className="benefit-item special">
                    <span className="benefit-icon">⭐</span>
                    <span className="benefit-label">Early Access Pricing</span>
                    <span className="benefit-value">Limited Time</span>
                  </div>
                )}
              </div>
            </div>

            {/* Purchase Button */}
            <div className="purchase-section">
              <button
                onClick={handlePurchaseClick}
                className={`purchase-btn ${selectedPhase.phase_number === 0 ? 'presale' : 'regular'}`}
                disabled={phaseStatus.status === 'Sold Out'}
              >
                {phaseStatus.status === 'Sold Out' ? (
                  '🔒 Sold Out'
                ) : phaseStatus.status === 'Upcoming' ? (
                  '⏳ Coming Soon'
                ) : (
                  `🚀 Purchase ${selectedPhase.phase_name} Shares`
                )}
              </button>
              
              {selectedPhase.phase_number === 0 && (
                <p className="purchase-note">
                  ⚡ Limited time: Early access pricing available now!
                </p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Commission Structure Overview - Compact Professional Design */}
      <div className="expansion-overview-compact">
        <div className="expansion-compact-header">
          <div className="expansion-compact-title">
            <span className="expansion-icon-small">🏗️</span>
            <h4>5-Year Expansion Plan</h4>
          </div>
          <p className="expansion-compact-subtitle">Systematic growth across verified gold concessions</p>
        </div>

        <div className="expansion-compact-grid">
          {/* Year 1-2: Foundation */}
          <div className="expansion-compact-card foundation">
            <div className="expansion-compact-badge presale">
              🏆 2026-2027
            </div>
            <div className="expansion-compact-details">
              <div className="expansion-compact-item">
                <span className="expansion-compact-label">Plants</span>
                <span className="expansion-compact-value primary">10-25</span>
              </div>
              <div className="expansion-compact-item">
                <span className="expansion-compact-label">Land Coverage</span>
                <span className="expansion-compact-value secondary">250-625 ha</span>
              </div>
            </div>
            <div className="expansion-compact-highlight">
              ⚡ Foundation phase with proven operations
            </div>
          </div>

          {/* Year 3-5: Scale */}
          <div className="expansion-compact-card scale">
            <div className="expansion-compact-badge expansion">
              📈 2028-2030
            </div>
            <div className="expansion-compact-details">
              <div className="expansion-compact-item">
                <span className="expansion-compact-label">Target Plants</span>
                <span className="expansion-compact-value primary">200</span>
              </div>
              <div className="expansion-compact-item">
                <span className="expansion-compact-label">Maximum Land</span>
                <span className="expansion-compact-value secondary">5,000 ha</span>
              </div>
            </div>
            <div className="expansion-compact-note">
              Full-scale operations across multiple concessions
            </div>
          </div>
        </div>

        {/* Call to Action Section */}
        <div className="expansion-cta-section">
          <div className="expansion-cta-content">
            <h5 className="expansion-cta-title">Secure Your Position</h5>
            <p className="expansion-cta-subtitle">Join the presale and be part of Africa's largest gold mining expansion</p>
          </div>
          <div className="expansion-cta-buttons">
            <button
              className="expansion-cta-btn primary"
              onClick={() => {
                if (onNavigateToRegister) {
                  onNavigateToRegister();
                } else {
                  // Fallback: navigate using window.location
                  window.location.href = '/register';
                }
              }}
            >
              <span className="cta-icon">🚀</span>
              Purchase Shares
            </button>
            <button
              className="expansion-cta-btn secondary"
              onClick={() => {
                // Scroll to calculator section
                const calculatorSection = document.getElementById('calculator');
                if (calculatorSection) {
                  calculatorSection.scrollIntoView({ behavior: 'smooth' });
                }
              }}
            >
              <span className="cta-icon">📊</span>
              Calculate Returns
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
