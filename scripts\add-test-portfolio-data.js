// <PERSON>ript to add test portfolio data for demonstration
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function addTestPortfolioData() {
  console.log('🔧 Adding Test Portfolio Data\n');

  try {
    // 1. First, let's check if we have any users
    console.log('1. Checking for existing users...');
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, username, email, full_name')
      .limit(5);

    if (usersError) {
      console.error('❌ Error accessing users:', usersError.message);
      return;
    }

    if (!users || users.length === 0) {
      console.log('⚠️ No users found. Creating a test user first...');
      
      // Create a test user
      const { data: newUser, error: userError } = await supabase
        .from('users')
        .insert({
          username: 'testuser',
          email: '<EMAIL>',
          full_name: 'Test User',
          phone: '+1234567890',
          country_of_residence: 'USA',
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (userError) {
        console.error('❌ Error creating test user:', userError.message);
        return;
      }

      console.log('✅ Created test user:', newUser.username);
      users.push(newUser);
    }

    console.log(`✅ Found ${users.length} users`);
    const testUser = users[0];
    console.log('👤 Using test user:', testUser.username, '(ID:', testUser.id, ')');

    // 2. Check if investment_phases table exists and has data
    console.log('\n2. Checking investment phases...');
    const { data: phases, error: phasesError } = await supabase
      .from('investment_phases')
      .select('*')
      .order('phase_number');

    if (phasesError) {
      console.error('❌ Error accessing investment_phases:', phasesError.message);
      return;
    }

    if (!phases || phases.length === 0) {
      console.log('⚠️ No investment phases found. Creating test phases...');
      
      const testPhases = [
        { phase_number: 0, phase_name: 'Pre Sale', price_per_share: 5.00, total_shares_available: 200000, is_active: true },
        { phase_number: 1, phase_name: 'Phase 1', price_per_share: 10.00, total_shares_available: 100000, is_active: false },
        { phase_number: 2, phase_name: 'Phase 2', price_per_share: 15.00, total_shares_available: 100000, is_active: false }
      ];

      const { data: createdPhases, error: createPhasesError } = await supabase
        .from('investment_phases')
        .insert(testPhases)
        .select();

      if (createPhasesError) {
        console.error('❌ Error creating test phases:', createPhasesError.message);
        return;
      }

      console.log('✅ Created test phases:', createdPhases.length);
    } else {
      console.log(`✅ Found ${phases.length} investment phases`);
    }

    // 3. Add test share purchases
    console.log('\n3. Adding test share purchases...');
    
    // Check if user already has share purchases
    const { data: existingPurchases } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .eq('user_id', testUser.id);

    if (existingPurchases && existingPurchases.length > 0) {
      console.log(`⚠️ User already has ${existingPurchases.length} share purchases. Skipping creation.`);
    } else {
      const testPurchases = [
        {
          user_id: testUser.id,
          package_name: 'Pre Sale Package',
          shares_purchased: 1000,
          total_amount: 5000.00,
          commission_used: 0,
          remaining_payment: 5000.00,
          payment_method: 'crypto',
          status: 'active',
          created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days ago
          updated_at: new Date().toISOString()
        },
        {
          user_id: testUser.id,
          package_name: 'Phase 1 Package',
          shares_purchased: 500,
          total_amount: 5000.00,
          commission_used: 0,
          remaining_payment: 5000.00,
          payment_method: 'crypto',
          status: 'active',
          created_at: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(), // 15 days ago
          updated_at: new Date().toISOString()
        },
        {
          user_id: testUser.id,
          package_name: 'Phase 2 Package',
          shares_purchased: 200,
          total_amount: 3000.00,
          commission_used: 0,
          remaining_payment: 3000.00,
          payment_method: 'crypto',
          status: 'active',
          created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days ago
          updated_at: new Date().toISOString()
        }
      ];

      const { data: createdPurchases, error: purchasesError } = await supabase
        .from('aureus_share_purchases')
        .insert(testPurchases)
        .select();

      if (purchasesError) {
        console.error('❌ Error creating test purchases:', purchasesError.message);
        return;
      }

      console.log('✅ Created test share purchases:', createdPurchases.length);
    }

    // 4. Add test KYC information
    console.log('\n4. Adding test KYC information...');
    
    // Check if KYC info exists
    const { data: existingKyc } = await supabase
      .from('kyc_information')
      .select('*')
      .eq('user_id', testUser.id)
      .single();

    if (existingKyc) {
      console.log('⚠️ KYC information already exists. Skipping creation.');
    } else {
      const { data: createdKyc, error: kycError } = await supabase
        .from('kyc_information')
        .insert({
          user_id: testUser.id,
          kyc_status: 'completed',
          document_type: 'passport',
          document_number: 'TEST123456',
          full_name: testUser.full_name,
          date_of_birth: '1990-01-01',
          nationality: 'US',
          address: '123 Test Street, Test City, TC 12345',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (kycError) {
        console.log('⚠️ Could not create KYC information (table may not exist):', kycError.message);
      } else {
        console.log('✅ Created test KYC information');
      }
    }

    // 5. Summary
    console.log('\n📋 TEST DATA SUMMARY:');
    console.log('='.repeat(50));
    
    const { data: finalPurchases } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .eq('user_id', testUser.id)
      .eq('status', 'active');

    const totalShares = finalPurchases?.reduce((sum, p) => sum + p.shares_purchased, 0) || 0;
    const totalInvested = finalPurchases?.reduce((sum, p) => sum + p.total_amount, 0) || 0;

    console.log(`👤 Test User: ${testUser.username} (${testUser.email})`);
    console.log(`📊 Total Share Purchases: ${finalPurchases?.length || 0}`);
    console.log(`🏷️ Total Shares Owned: ${totalShares.toLocaleString()}`);
    console.log(`💰 Total Amount Invested: $${totalInvested.toLocaleString()}`);
    console.log(`🎯 KYC Status: Available for testing`);
    
    console.log('\n🎯 PORTFOLIO COMPONENT TEST READY!');
    console.log('The portfolio component should now display data for the test user.');
    console.log(`Login with: ${testUser.email} (if authentication is set up)`);

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the script
addTestPortfolioData();
