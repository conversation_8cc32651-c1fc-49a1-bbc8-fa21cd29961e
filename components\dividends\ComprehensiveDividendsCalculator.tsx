import React, { useState, useEffect, useMemo } from 'react';

interface DividendsCalculatorProps {
  userShares: number;
  currentPhase?: any;
}

interface CalculationInputs {
  landHa: number;
  avgGravelThickness: number;
  inSituGrade: number;
  recoveryFactor: number;
  goldPriceUsdPerKg: number;
  opexPercent: number;
  selectedYear: number;
}

interface CalculationResults {
  numPlants: number;
  annualRevenue: number;
  annualEbit: number;
  annualGoldKg: number;
  dividendPerShare: number;
  userAnnualDividend: number;
  monthlyDividend: number;
  quarterlyDividend: number;
}

interface TrainingStep {
  id: number;
  title: string;
  content: string;
  formula?: string;
  example?: string;
}

// Constants from the working calculator
const TOTAL_SHARES = 1400000; // Correct total shares
const HECTARES_PER_PLANT = 25; // Each plant covers 25 hectares
const PLANT_CAPACITY_TPH = 200; // Tonnes per hour per plant
const EFFECTIVE_HOURS_PER_DAY = 20; // Operating hours per day
const OPERATING_DAYS_PER_YEAR = 330; // Operating days per year
const BULK_DENSITY_T_PER_M3 = 1.8; // Bulk density

// Aureus Africa 5-year expansion plan
const EXPANSION_PLAN = {
  2026: { plants: 10, hectares: 250 },
  2027: { plants: 25, hectares: 625 },
  2028: { plants: 50, hectares: 1250 },
  2029: { plants: 100, hectares: 2500 },
  2030: { plants: 200, hectares: 5000 }
};

const TRAINING_STEPS: TrainingStep[] = [
  {
    id: 1,
    title: "Understanding Gold Production",
    content: "Gold production depends on plant capacity, operating hours, gold grade in the gravel, and recovery efficiency. Each plant processes 200 tonnes per hour for 20 hours daily, 330 days per year.",
    formula: "Annual Throughput = Plants × 200 TPH × 20 hours × 330 days\nGold Production = (Throughput × Grade/Density × Recovery) ÷ 1000",
    example: "10 plants: 10 × 200 × 20 × 330 = 13.2M tonnes\nGold: (13.2M × 0.9/1.8 × 70%) ÷ 1000 = 4,620 kg annually"
  },
  {
    id: 2,
    title: "Revenue Calculation",
    content: "Revenue is calculated by multiplying the total gold produced by the current gold price per kilogram.",
    formula: "Annual Revenue = Gold Production (kg) × Gold Price (USD/kg)",
    example: "4,620 kg × $109,026/kg = $503.7M annual revenue"
  },
  {
    id: 3,
    title: "Operating Expenses (OPEX)",
    content: "Operating expenses include mining costs, processing, labor, equipment maintenance, and overhead. This is typically 40-50% of revenue.",
    formula: "OPEX = Annual Revenue × OPEX Percentage",
    example: "$503.7M × 45% = $226.7M in operating costs"
  },
  {
    id: 4,
    title: "EBIT Calculation",
    content: "EBIT (Earnings Before Interest and Tax) is the profit available for distribution to shareholders after covering all operating expenses.",
    formula: "EBIT = Annual Revenue - Operating Expenses",
    example: "$503.7M - $226.7M = $277M EBIT"
  },
  {
    id: 5,
    title: "Dividend Distribution",
    content: "Aureus Africa distributes 100% of EBIT as dividends to shareholders. Your dividend is proportional to your share ownership.",
    formula: "Dividend Per Share = Total EBIT ÷ Total Shares Outstanding",
    example: "$277M ÷ 1,400,000 shares = $198 per share annually"
  },
  {
    id: 6,
    title: "Your Personal Dividend",
    content: "Your annual dividend is calculated by multiplying the dividend per share by the number of shares you own.",
    formula: "Your Annual Dividend = Dividend Per Share × Your Shares",
    example: "$198 × 1,000 shares = $198,000 annual dividend"
  }
];

export const ComprehensiveDividendsCalculator: React.FC<DividendsCalculatorProps> = ({ 
  userShares, 
  currentPhase 
}) => {
  const [activeTab, setActiveTab] = useState<'calculator' | 'training' | 'projections'>('calculator');
  const [currentTrainingStep, setCurrentTrainingStep] = useState(0);
  const [showAdvanced, setShowAdvanced] = useState(false);
  
  const [inputs, setInputs] = useState<CalculationInputs>({
    landHa: EXPANSION_PLAN[2026].hectares,
    avgGravelThickness: 0.8,
    inSituGrade: 0.9,
    recoveryFactor: 70,
    goldPriceUsdPerKg: 109026,
    opexPercent: 45,
    selectedYear: 2026
  });

  const calculations = useMemo((): CalculationResults => {
    const { landHa, avgGravelThickness, inSituGrade, recoveryFactor, goldPriceUsdPerKg, opexPercent } = inputs;

    // Calculate number of plants needed
    const numPlants = landHa / HECTARES_PER_PLANT;

    // Calculate annual gold production using CORRECT formula from App.tsx
    const annualThroughputT = numPlants * PLANT_CAPACITY_TPH * EFFECTIVE_HOURS_PER_DAY * OPERATING_DAYS_PER_YEAR;
    const annualGoldKg = (annualThroughputT * (inSituGrade / BULK_DENSITY_T_PER_M3) * (recoveryFactor / 100)) / 1000;

    // Calculate revenue and EBIT
    const annualRevenue = annualGoldKg * goldPriceUsdPerKg;
    const annualOperatingCost = annualRevenue * (opexPercent / 100);
    const annualEbit = annualRevenue - annualOperatingCost;
    
    // Calculate dividends (100% EBIT distribution)
    const dividendPerShare = TOTAL_SHARES > 0 ? annualEbit / TOTAL_SHARES : 0;
    const userAnnualDividend = dividendPerShare * userShares;
    const monthlyDividend = userAnnualDividend / 12;
    const quarterlyDividend = userAnnualDividend / 4;

    return {
      numPlants,
      annualRevenue,
      annualEbit,
      annualGoldKg,
      dividendPerShare,
      userAnnualDividend,
      monthlyDividend,
      quarterlyDividend
    };
  }, [inputs, userShares]);

  const handleInputChange = (field: keyof CalculationInputs, value: number) => {
    setInputs(prev => ({ ...prev, [field]: value }));
  };

  const handleYearChange = (year: number) => {
    const planData = EXPANSION_PLAN[year as keyof typeof EXPANSION_PLAN];
    if (planData) {
      setInputs(prev => ({
        ...prev,
        selectedYear: year,
        landHa: planData.hectares
      }));
    }
  };

  const renderTrainingTab = () => (
    <div className="space-y-6">
      {/* Progress Indicator */}
      <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-lg font-semibold text-white">🎓 Dividend Training Course</h3>
          <span className="text-sm text-gray-400">
            Step {currentTrainingStep + 1} of {TRAINING_STEPS.length}
          </span>
        </div>
        <div className="w-full bg-gray-700 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${((currentTrainingStep + 1) / TRAINING_STEPS.length) * 100}%` }}
          />
        </div>
      </div>

      {/* Current Training Step */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="mb-4">
          <h4 className="text-xl font-semibold text-white mb-2">
            {TRAINING_STEPS[currentTrainingStep].title}
          </h4>
          <p className="text-gray-300 leading-relaxed">
            {TRAINING_STEPS[currentTrainingStep].content}
          </p>
        </div>

        {TRAINING_STEPS[currentTrainingStep].formula && (
          <div className="bg-gray-900 rounded-lg p-4 mb-4 border border-gray-600">
            <h5 className="text-sm font-semibold text-yellow-400 mb-2">📐 Formula:</h5>
            <code className="text-green-400 font-mono text-sm">
              {TRAINING_STEPS[currentTrainingStep].formula}
            </code>
          </div>
        )}

        {TRAINING_STEPS[currentTrainingStep].example && (
          <div className="bg-blue-900/30 rounded-lg p-4 border border-blue-700">
            <h5 className="text-sm font-semibold text-blue-400 mb-2">💡 Example:</h5>
            <p className="text-blue-200 text-sm">
              {TRAINING_STEPS[currentTrainingStep].example}
            </p>
          </div>
        )}
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between">
        <button
          onClick={() => setCurrentTrainingStep(Math.max(0, currentTrainingStep - 1))}
          disabled={currentTrainingStep === 0}
          className="px-4 py-2 bg-gray-700 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600"
        >
          ← Previous
        </button>
        <button
          onClick={() => setCurrentTrainingStep(Math.min(TRAINING_STEPS.length - 1, currentTrainingStep + 1))}
          disabled={currentTrainingStep === TRAINING_STEPS.length - 1}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-blue-500"
        >
          Next →
        </button>
      </div>

      {/* Quick Reference */}
      <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
        <h4 className="text-lg font-semibold text-white mb-3">📚 Quick Reference</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <div className="text-gray-400 mb-1">Current Gold Price:</div>
            <div className="text-white font-semibold">${inputs.goldPriceUsdPerKg.toLocaleString()}/kg</div>
          </div>
          <div>
            <div className="text-gray-400 mb-1">Total Shares Outstanding:</div>
            <div className="text-white font-semibold">{TOTAL_SHARES.toLocaleString()}</div>
          </div>
          <div>
            <div className="text-gray-400 mb-1">Your Share Count:</div>
            <div className="text-white font-semibold">{userShares.toLocaleString()}</div>
          </div>
          <div>
            <div className="text-gray-400 mb-1">Dividend Payout:</div>
            <div className="text-white font-semibold">100% of EBIT</div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderProjectionsTab = () => {
    const projections = Object.entries(EXPANSION_PLAN).map(([year, data]) => {
      const yearInputs = { ...inputs, landHa: data.hectares, selectedYear: parseInt(year) };
      const numPlants = data.plants; // Use actual plant count from expansion plan

      // Use CORRECT calculation formula
      const annualThroughputT = numPlants * PLANT_CAPACITY_TPH * EFFECTIVE_HOURS_PER_DAY * OPERATING_DAYS_PER_YEAR;
      const annualGoldKg = (annualThroughputT * (yearInputs.inSituGrade / BULK_DENSITY_T_PER_M3) * (yearInputs.recoveryFactor / 100)) / 1000;
      const annualRevenue = annualGoldKg * yearInputs.goldPriceUsdPerKg;
      const annualOperatingCost = annualRevenue * (yearInputs.opexPercent / 100);
      const annualEbit = annualRevenue - annualOperatingCost;
      const dividendPerShare = TOTAL_SHARES > 0 ? annualEbit / TOTAL_SHARES : 0;
      const userAnnualDividend = dividendPerShare * userShares;

      return {
        year: parseInt(year),
        plants: data.plants,
        hectares: data.hectares,
        goldKg: annualGoldKg,
        revenue: annualRevenue,
        ebit: annualEbit,
        dividendPerShare,
        userDividend: userAnnualDividend
      };
    });

    return (
      <div className="space-y-6">
        {/* 5-Year Overview */}
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <h3 className="text-xl font-semibold text-white mb-4">📈 5-Year Dividend Projections</h3>
          <p className="text-gray-400 mb-6">
            Based on Aureus Africa's expansion plan and your current {userShares.toLocaleString()} shares
          </p>

          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-gray-700">
                  <th className="text-left py-2 text-gray-400">Year</th>
                  <th className="text-left py-2 text-gray-400">Plants</th>
                  <th className="text-left py-2 text-gray-400">Hectares</th>
                  <th className="text-left py-2 text-gray-400">Gold (kg)</th>
                  <th className="text-left py-2 text-gray-400">Revenue</th>
                  <th className="text-left py-2 text-gray-400">Your Dividend</th>
                </tr>
              </thead>
              <tbody>
                {projections.map((proj, index) => (
                  <tr key={proj.year} className="border-b border-gray-700/50">
                    <td className="py-3 text-white font-semibold">{proj.year}</td>
                    <td className="py-3 text-gray-300">{proj.plants}</td>
                    <td className="py-3 text-gray-300">{proj.hectares}</td>
                    <td className="py-3 text-gray-300">{proj.goldKg.toFixed(0)}</td>
                    <td className="py-3 text-gray-300">${(proj.revenue / 1000000).toFixed(1)}M</td>
                    <td className="py-3 text-green-400 font-semibold">
                      ${proj.userDividend.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Growth Visualization */}
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <h4 className="text-lg font-semibold text-white mb-4">📊 Dividend Growth Visualization</h4>
          <div className="space-y-3">
            {projections.map((proj, index) => {
              const maxDividend = Math.max(...projections.map(p => p.userDividend));
              const barWidth = (proj.userDividend / maxDividend) * 100;

              return (
                <div key={proj.year} className="flex items-center space-x-4">
                  <div className="w-12 text-sm text-gray-400">{proj.year}</div>
                  <div className="flex-1 bg-gray-700 rounded-full h-6 relative">
                    <div
                      className="bg-gradient-to-r from-blue-600 to-green-500 h-6 rounded-full transition-all duration-1000"
                      style={{ width: `${barWidth}%` }}
                    />
                    <div className="absolute inset-0 flex items-center justify-center text-xs font-semibold text-white">
                      ${proj.userDividend.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Key Insights */}
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <h4 className="text-lg font-semibold text-white mb-4">💡 Key Insights</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-green-900/30 rounded-lg p-4 border border-green-700">
              <div className="text-green-400 font-semibold mb-2">Total 5-Year Dividends</div>
              <div className="text-2xl font-bold text-white">
                ${projections.reduce((sum, proj) => sum + proj.userDividend, 0).toLocaleString(undefined, { maximumFractionDigits: 0 })}
              </div>
            </div>
            <div className="bg-blue-900/30 rounded-lg p-4 border border-blue-700">
              <div className="text-blue-400 font-semibold mb-2">Peak Annual Dividend (2030)</div>
              <div className="text-2xl font-bold text-white">
                ${projections[projections.length - 1].userDividend.toLocaleString(undefined, { maximumFractionDigits: 0 })}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderHelpTab = () => (
    <div className="space-y-6">
      {/* FAQ Section */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-xl font-semibold text-white mb-4">❓ Frequently Asked Questions</h3>

        <div className="space-y-4">
          <div className="border-b border-gray-700 pb-4">
            <h4 className="text-lg font-semibold text-blue-400 mb-2">
              How are dividends calculated?
            </h4>
            <p className="text-gray-300">
              Dividends are calculated based on the company's EBIT (Earnings Before Interest and Tax).
              Aureus Africa distributes 100% of EBIT as dividends to shareholders. Your dividend is
              proportional to your share ownership out of the total 10 million shares.
            </p>
          </div>

          <div className="border-b border-gray-700 pb-4">
            <h4 className="text-lg font-semibold text-blue-400 mb-2">
              When are dividends paid?
            </h4>
            <p className="text-gray-300">
              Dividends are typically paid quarterly (every 3 months) based on the actual gold
              production and sales. The exact payment dates depend on mining operations and
              gold market conditions.
            </p>
          </div>

          <div className="border-b border-gray-700 pb-4">
            <h4 className="text-lg font-semibold text-blue-400 mb-2">
              What factors affect my dividend amount?
            </h4>
            <p className="text-gray-300">
              Your dividend depends on: (1) Number of shares you own, (2) Gold production volume,
              (3) Gold market price, (4) Operating costs, and (5) Company expansion phase.
              As Aureus Africa expands operations, dividend potential increases significantly.
            </p>
          </div>

          <div className="border-b border-gray-700 pb-4">
            <h4 className="text-lg font-semibold text-blue-400 mb-2">
              How accurate are these projections?
            </h4>
            <p className="text-gray-300">
              Projections are estimates based on geological surveys, current market conditions,
              and the company's expansion plan. Actual results may vary due to gold price
              fluctuations, operational challenges, or changes in mining conditions.
            </p>
          </div>

          <div>
            <h4 className="text-lg font-semibold text-blue-400 mb-2">
              Can I reinvest my dividends?
            </h4>
            <p className="text-gray-300">
              Yes! You can use dividend payments to purchase additional shares during any phase,
              allowing you to compound your holdings and increase future dividend potential.
            </p>
          </div>
        </div>
      </div>

      {/* Key Terms */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-xl font-semibold text-white mb-4">📚 Key Terms</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div>
              <div className="text-yellow-400 font-semibold">EBIT</div>
              <div className="text-sm text-gray-300">
                Earnings Before Interest and Tax - the company's profit available for distribution
              </div>
            </div>
            <div>
              <div className="text-yellow-400 font-semibold">OPEX</div>
              <div className="text-sm text-gray-300">
                Operating Expenses - costs of running mining operations (labor, equipment, etc.)
              </div>
            </div>
            <div>
              <div className="text-yellow-400 font-semibold">Recovery Factor</div>
              <div className="text-sm text-gray-300">
                Percentage of gold successfully extracted from processed gravel
              </div>
            </div>
          </div>
          <div className="space-y-3">
            <div>
              <div className="text-yellow-400 font-semibold">Gravel Thickness</div>
              <div className="text-sm text-gray-300">
                Average depth of gold-bearing gravel layer in meters
              </div>
            </div>
            <div>
              <div className="text-yellow-400 font-semibold">In-Situ Grade</div>
              <div className="text-sm text-gray-300">
                Concentration of gold in the ground before processing (grams per cubic meter)
              </div>
            </div>
            <div>
              <div className="text-yellow-400 font-semibold">Dividend Yield</div>
              <div className="text-sm text-gray-300">
                Annual dividend as a percentage of your share purchase price
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tips for Maximizing Dividends */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-xl font-semibold text-white mb-4">💡 Tips for Maximizing Dividends</h3>

        <div className="space-y-3">
          <div className="flex items-start space-x-3">
            <div className="text-green-400 text-xl">1.</div>
            <div>
              <div className="text-white font-semibold">Buy Early in Each Phase</div>
              <div className="text-sm text-gray-300">
                Share prices increase with each phase. Buying early maximizes your share count for the same amount.
              </div>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <div className="text-green-400 text-xl">2.</div>
            <div>
              <div className="text-white font-semibold">Reinvest Dividends</div>
              <div className="text-sm text-gray-300">
                Use dividend payments to buy more shares, creating a compounding effect on future dividends.
              </div>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <div className="text-green-400 text-xl">3.</div>
            <div>
              <div className="text-white font-semibold">Monitor Gold Prices</div>
              <div className="text-sm text-gray-300">
                Higher gold prices directly increase dividend potential. Stay informed about market trends.
              </div>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <div className="text-green-400 text-xl">4.</div>
            <div>
              <div className="text-white font-semibold">Understand the Expansion Plan</div>
              <div className="text-sm text-gray-300">
                Each expansion phase significantly increases production capacity and dividend potential.
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Contact Information */}
      <div className="bg-blue-900/30 rounded-lg p-6 border border-blue-700">
        <h3 className="text-xl font-semibold text-white mb-4">📞 Need More Help?</h3>
        <p className="text-blue-200 mb-4">
          Our team is here to help you understand your dividend potential and make informed decisions.
        </p>
        <div className="space-y-2 text-sm">
          <div className="text-blue-300">
            📧 Email: <EMAIL>
          </div>
          <div className="text-blue-300">
            💬 Telegram: @AureusSupport
          </div>
          <div className="text-blue-300">
            📱 WhatsApp: Available through your dashboard
          </div>
        </div>
      </div>
    </div>
  );

  const renderCalculatorTab = () => (
    <div className="space-y-6">
      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="text-2xl font-bold text-blue-400">
            {userShares.toLocaleString()}
          </div>
          <div className="text-sm text-gray-400">Your Shares</div>
        </div>
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="text-2xl font-bold text-green-400">
            ${calculations.userAnnualDividend.toLocaleString(undefined, { maximumFractionDigits: 0 })}
          </div>
          <div className="text-sm text-gray-400">Annual Dividend</div>
        </div>
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="text-2xl font-bold text-yellow-400">
            ${calculations.monthlyDividend.toLocaleString(undefined, { maximumFractionDigits: 0 })}
          </div>
          <div className="text-sm text-gray-400">Monthly Dividend</div>
        </div>
      </div>

      {/* Year Selection */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">📅 Select Expansion Year</h3>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
          {Object.entries(EXPANSION_PLAN).map(([year, data]) => (
            <button
              key={year}
              onClick={() => handleYearChange(parseInt(year))}
              className={`p-3 rounded-lg border text-center transition-colors ${
                inputs.selectedYear === parseInt(year)
                  ? 'bg-blue-600 border-blue-500 text-white'
                  : 'bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600'
              }`}
            >
              <div className="font-semibold">{year}</div>
              <div className="text-xs">{data.plants} plants</div>
              <div className="text-xs">{data.hectares} ha</div>
            </button>
          ))}
        </div>
      </div>

      {/* Advanced Controls */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">⚙️ Advanced Parameters</h3>
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="text-blue-400 hover:text-blue-300 text-sm"
          >
            {showAdvanced ? 'Hide' : 'Show'} Advanced
          </button>
        </div>
        
        {showAdvanced && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Gold Price (USD/kg)
              </label>
              <input
                type="number"
                value={inputs.goldPriceUsdPerKg}
                onChange={(e) => handleInputChange('goldPriceUsdPerKg', parseFloat(e.target.value) || 0)}
                className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                OPEX Percentage (%)
              </label>
              <input
                type="number"
                value={inputs.opexPercent}
                onChange={(e) => handleInputChange('opexPercent', parseFloat(e.target.value) || 0)}
                className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Gravel Thickness (m)
              </label>
              <input
                type="number"
                step="0.1"
                value={inputs.avgGravelThickness}
                onChange={(e) => handleInputChange('avgGravelThickness', parseFloat(e.target.value) || 0)}
                className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Recovery Factor (%)
              </label>
              <input
                type="number"
                value={inputs.recoveryFactor}
                onChange={(e) => handleInputChange('recoveryFactor', parseFloat(e.target.value) || 0)}
                className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="bg-gray-900 rounded-lg border border-gray-700">
      {/* Header */}
      <div className="p-6 border-b border-gray-700">
        <h2 className="text-2xl font-bold text-white mb-2">💰 Dividends Calculator</h2>
        <p className="text-gray-400">
          Understand how your dividends are calculated and explore different scenarios
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="flex border-b border-gray-700">
        {[
          { key: 'calculator', label: '🧮 Calculator', icon: '🧮' },
          { key: 'training', label: '🎓 Training', icon: '🎓' },
          { key: 'projections', label: '📈 Projections', icon: '📈' },
          { key: 'help', label: '❓ Help', icon: '❓' }
        ].map(tab => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key as any)}
            className={`flex-1 p-4 text-center font-medium transition-colors ${
              activeTab === tab.key
                ? 'bg-blue-600 text-white border-b-2 border-blue-400'
                : 'text-gray-400 hover:text-gray-300 hover:bg-gray-800'
            }`}
          >
            <span className="mr-2">{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="p-6">
        {activeTab === 'calculator' && renderCalculatorTab()}
        {activeTab === 'training' && renderTrainingTab()}
        {activeTab === 'projections' && renderProjectionsTab()}
        {activeTab === 'help' && renderHelpTab()}
      </div>
    </div>
  );
};
