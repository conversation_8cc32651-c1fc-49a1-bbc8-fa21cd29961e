import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';

interface MaterialTemplate {
  id: string;
  type: 'banner' | 'video' | 'presentation' | 'infographic' | 'qr_code';
  name: string;
  description: string;
  dimensions: string;
  format: string;
  thumbnail: string;
  customizable: string[];
}

interface GeneratedMaterial {
  id: string;
  type: string;
  name: string;
  url: string;
  downloadUrl: string;
  customizations: Record<string, any>;
  createdAt: string;
}

interface MarketingMaterialsGeneratorProps {
  user: any;
  getReferralUsername: (user: any) => string;
  currentPhase?: any;
}

export const MarketingMaterialsGenerator: React.FC<MarketingMaterialsGeneratorProps> = ({
  user,
  getReferralUsername,
  currentPhase
}) => {
  const [selectedType, setSelectedType] = useState<string>('banner');
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [templates, setTemplates] = useState<MaterialTemplate[]>([]);
  const [customizations, setCustomizations] = useState<Record<string, any>>({});
  const [generatedMaterials, setGeneratedMaterials] = useState<GeneratedMaterial[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string>('');

  const materialTypes = [
    { id: 'banner', name: 'Social Media Banners', icon: '🖼️', description: 'Facebook, Instagram, Twitter banners' },
    { id: 'video', name: 'Video Templates', icon: '🎥', description: 'Short promotional videos' },
    { id: 'presentation', name: 'Presentations', icon: '📊', description: 'PowerPoint and PDF presentations' },
    { id: 'infographic', name: 'Infographics', icon: '📈', description: 'Visual data presentations' },
    { id: 'qr_code', name: 'QR Codes', icon: '📱', description: 'Scannable referral codes' }
  ];

  useEffect(() => {
    loadTemplates();
    loadGeneratedMaterials();
  }, [selectedType]);

  const loadTemplates = async () => {
    // Mock templates - in production, these would come from a database
    const mockTemplates: MaterialTemplate[] = [
      {
        id: 'gold_banner_1',
        type: 'banner',
        name: 'Gold Mining Opportunity Banner',
        description: 'Professional banner highlighting gold mining opportunity',
        dimensions: '1200x630px',
        format: 'PNG/JPG',
        thumbnail: '/templates/gold-banner-1-thumb.jpg',
        customizable: ['headline', 'price', 'phase', 'referral_code', 'background_color']
      },
      {
        id: 'success_banner_1',
        type: 'banner',
        name: 'Success Story Banner',
        description: 'Testimonial-style banner with success metrics',
        dimensions: '1200x630px',
        format: 'PNG/JPG',
        thumbnail: '/templates/success-banner-1-thumb.jpg',
        customizable: ['testimonial', 'earnings', 'name', 'photo', 'referral_code']
      },
      {
        id: 'promo_video_1',
        type: 'video',
        name: 'Gold Mining Promo Video',
        description: '30-second promotional video with mining footage',
        dimensions: '1920x1080px',
        format: 'MP4',
        thumbnail: '/templates/promo-video-1-thumb.jpg',
        customizable: ['intro_text', 'price', 'phase', 'outro_text', 'referral_code']
      },
      {
        id: 'business_presentation',
        type: 'presentation',
        name: 'Business Opportunity Presentation',
        description: 'Complete presentation deck for business meetings',
        dimensions: '16:9 slides',
        format: 'PPTX/PDF',
        thumbnail: '/templates/presentation-1-thumb.jpg',
        customizable: ['company_name', 'presenter_name', 'contact_info', 'referral_code']
      },
      {
        id: 'mining_infographic',
        type: 'infographic',
        name: 'Gold Mining Statistics',
        description: 'Infographic with mining industry statistics',
        dimensions: '800x2000px',
        format: 'PNG/PDF',
        thumbnail: '/templates/infographic-1-thumb.jpg',
        customizable: ['statistics', 'company_info', 'referral_code']
      },
      {
        id: 'referral_qr',
        type: 'qr_code',
        name: 'Referral QR Code',
        description: 'Customizable QR code for offline marketing',
        dimensions: '500x500px',
        format: 'PNG/SVG',
        thumbnail: '/templates/qr-code-thumb.jpg',
        customizable: ['referral_url', 'logo', 'colors', 'call_to_action']
      }
    ];

    const filteredTemplates = mockTemplates.filter(t => t.type === selectedType);
    setTemplates(filteredTemplates);
    
    if (filteredTemplates.length > 0 && !selectedTemplate) {
      setSelectedTemplate(filteredTemplates[0].id);
      initializeCustomizations(filteredTemplates[0]);
    }
  };

  const loadGeneratedMaterials = async () => {
    try {
      const { data, error } = await supabase
        .from('generated_marketing_materials')
        .select('*')
        .eq('user_id', user.database_user?.id)
        .eq('material_type', selectedType)
        .order('created_at', { ascending: false })
        .limit(10);

      if (!error && data) {
        setGeneratedMaterials(data);
      }
    } catch (error) {
      console.error('Error loading generated materials:', error);
    }
  };

  const initializeCustomizations = (template: MaterialTemplate) => {
    const defaultCustomizations: Record<string, any> = {};
    
    template.customizable.forEach(field => {
      switch (field) {
        case 'headline':
          defaultCustomizations[field] = '🌟 Own Real Gold Mining Shares!';
          break;
        case 'price':
          defaultCustomizations[field] = currentPhase?.price_per_share || '25.00';
          break;
        case 'phase':
          defaultCustomizations[field] = currentPhase?.phase_name || 'Phase 1';
          break;
        case 'referral_code':
          defaultCustomizations[field] = getReferralUsername(user);
          break;
        case 'referral_url':
          defaultCustomizations[field] = `https://aureus.africa/register?ref=${getReferralUsername(user)}`;
          break;
        case 'background_color':
          defaultCustomizations[field] = '#f59e0b';
          break;
        case 'testimonial':
          defaultCustomizations[field] = 'Amazing returns from my gold mining shares!';
          break;
        case 'earnings':
          defaultCustomizations[field] = '$2,450';
          break;
        case 'name':
          defaultCustomizations[field] = user.first_name || 'Happy Customer';
          break;
        case 'intro_text':
          defaultCustomizations[field] = 'Discover Gold Mining Opportunities';
          break;
        case 'outro_text':
          defaultCustomizations[field] = 'Start Your Journey Today!';
          break;
        case 'company_name':
          defaultCustomizations[field] = 'Aureus Africa Holdings';
          break;
        case 'presenter_name':
          defaultCustomizations[field] = user.first_name || 'Your Name';
          break;
        case 'contact_info':
          defaultCustomizations[field] = user.email || '<EMAIL>';
          break;
        case 'call_to_action':
          defaultCustomizations[field] = 'Scan to Join Aureus Africa!';
          break;
        default:
          defaultCustomizations[field] = '';
      }
    });

    setCustomizations(defaultCustomizations);
  };

  const handleTemplateChange = (templateId: string) => {
    setSelectedTemplate(templateId);
    const template = templates.find(t => t.id === templateId);
    if (template) {
      initializeCustomizations(template);
    }
  };

  const generateMaterial = async () => {
    setIsGenerating(true);
    try {
      const template = templates.find(t => t.id === selectedTemplate);
      if (!template) return;

      // In a real implementation, this would call an API to generate the actual material
      const mockGeneratedMaterial: GeneratedMaterial = {
        id: `generated_${Date.now()}`,
        type: selectedType,
        name: `${template.name} - ${new Date().toLocaleDateString()}`,
        url: `/generated/${selectedType}/${selectedTemplate}_${Date.now()}.${template.format.split('/')[0].toLowerCase()}`,
        downloadUrl: `/api/marketing/download/${selectedTemplate}_${Date.now()}`,
        customizations: { ...customizations },
        createdAt: new Date().toISOString()
      };

      // Save to database
      await supabase.from('generated_marketing_materials').insert({
        user_id: user.database_user?.id,
        material_type: selectedType,
        template_id: selectedTemplate,
        customizations: customizations,
        file_url: mockGeneratedMaterial.url,
        download_url: mockGeneratedMaterial.downloadUrl
      });

      setGeneratedMaterials(prev => [mockGeneratedMaterial, ...prev]);
      setPreviewUrl(mockGeneratedMaterial.url);

    } catch (error) {
      console.error('Error generating material:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const renderCustomizationFields = () => {
    const template = templates.find(t => t.id === selectedTemplate);
    if (!template) return null;

    return (
      <div style={{
        backgroundColor: 'rgba(55, 65, 81, 0.5)',
        borderRadius: '8px',
        padding: '16px',
        marginBottom: '20px'
      }}>
        <h4 style={{ color: '#f3f4f6', fontSize: '16px', fontWeight: '600', marginBottom: '16px' }}>
          🎨 Customize Your Material
        </h4>
        
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '16px'
        }}>
          {template.customizable.map(field => (
            <div key={field}>
              <label style={{
                display: 'block',
                color: '#9ca3af',
                fontSize: '12px',
                marginBottom: '4px',
                textTransform: 'capitalize'
              }}>
                {field.replace('_', ' ')}
              </label>
              
              {field === 'background_color' ? (
                <input
                  type="color"
                  value={customizations[field] || '#f59e0b'}
                  onChange={(e) => setCustomizations(prev => ({ ...prev, [field]: e.target.value }))}
                  style={{
                    width: '100%',
                    height: '40px',
                    border: '1px solid #4b5563',
                    borderRadius: '6px',
                    backgroundColor: 'transparent'
                  }}
                />
              ) : field === 'testimonial' || field === 'intro_text' || field === 'outro_text' ? (
                <textarea
                  value={customizations[field] || ''}
                  onChange={(e) => setCustomizations(prev => ({ ...prev, [field]: e.target.value }))}
                  rows={3}
                  style={{
                    width: '100%',
                    padding: '8px',
                    backgroundColor: 'rgba(75, 85, 99, 0.5)',
                    border: '1px solid #4b5563',
                    borderRadius: '6px',
                    color: 'white',
                    fontSize: '14px',
                    resize: 'vertical'
                  }}
                />
              ) : (
                <input
                  type="text"
                  value={customizations[field] || ''}
                  onChange={(e) => setCustomizations(prev => ({ ...prev, [field]: e.target.value }))}
                  style={{
                    width: '100%',
                    padding: '8px',
                    backgroundColor: 'rgba(75, 85, 99, 0.5)',
                    border: '1px solid #4b5563',
                    borderRadius: '6px',
                    color: 'white',
                    fontSize: '14px'
                  }}
                />
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div style={{
      backgroundColor: 'rgba(31, 41, 55, 0.9)',
      borderRadius: '12px',
      padding: '24px',
      border: '1px solid #374151'
    }}>
      {/* Header */}
      <div style={{ marginBottom: '24px' }}>
        <h3 style={{
          fontSize: '20px',
          fontWeight: 'bold',
          color: '#f59e0b',
          marginBottom: '8px',
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }}>
          🎨 Marketing Materials Generator
        </h3>
        <p style={{ color: '#9ca3af', fontSize: '14px', margin: 0 }}>
          Create professional marketing materials with your referral information
        </p>
      </div>

      {/* Material Type Selection */}
      <div style={{ marginBottom: '24px' }}>
        <label style={{ display: 'block', color: '#f3f4f6', marginBottom: '12px', fontSize: '16px', fontWeight: '600' }}>
          Select Material Type
        </label>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '12px'
        }}>
          {materialTypes.map(type => (
            <button
              key={type.id}
              onClick={() => setSelectedType(type.id)}
              style={{
                padding: '16px',
                backgroundColor: selectedType === type.id 
                  ? 'rgba(59, 130, 246, 0.2)' 
                  : 'rgba(55, 65, 81, 0.5)',
                border: selectedType === type.id 
                  ? '2px solid #3b82f6' 
                  : '1px solid #4b5563',
                borderRadius: '8px',
                color: selectedType === type.id ? '#60a5fa' : '#d1d5db',
                fontSize: '14px',
                fontWeight: '600',
                cursor: 'pointer',
                textAlign: 'left',
                transition: 'all 0.2s ease'
              }}
            >
              <div style={{ fontSize: '20px', marginBottom: '8px' }}>{type.icon}</div>
              <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>{type.name}</div>
              <div style={{ fontSize: '12px', color: '#9ca3af' }}>{type.description}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Template Selection */}
      {templates.length > 0 && (
        <div style={{ marginBottom: '24px' }}>
          <label style={{ display: 'block', color: '#f3f4f6', marginBottom: '12px', fontSize: '16px', fontWeight: '600' }}>
            Choose Template
          </label>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '12px'
          }}>
            {templates.map(template => (
              <div
                key={template.id}
                onClick={() => handleTemplateChange(template.id)}
                style={{
                  padding: '16px',
                  backgroundColor: selectedTemplate === template.id 
                    ? 'rgba(16, 185, 129, 0.2)' 
                    : 'rgba(55, 65, 81, 0.5)',
                  border: selectedTemplate === template.id 
                    ? '2px solid #10b981' 
                    : '1px solid #4b5563',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
              >
                <div style={{
                  width: '100%',
                  height: '120px',
                  backgroundColor: 'rgba(75, 85, 99, 0.5)',
                  borderRadius: '6px',
                  marginBottom: '12px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '32px'
                }}>
                  {materialTypes.find(t => t.id === selectedType)?.icon}
                </div>
                <div style={{ color: '#f3f4f6', fontWeight: '600', marginBottom: '4px' }}>
                  {template.name}
                </div>
                <div style={{ color: '#9ca3af', fontSize: '12px', marginBottom: '8px' }}>
                  {template.description}
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '11px', color: '#6b7280' }}>
                  <span>{template.dimensions}</span>
                  <span>{template.format}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Customization Fields */}
      {selectedTemplate && renderCustomizationFields()}

      {/* Generate Button */}
      <div style={{ display: 'flex', gap: '12px', marginBottom: '24px' }}>
        <button
          onClick={generateMaterial}
          disabled={isGenerating || !selectedTemplate}
          style={{
            padding: '12px 24px',
            backgroundColor: isGenerating ? 'rgba(55, 65, 81, 0.5)' : '#10b981',
            border: 'none',
            borderRadius: '8px',
            color: 'white',
            fontSize: '16px',
            fontWeight: '600',
            cursor: isGenerating ? 'not-allowed' : 'pointer',
            flex: 1
          }}
        >
          {isGenerating ? '⏳ Generating...' : '🎨 Generate Material'}
        </button>
        
        {previewUrl && (
          <button
            onClick={() => window.open(previewUrl, '_blank')}
            style={{
              padding: '12px 24px',
              backgroundColor: 'rgba(59, 130, 246, 0.2)',
              border: '1px solid #3b82f6',
              borderRadius: '8px',
              color: '#60a5fa',
              fontSize: '16px',
              fontWeight: '600',
              cursor: 'pointer'
            }}
          >
            👁️ Preview
          </button>
        )}
      </div>

      {/* Generated Materials History */}
      {generatedMaterials.length > 0 && (
        <div>
          <h4 style={{ color: '#f3f4f6', fontSize: '16px', fontWeight: '600', marginBottom: '16px' }}>
            📁 Your Generated Materials
          </h4>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '12px'
          }}>
            {generatedMaterials.slice(0, 6).map(material => (
              <div key={material.id} style={{
                backgroundColor: 'rgba(55, 65, 81, 0.5)',
                borderRadius: '8px',
                padding: '16px',
                border: '1px solid #4b5563'
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                  <div style={{ color: '#f3f4f6', fontWeight: '600', fontSize: '14px' }}>
                    {material.name}
                  </div>
                  <div style={{ color: '#9ca3af', fontSize: '12px' }}>
                    {new Date(material.createdAt).toLocaleDateString()}
                  </div>
                </div>
                
                <div style={{ display: 'flex', gap: '8px', marginTop: '12px' }}>
                  <button
                    onClick={() => window.open(material.url, '_blank')}
                    style={{
                      flex: 1,
                      padding: '8px 12px',
                      backgroundColor: 'rgba(59, 130, 246, 0.2)',
                      border: '1px solid #3b82f6',
                      borderRadius: '6px',
                      color: '#60a5fa',
                      fontSize: '12px',
                      cursor: 'pointer'
                    }}
                  >
                    👁️ View
                  </button>
                  
                  <button
                    onClick={() => window.open(material.downloadUrl, '_blank')}
                    style={{
                      flex: 1,
                      padding: '8px 12px',
                      backgroundColor: 'rgba(16, 185, 129, 0.2)',
                      border: '1px solid #10b981',
                      borderRadius: '6px',
                      color: '#10b981',
                      fontSize: '12px',
                      cursor: 'pointer'
                    }}
                  >
                    📥 Download
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
