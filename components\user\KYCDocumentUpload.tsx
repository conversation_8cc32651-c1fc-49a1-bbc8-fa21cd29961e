import React, { useState, useRef, useCallback } from 'react';
import { supabase } from '../../lib/supabase';

interface KYCDocumentUploadProps {
  userId: number;
  onComplete?: (data: any) => void;
  onCancel?: () => void;
}

interface KYCFormData {
  // Personal Information
  firstName: string;
  lastName: string;
  idType: 'national_id' | 'passport' | 'drivers_license';
  idNumber: string;
  phoneNumber: string;
  emailAddress: string;
  
  // Address Information
  streetAddress: string;
  city: string;
  postalCode: string;
  countryCode: string;
  countryName: string;
  
  // Consent
  dataConsent: boolean;
  privacyPolicyAccepted: boolean;
}

interface DocumentFile {
  file: File;
  preview: string;
  type: 'id_document' | 'selfie' | 'address_proof';
}

export const KYCDocumentUpload: React.FC<KYCDocumentUploadProps> = ({
  userId,
  onComplete,
  onCancel
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<KYCFormData>({
    firstName: '',
    lastName: '',
    idType: 'national_id',
    idNumber: '',
    phoneNumber: '',
    emailAddress: '',
    streetAddress: '',
    city: '',
    postalCode: '',
    countryCode: 'ZAF',
    countryName: 'South Africa',
    dataConsent: false,
    privacyPolicyAccepted: false
  });
  
  const [documents, setDocuments] = useState<{
    idDocument?: DocumentFile;
    selfie?: DocumentFile;
    addressProof?: DocumentFile;
  }>({});
  
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const fileInputRefs = {
    idDocument: useRef<HTMLInputElement>(null),
    selfie: useRef<HTMLInputElement>(null),
    addressProof: useRef<HTMLInputElement>(null)
  };

  const countries = [
    { code: 'ZAF', name: 'South Africa' },
    { code: 'USA', name: 'United States' },
    { code: 'GBR', name: 'United Kingdom' },
    { code: 'CAN', name: 'Canada' },
    { code: 'AUS', name: 'Australia' },
    // Add more countries as needed
  ];

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    if (step === 1) {
      if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';
      if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';
      if (!formData.idNumber.trim()) newErrors.idNumber = 'ID number is required';
      if (!formData.phoneNumber.trim()) newErrors.phoneNumber = 'Phone number is required';
      if (!formData.emailAddress.trim()) newErrors.emailAddress = 'Email address is required';
      
      // Validate ID number format
      if (formData.idType === 'national_id' && formData.countryCode === 'ZAF') {
        if (!/^\d{13}$/.test(formData.idNumber)) {
          newErrors.idNumber = 'South African ID must be 13 digits';
        }
      } else if (formData.idType === 'passport') {
        if (!/^[A-Z0-9]{6,20}$/i.test(formData.idNumber)) {
          newErrors.idNumber = 'Invalid passport number format';
        }
      }
    }

    if (step === 2) {
      if (!formData.streetAddress.trim()) newErrors.streetAddress = 'Street address is required';
      if (!formData.city.trim()) newErrors.city = 'City is required';
      if (!formData.postalCode.trim()) newErrors.postalCode = 'Postal code is required';
    }

    if (step === 3) {
      if (!documents.idDocument) newErrors.idDocument = 'ID document is required';
      if (!documents.selfie) newErrors.selfie = 'Selfie is required';
      if (!documents.addressProof) newErrors.addressProof = 'Address proof is required';
    }

    if (step === 4) {
      if (!formData.dataConsent) newErrors.dataConsent = 'Data consent is required';
      if (!formData.privacyPolicyAccepted) newErrors.privacyPolicyAccepted = 'Privacy policy acceptance is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleFileUpload = useCallback((type: 'id_document' | 'selfie' | 'address_proof', file: File) => {
    // Validate file
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];

    if (file.size > maxSize) {
      setErrors(prev => ({ ...prev, [type]: 'File size must be less than 10MB' }));
      return;
    }

    if (!allowedTypes.includes(file.type)) {
      setErrors(prev => ({ ...prev, [type]: 'Only JPEG, PNG, and PDF files are allowed' }));
      return;
    }

    // Create preview
    const preview = URL.createObjectURL(file);
    
    setDocuments(prev => ({
      ...prev,
      [type]: { file, preview, type }
    }));

    // Clear error
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[type];
      return newErrors;
    });
  }, []);

  const handleSubmit = async () => {
    if (!validateStep(4)) return;

    try {
      setLoading(true);

      // Hash the ID number for duplicate checking
      const idNumberHash = await hashIdNumber(formData.idNumber);

      // Encrypt sensitive data (in production, this should be done server-side)
      const encryptedIdNumber = btoa(formData.idNumber); // Simple base64 encoding for demo

      // Insert KYC information
      const { data: kycData, error: kycError } = await supabase
        .from('kyc_information')
        .insert({
          user_id: userId,
          first_name: formData.firstName,
          last_name: formData.lastName,
          id_type: formData.idType,
          id_number_encrypted: encryptedIdNumber,
          id_number_hash: idNumberHash,
          phone_number: formData.phoneNumber,
          email_address: formData.emailAddress,
          street_address: formData.streetAddress,
          city: formData.city,
          postal_code: formData.postalCode,
          country_code: formData.countryCode,
          country_name: formData.countryName,
          data_consent_given: formData.dataConsent,
          privacy_policy_accepted: formData.privacyPolicyAccepted,
          kyc_status: 'pending'
        })
        .select()
        .single();

      if (kycError) throw kycError;

      // Upload documents (simplified - in production, use proper file storage)
      const documentUploads = [];
      
      for (const [type, doc] of Object.entries(documents)) {
        if (doc) {
          const fileName = `${userId}_${type}_${Date.now()}.${doc.file.name.split('.').pop()}`;
          const { error: uploadError } = await supabase.storage
            .from('kyc-documents')
            .upload(fileName, doc.file);

          if (uploadError) {
            console.error(`Error uploading ${type}:`, uploadError);
          } else {
            documentUploads.push({ type, fileName });
          }
        }
      }

      // Log KYC action
      await supabase.rpc('log_kyc_action', {
        p_kyc_id: kycData.id,
        p_user_id: userId,
        p_action: 'created',
        p_performed_by_username: 'web_dashboard'
      });

      onComplete?.(kycData);

    } catch (error) {
      console.error('Error submitting KYC:', error);
      setErrors({ submit: 'Failed to submit KYC information. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  const hashIdNumber = async (idNumber: string): Promise<string> => {
    const encoder = new TextEncoder();
    const data = encoder.encode(idNumber);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div>
            <h3 style={{ color: '#f59e0b', fontSize: '18px', fontWeight: 'bold', marginBottom: '20px' }}>
              📝 Personal Information
            </h3>
            
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '16px' }}>
              <div>
                <label style={{ display: 'block', color: '#f3f4f6', marginBottom: '8px', fontSize: '14px' }}>
                  First Name *
                </label>
                <input
                  type="text"
                  value={formData.firstName}
                  onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                  style={{
                    width: '100%',
                    padding: '12px',
                    backgroundColor: 'rgba(55, 65, 81, 0.5)',
                    border: errors.firstName ? '2px solid #ef4444' : '1px solid #4b5563',
                    borderRadius: '8px',
                    color: 'white',
                    fontSize: '14px'
                  }}
                />
                {errors.firstName && (
                  <div style={{ color: '#f87171', fontSize: '12px', marginTop: '4px' }}>
                    {errors.firstName}
                  </div>
                )}
              </div>

              <div>
                <label style={{ display: 'block', color: '#f3f4f6', marginBottom: '8px', fontSize: '14px' }}>
                  Last Name *
                </label>
                <input
                  type="text"
                  value={formData.lastName}
                  onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                  style={{
                    width: '100%',
                    padding: '12px',
                    backgroundColor: 'rgba(55, 65, 81, 0.5)',
                    border: errors.lastName ? '2px solid #ef4444' : '1px solid #4b5563',
                    borderRadius: '8px',
                    color: 'white',
                    fontSize: '14px'
                  }}
                />
                {errors.lastName && (
                  <div style={{ color: '#f87171', fontSize: '12px', marginTop: '4px' }}>
                    {errors.lastName}
                  </div>
                )}
              </div>

              <div>
                <label style={{ display: 'block', color: '#f3f4f6', marginBottom: '8px', fontSize: '14px' }}>
                  ID Type *
                </label>
                <select
                  value={formData.idType}
                  onChange={(e) => setFormData(prev => ({ ...prev, idType: e.target.value as 'national_id' | 'passport' }))}
                  style={{
                    width: '100%',
                    padding: '12px',
                    backgroundColor: 'rgba(55, 65, 81, 0.5)',
                    border: '1px solid #4b5563',
                    borderRadius: '8px',
                    color: 'white',
                    fontSize: '14px'
                  }}
                >
                  <option value="national_id">National ID</option>
                  <option value="passport">Passport</option>
                </select>
              </div>

              <div>
                <label style={{ display: 'block', color: '#f3f4f6', marginBottom: '8px', fontSize: '14px' }}>
                  {formData.idType === 'national_id' ? 'ID Number' : 'Passport Number'} *
                </label>
                <input
                  type="text"
                  value={formData.idNumber}
                  onChange={(e) => setFormData(prev => ({ ...prev, idNumber: e.target.value }))}
                  placeholder={formData.idType === 'national_id' ? '1234567890123' : 'A1234567'}
                  style={{
                    width: '100%',
                    padding: '12px',
                    backgroundColor: 'rgba(55, 65, 81, 0.5)',
                    border: errors.idNumber ? '2px solid #ef4444' : '1px solid #4b5563',
                    borderRadius: '8px',
                    color: 'white',
                    fontSize: '14px'
                  }}
                />
                {errors.idNumber && (
                  <div style={{ color: '#f87171', fontSize: '12px', marginTop: '4px' }}>
                    {errors.idNumber}
                  </div>
                )}
              </div>

              <div>
                <label style={{ display: 'block', color: '#f3f4f6', marginBottom: '8px', fontSize: '14px' }}>
                  Phone Number *
                </label>
                <input
                  type="tel"
                  value={formData.phoneNumber}
                  onChange={(e) => setFormData(prev => ({ ...prev, phoneNumber: e.target.value }))}
                  placeholder="+27 12 345 6789"
                  style={{
                    width: '100%',
                    padding: '12px',
                    backgroundColor: 'rgba(55, 65, 81, 0.5)',
                    border: errors.phoneNumber ? '2px solid #ef4444' : '1px solid #4b5563',
                    borderRadius: '8px',
                    color: 'white',
                    fontSize: '14px'
                  }}
                />
                {errors.phoneNumber && (
                  <div style={{ color: '#f87171', fontSize: '12px', marginTop: '4px' }}>
                    {errors.phoneNumber}
                  </div>
                )}
              </div>

              <div>
                <label style={{ display: 'block', color: '#f3f4f6', marginBottom: '8px', fontSize: '14px' }}>
                  Email Address *
                </label>
                <input
                  type="email"
                  value={formData.emailAddress}
                  onChange={(e) => setFormData(prev => ({ ...prev, emailAddress: e.target.value }))}
                  placeholder="<EMAIL>"
                  style={{
                    width: '100%',
                    padding: '12px',
                    backgroundColor: 'rgba(55, 65, 81, 0.5)',
                    border: errors.emailAddress ? '2px solid #ef4444' : '1px solid #4b5563',
                    borderRadius: '8px',
                    color: 'white',
                    fontSize: '14px'
                  }}
                />
                {errors.emailAddress && (
                  <div style={{ color: '#f87171', fontSize: '12px', marginTop: '4px' }}>
                    {errors.emailAddress}
                  </div>
                )}
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div>
            <h3 style={{ color: '#f59e0b', fontSize: '18px', fontWeight: 'bold', marginBottom: '20px' }}>
              🏠 Address Information
            </h3>
            
            <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '16px' }}>
              <div>
                <label style={{ display: 'block', color: '#f3f4f6', marginBottom: '8px', fontSize: '14px' }}>
                  Street Address *
                </label>
                <textarea
                  value={formData.streetAddress}
                  onChange={(e) => setFormData(prev => ({ ...prev, streetAddress: e.target.value }))}
                  placeholder="123 Main Street, Apartment 4B"
                  rows={3}
                  style={{
                    width: '100%',
                    padding: '12px',
                    backgroundColor: 'rgba(55, 65, 81, 0.5)',
                    border: errors.streetAddress ? '2px solid #ef4444' : '1px solid #4b5563',
                    borderRadius: '8px',
                    color: 'white',
                    fontSize: '14px',
                    resize: 'vertical'
                  }}
                />
                {errors.streetAddress && (
                  <div style={{ color: '#f87171', fontSize: '12px', marginTop: '4px' }}>
                    {errors.streetAddress}
                  </div>
                )}
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '16px' }}>
                <div>
                  <label style={{ display: 'block', color: '#f3f4f6', marginBottom: '8px', fontSize: '14px' }}>
                    City *
                  </label>
                  <input
                    type="text"
                    value={formData.city}
                    onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                    placeholder="Cape Town"
                    style={{
                      width: '100%',
                      padding: '12px',
                      backgroundColor: 'rgba(55, 65, 81, 0.5)',
                      border: errors.city ? '2px solid #ef4444' : '1px solid #4b5563',
                      borderRadius: '8px',
                      color: 'white',
                      fontSize: '14px'
                    }}
                  />
                  {errors.city && (
                    <div style={{ color: '#f87171', fontSize: '12px', marginTop: '4px' }}>
                      {errors.city}
                    </div>
                  )}
                </div>

                <div>
                  <label style={{ display: 'block', color: '#f3f4f6', marginBottom: '8px', fontSize: '14px' }}>
                    Postal Code *
                  </label>
                  <input
                    type="text"
                    value={formData.postalCode}
                    onChange={(e) => setFormData(prev => ({ ...prev, postalCode: e.target.value }))}
                    placeholder="8001"
                    style={{
                      width: '100%',
                      padding: '12px',
                      backgroundColor: 'rgba(55, 65, 81, 0.5)',
                      border: errors.postalCode ? '2px solid #ef4444' : '1px solid #4b5563',
                      borderRadius: '8px',
                      color: 'white',
                      fontSize: '14px'
                    }}
                  />
                  {errors.postalCode && (
                    <div style={{ color: '#f87171', fontSize: '12px', marginTop: '4px' }}>
                      {errors.postalCode}
                    </div>
                  )}
                </div>

                <div>
                  <label style={{ display: 'block', color: '#f3f4f6', marginBottom: '8px', fontSize: '14px' }}>
                    Country *
                  </label>
                  <select
                    value={formData.countryCode}
                    onChange={(e) => {
                      const country = countries.find(c => c.code === e.target.value);
                      setFormData(prev => ({
                        ...prev,
                        countryCode: e.target.value,
                        countryName: country?.name || ''
                      }));
                    }}
                    style={{
                      width: '100%',
                      padding: '12px',
                      backgroundColor: 'rgba(55, 65, 81, 0.5)',
                      border: '1px solid #4b5563',
                      borderRadius: '8px',
                      color: 'white',
                      fontSize: '14px'
                    }}
                  >
                    {countries.map(country => (
                      <option key={country.code} value={country.code}>
                        {country.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div>
            <h3 style={{ color: '#f59e0b', fontSize: '18px', fontWeight: 'bold', marginBottom: '20px' }}>
              📄 Document Upload
            </h3>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
              {/* ID Document Upload */}
              <div style={{
                backgroundColor: 'rgba(55, 65, 81, 0.5)',
                borderRadius: '8px',
                padding: '20px',
                border: errors.idDocument ? '2px solid #ef4444' : '1px solid #4b5563'
              }}>
                <h4 style={{ color: '#f3f4f6', fontSize: '16px', fontWeight: '600', marginBottom: '12px' }}>
                  🆔 {formData.idType === 'national_id' ? 'National ID' : 'Passport'} Document
                </h4>
                <p style={{ color: '#9ca3af', fontSize: '14px', marginBottom: '16px' }}>
                  Upload a clear photo of your {formData.idType === 'national_id' ? 'National ID' : 'Passport'}.
                  Make sure all text is readable and the image is not blurry.
                </p>

                <input
                  ref={fileInputRefs.idDocument}
                  type="file"
                  accept="image/*,.pdf"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) handleFileUpload('id_document', file);
                  }}
                  style={{ display: 'none' }}
                />

                <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                  <button
                    onClick={() => fileInputRefs.idDocument.current?.click()}
                    style={{
                      padding: '12px 24px',
                      backgroundColor: 'rgba(59, 130, 246, 0.2)',
                      border: '1px solid #3b82f6',
                      borderRadius: '8px',
                      color: '#60a5fa',
                      fontSize: '14px',
                      cursor: 'pointer'
                    }}
                  >
                    📁 Choose File
                  </button>

                  {documents.idDocument && (
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <span style={{ color: '#10b981', fontSize: '14px' }}>
                        ✅ {documents.idDocument.file.name}
                      </span>
                      <button
                        onClick={() => setDocuments(prev => ({ ...prev, idDocument: undefined }))}
                        style={{
                          padding: '4px 8px',
                          backgroundColor: 'rgba(239, 68, 68, 0.2)',
                          border: '1px solid #ef4444',
                          borderRadius: '4px',
                          color: '#f87171',
                          fontSize: '12px',
                          cursor: 'pointer'
                        }}
                      >
                        Remove
                      </button>
                    </div>
                  )}
                </div>

                {errors.idDocument && (
                  <div style={{ color: '#f87171', fontSize: '12px', marginTop: '8px' }}>
                    {errors.idDocument}
                  </div>
                )}
              </div>

              {/* Selfie Upload */}
              <div style={{
                backgroundColor: 'rgba(55, 65, 81, 0.5)',
                borderRadius: '8px',
                padding: '20px',
                border: errors.selfie ? '2px solid #ef4444' : '1px solid #4b5563'
              }}>
                <h4 style={{ color: '#f3f4f6', fontSize: '16px', fontWeight: '600', marginBottom: '12px' }}>
                  🤳 Selfie Verification
                </h4>
                <p style={{ color: '#9ca3af', fontSize: '14px', marginBottom: '16px' }}>
                  Take a clear selfie holding your ID document next to your face.
                  Ensure both your face and the ID document are clearly visible.
                </p>

                <input
                  ref={fileInputRefs.selfie}
                  type="file"
                  accept="image/*"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) handleFileUpload('selfie', file);
                  }}
                  style={{ display: 'none' }}
                />

                <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                  <button
                    onClick={() => fileInputRefs.selfie.current?.click()}
                    style={{
                      padding: '12px 24px',
                      backgroundColor: 'rgba(59, 130, 246, 0.2)',
                      border: '1px solid #3b82f6',
                      borderRadius: '8px',
                      color: '#60a5fa',
                      fontSize: '14px',
                      cursor: 'pointer'
                    }}
                  >
                    📷 Take/Upload Selfie
                  </button>

                  {documents.selfie && (
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <span style={{ color: '#10b981', fontSize: '14px' }}>
                        ✅ {documents.selfie.file.name}
                      </span>
                      <button
                        onClick={() => setDocuments(prev => ({ ...prev, selfie: undefined }))}
                        style={{
                          padding: '4px 8px',
                          backgroundColor: 'rgba(239, 68, 68, 0.2)',
                          border: '1px solid #ef4444',
                          borderRadius: '4px',
                          color: '#f87171',
                          fontSize: '12px',
                          cursor: 'pointer'
                        }}
                      >
                        Remove
                      </button>
                    </div>
                  )}
                </div>

                {errors.selfie && (
                  <div style={{ color: '#f87171', fontSize: '12px', marginTop: '8px' }}>
                    {errors.selfie}
                  </div>
                )}
              </div>

              {/* Address Proof Upload */}
              <div style={{
                backgroundColor: 'rgba(55, 65, 81, 0.5)',
                borderRadius: '8px',
                padding: '20px',
                border: errors.addressProof ? '2px solid #ef4444' : '1px solid #4b5563'
              }}>
                <h4 style={{ color: '#f3f4f6', fontSize: '16px', fontWeight: '600', marginBottom: '12px' }}>
                  🏠 Proof of Address
                </h4>
                <p style={{ color: '#9ca3af', fontSize: '14px', marginBottom: '16px' }}>
                  Upload a recent utility bill, bank statement, or official document showing your address.
                  The document must be dated within the last 3 months.
                </p>

                <input
                  ref={fileInputRefs.addressProof}
                  type="file"
                  accept="image/*,.pdf"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) handleFileUpload('address_proof', file);
                  }}
                  style={{ display: 'none' }}
                />

                <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                  <button
                    onClick={() => fileInputRefs.addressProof.current?.click()}
                    style={{
                      padding: '12px 24px',
                      backgroundColor: 'rgba(59, 130, 246, 0.2)',
                      border: '1px solid #3b82f6',
                      borderRadius: '8px',
                      color: '#60a5fa',
                      fontSize: '14px',
                      cursor: 'pointer'
                    }}
                  >
                    📁 Choose File
                  </button>

                  {documents.addressProof && (
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <span style={{ color: '#10b981', fontSize: '14px' }}>
                        ✅ {documents.addressProof.file.name}
                      </span>
                      <button
                        onClick={() => setDocuments(prev => ({ ...prev, addressProof: undefined }))}
                        style={{
                          padding: '4px 8px',
                          backgroundColor: 'rgba(239, 68, 68, 0.2)',
                          border: '1px solid #ef4444',
                          borderRadius: '4px',
                          color: '#f87171',
                          fontSize: '12px',
                          cursor: 'pointer'
                        }}
                      >
                        Remove
                      </button>
                    </div>
                  )}
                </div>

                {errors.addressProof && (
                  <div style={{ color: '#f87171', fontSize: '12px', marginTop: '8px' }}>
                    {errors.addressProof}
                  </div>
                )}
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div>
            <h3 style={{ color: '#f59e0b', fontSize: '18px', fontWeight: 'bold', marginBottom: '20px' }}>
              ✅ Review & Consent
            </h3>

            {/* Data Summary */}
            <div style={{
              backgroundColor: 'rgba(55, 65, 81, 0.5)',
              borderRadius: '8px',
              padding: '20px',
              marginBottom: '24px',
              border: '1px solid #4b5563'
            }}>
              <h4 style={{ color: '#f3f4f6', fontSize: '16px', fontWeight: '600', marginBottom: '16px' }}>
                📋 Information Summary
              </h4>

              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                gap: '12px',
                fontSize: '14px'
              }}>
                <div>
                  <span style={{ color: '#9ca3af' }}>Name:</span>
                  <div style={{ color: '#f3f4f6', fontWeight: '500' }}>
                    {formData.firstName} {formData.lastName}
                  </div>
                </div>
                <div>
                  <span style={{ color: '#9ca3af' }}>ID Type:</span>
                  <div style={{ color: '#f3f4f6', fontWeight: '500' }}>
                    {formData.idType === 'national_id' ? 'National ID' : 'Passport'}
                  </div>
                </div>
                <div>
                  <span style={{ color: '#9ca3af' }}>Country:</span>
                  <div style={{ color: '#f3f4f6', fontWeight: '500' }}>{formData.countryName}</div>
                </div>
                <div>
                  <span style={{ color: '#9ca3af' }}>Documents:</span>
                  <div style={{ color: '#f3f4f6', fontWeight: '500' }}>
                    {Object.keys(documents).length} uploaded
                  </div>
                </div>
              </div>
            </div>

            {/* Privacy Consent */}
            <div style={{
              backgroundColor: 'rgba(59, 130, 246, 0.1)',
              border: '1px solid rgba(59, 130, 246, 0.3)',
              borderRadius: '8px',
              padding: '20px',
              marginBottom: '20px'
            }}>
              <h4 style={{ color: '#60a5fa', fontSize: '16px', fontWeight: '600', marginBottom: '16px' }}>
                🔒 Privacy & Data Consent
              </h4>

              <div style={{ marginBottom: '16px' }}>
                <label style={{ display: 'flex', alignItems: 'flex-start', gap: '12px', cursor: 'pointer' }}>
                  <input
                    type="checkbox"
                    checked={formData.dataConsent}
                    onChange={(e) => setFormData(prev => ({ ...prev, dataConsent: e.target.checked }))}
                    style={{ marginTop: '2px' }}
                  />
                  <span style={{ color: '#d1d5db', fontSize: '14px', lineHeight: '1.6' }}>
                    I consent to the collection, processing, and storage of my personal information
                    for KYC verification and regulatory compliance purposes. I understand that this
                    information will be used to verify my identity and generate share certificates.
                  </span>
                </label>
                {errors.dataConsent && (
                  <div style={{ color: '#f87171', fontSize: '12px', marginTop: '4px', marginLeft: '24px' }}>
                    {errors.dataConsent}
                  </div>
                )}
              </div>

              <div>
                <label style={{ display: 'flex', alignItems: 'flex-start', gap: '12px', cursor: 'pointer' }}>
                  <input
                    type="checkbox"
                    checked={formData.privacyPolicyAccepted}
                    onChange={(e) => setFormData(prev => ({ ...prev, privacyPolicyAccepted: e.target.checked }))}
                    style={{ marginTop: '2px' }}
                  />
                  <span style={{ color: '#d1d5db', fontSize: '14px', lineHeight: '1.6' }}>
                    I have read and accept the{' '}
                    <a href="/privacy-policy" target="_blank" style={{ color: '#60a5fa', textDecoration: 'underline' }}>
                      Privacy Policy
                    </a>{' '}
                    and{' '}
                    <a href="/terms-of-service" target="_blank" style={{ color: '#60a5fa', textDecoration: 'underline' }}>
                      Terms of Service
                    </a>.
                  </span>
                </label>
                {errors.privacyPolicyAccepted && (
                  <div style={{ color: '#f87171', fontSize: '12px', marginTop: '4px', marginLeft: '24px' }}>
                    {errors.privacyPolicyAccepted}
                  </div>
                )}
              </div>
            </div>

            {/* Important Notice */}
            <div style={{
              backgroundColor: 'rgba(245, 158, 11, 0.1)',
              border: '1px solid rgba(245, 158, 11, 0.3)',
              borderRadius: '8px',
              padding: '16px'
            }}>
              <div style={{ color: '#f59e0b', fontSize: '14px', fontWeight: '600', marginBottom: '8px' }}>
                ⚠️ Important Notice
              </div>
              <p style={{ color: '#d1d5db', fontSize: '13px', lineHeight: '1.6', margin: 0 }}>
                Your KYC information will be reviewed within 24-48 hours. Once approved,
                your share certificates will be generated and sent to your registered email address.
                Please ensure all information is accurate as changes may require re-verification.
              </p>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.9)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '20px'
    }}>
      <div style={{
        backgroundColor: 'rgba(31, 41, 55, 0.95)',
        borderRadius: '16px',
        padding: '32px',
        border: '1px solid #374151',
        maxWidth: '800px',
        width: '100%',
        maxHeight: '90vh',
        overflow: 'auto'
      }}>
        {/* Header */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
          <h2 style={{ color: '#f59e0b', fontSize: '24px', fontWeight: 'bold', margin: 0 }}>
            🆔 KYC Verification
          </h2>
          <button
            onClick={onCancel}
            style={{
              padding: '8px',
              backgroundColor: 'transparent',
              border: 'none',
              color: '#9ca3af',
              fontSize: '20px',
              cursor: 'pointer'
            }}
          >
            ✕
          </button>
        </div>

        {/* Progress Indicator */}
        <div style={{ marginBottom: '32px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
            {[1, 2, 3, 4].map(step => (
              <div key={step} style={{
                flex: 1,
                textAlign: 'center',
                color: step <= currentStep ? '#f59e0b' : '#6b7280',
                fontSize: '12px',
                fontWeight: '600'
              }}>
                Step {step}
              </div>
            ))}
          </div>
          <div style={{
            width: '100%',
            height: '4px',
            backgroundColor: 'rgba(55, 65, 81, 0.5)',
            borderRadius: '2px'
          }}>
            <div style={{
              width: `${(currentStep / 4) * 100}%`,
              height: '100%',
              backgroundColor: '#f59e0b',
              borderRadius: '2px',
              transition: 'width 0.3s ease'
            }} />
          </div>
        </div>

        {/* Step Content */}
        {renderStep()}

        {/* Navigation Buttons */}
        <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '32px' }}>
          <button
            onClick={() => currentStep > 1 ? setCurrentStep(currentStep - 1) : onCancel?.()}
            style={{
              padding: '12px 24px',
              backgroundColor: 'transparent',
              border: '1px solid #4b5563',
              borderRadius: '8px',
              color: '#9ca3af',
              fontSize: '14px',
              cursor: 'pointer'
            }}
          >
            {currentStep > 1 ? '← Previous' : 'Cancel'}
          </button>

          <button
            onClick={() => {
              if (validateStep(currentStep)) {
                if (currentStep < 4) {
                  setCurrentStep(currentStep + 1);
                } else {
                  handleSubmit();
                }
              }
            }}
            disabled={loading}
            style={{
              padding: '12px 24px',
              backgroundColor: loading ? 'rgba(55, 65, 81, 0.5)' : '#3b82f6',
              border: 'none',
              borderRadius: '8px',
              color: 'white',
              fontSize: '14px',
              fontWeight: '600',
              cursor: loading ? 'not-allowed' : 'pointer'
            }}
          >
            {loading ? 'Submitting...' : currentStep < 4 ? 'Next →' : 'Submit KYC'}
          </button>
        </div>

        {errors.submit && (
          <div style={{
            backgroundColor: 'rgba(239, 68, 68, 0.1)',
            border: '1px solid rgba(239, 68, 68, 0.3)',
            borderRadius: '8px',
            padding: '12px',
            marginTop: '16px',
            color: '#f87171'
          }}>
            ❌ {errors.submit}
          </div>
        )}
      </div>
    </div>
  );
};
