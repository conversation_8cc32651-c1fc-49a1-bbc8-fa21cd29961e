// API endpoint to check Telegram connection status
import { createClient } from '@supabase/supabase-js';

// Use the correct environment variable names
const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';

console.log('🔧 Telegram Connection API - Environment check:');
console.log('URL:', supabaseUrl);
console.log('Key exists:', !!supabaseKey);

const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { user_id } = req.query;
    console.log('🔍 Checking Telegram connection for user:', user_id);

    if (!user_id) {
      console.log('❌ No user_id provided');
      return res.status(400).json({ error: 'User ID is required' });
    }

    const userIdNum = parseInt(user_id);
    if (isNaN(userIdNum)) {
      console.log('❌ Invalid user_id format:', user_id);
      return res.status(400).json({ error: 'Invalid user ID format' });
    }

    // Check if user has a linked Telegram account
    console.log('🔍 Querying telegram_users table for user_id:', userIdNum);
    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .select('id, user_id, telegram_id, username, first_name')
      .eq('user_id', userIdNum)
      .maybeSingle();

    if (telegramError) {
      console.error('❌ Database error checking Telegram connection:', telegramError);
      // Return success with no connection instead of error to prevent UI issues
      return res.status(200).json({
        success: true,
        isConnected: false,
        telegramUser: null,
        error: `Database query failed: ${telegramError.message}`
      });
    }

    console.log('✅ Telegram connection check result:', {
      isConnected: !!telegramUser,
      telegramUser: telegramUser ? `${telegramUser.username} (${telegramUser.telegram_id})` : null
    });

    // Return connection status
    res.status(200).json({
      success: true,
      isConnected: !!telegramUser,
      telegramUser: telegramUser || null
    });

  } catch (error) {
    console.error('❌ Telegram connection check exception:', error);
    // Return 200 with error info instead of 500 to prevent UI issues
    res.status(200).json({ 
      success: false,
      isConnected: false,
      telegramUser: null,
      error: `Internal server error: ${error.message}` 
    });
  }
}
