# Aureus Africa - Environment Variables Template
# Copy this file to .env and fill in your actual values
# NEVER commit .env files to version control

# =============================================================================
# SUPABASE CONFIGURATION (Required)
# =============================================================================

# Supabase Project URL - Get from your Supabase dashboard
VITE_SUPABASE_URL=https://your-project-id.supabase.co

# Supabase Anonymous Key (Public) - Safe for client-side
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.your-anon-key

# Supabase Service Role Key (Development Only - DO NOT use in production)
VITE_SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.your-service-role-key

# =============================================================================
# EMAIL SERVICE CONFIGURATION (Required)
# =============================================================================

# Resend API Key - Get from https://resend.com/api-keysdsdsd
VITE_RESEND_API_KEY=re_xxxxxxxxxxxxxxxxxxxxxxxxxx

# Email From Address - Must be verified in Resend
VITE_RESEND_FROM_EMAIL=<EMAIL>

# Email From Name - Display name for emails
VITE_RESEND_FROM_NAME=Aureus Alliance Holdings

# =============================================================================
# SERVER-SIDE ONLY VARIABLES (Not in client bundle)
# =============================================================================

# Server-side Supabase (for API routes)
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.your-service-role-key

# Database Direct Access
SUPABASE_DB_HOST=db.your-project-id.supabase.co
SUPABASE_DB_PORT=5432
SUPABASE_DB_NAME=postgres
SUPABASE_DB_USER=postgres
SUPABASE_DB_PASSWORD=your-database-password

# JWT Secret for token signing
JWT_SECRET=your-super-secret-jwt-key-here

# Server-side Email Configuration
RESEND_API_KEY=re_xxxxxxxxxxxxxxxxxxxxxxxxxx
RESEND_FROM_EMAIL=<EMAIL>
RESEND_FROM_NAME=Aureus Alliance Holdings

# =============================================================================
# OPTIONAL CONFIGURATION
# =============================================================================

# Application Metadata
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=development

# Email Settings
EMAIL_VERIFICATION_EXPIRY_MINUTES=15
EMAIL_VERIFICATION_MAX_ATTEMPTS=3
EMAIL_RATE_LIMIT_WINDOW_MINUTES=10

# Development Ports
PORT=8000
API_PORT=8002

# Debug Mode
VITE_DEBUG=false

# =============================================================================
# SETUP INSTRUCTIONS
# =============================================================================
# 1. Copy this file to .env
# 2. Replace placeholder values with real credentials
# 3. Get Supabase keys: https://supabase.com/dashboard
# 4. Get Resend API key: https://resend.com/api-keys
# 5. Run: npm run setup
