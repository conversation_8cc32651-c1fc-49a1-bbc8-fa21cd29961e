import React from 'react';
import { EnhancedGallery } from '../components/gallery/EnhancedGallery';

interface GalleryPageProps {
  onNavigate: (page: string) => void;
}

const GalleryPage: React.FC<GalleryPageProps> = ({ onNavigate }) => {
  return (
    <div className="page">
      {/* Page Header */}
      <section className="page-header">
        <div className="container">
          <div className="breadcrumb">
            <button onClick={() => onNavigate('home')} className="breadcrumb-link">
              Home
            </button>
            <span className="breadcrumb-separator">→</span>
            <span className="breadcrumb-current">Gallery</span>
          </div>
          
          <h1 className="page-title">Proof of Concept & Gallery</h1>
          <p className="page-subtitle">
            A visual journey through our on-site activities, team collaborations, and project milestones
          </p>
        </div>
      </section>

      {/* Gallery Introduction */}
      <section className="gallery-intro">
        <div className="container">
          <div className="intro-grid">
            <div className="intro-card">
              <div className="intro-icon">📸</div>
              <h3>Project Documentation</h3>
              <p>
                Real photos from our mining operations, showcasing the actual work being done 
                on the ground and the progress of our expansion plans.
              </p>
            </div>

            <div className="intro-card">
              <div className="intro-icon">🏗️</div>
              <h3>Site Development</h3>
              <p>
                Visual evidence of our mining site development, equipment deployment, 
                and infrastructure improvements across our operational areas.
              </p>
            </div>

            <div className="intro-card">
              <div className="intro-icon">👥</div>
              <h3>Team & Operations</h3>
              <p>
                Meet our team in action and see the day-to-day operations that drive 
                our gold production and shareholder value creation.
              </p>
            </div>

            <div className="intro-card">
              <div className="intro-icon">🌍</div>
              <h3>Environmental Responsibility</h3>
              <p>
                Documentation of our commitment to responsible mining practices and 
                environmental stewardship in all our operations.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Content */}
      <section className="gallery-content">
        <div className="container">
          <div className="gallery-wrapper">
            <EnhancedGallery
              showCategories={true}
              showSearch={true}
              itemsPerPage={12}
              showFeaturedFirst={true}
              fallbackMode="static"
              layout="grid"
            />
          </div>
        </div>
      </section>

      {/* Gallery Features */}
      <section className="gallery-features">
        <div className="container">
          <h2 className="section-title">Gallery Features</h2>
          <div className="features-grid">
            <div className="feature-card">
              <div className="feature-icon">🔍</div>
              <h4>High-Resolution Images</h4>
              <p>All images are captured in high resolution to provide clear, detailed views of our operations and progress.</p>
            </div>

            <div className="feature-card">
              <div className="feature-icon">📅</div>
              <h4>Chronological Organization</h4>
              <p>Images are organized by date and project phase, making it easy to track our development over time.</p>
            </div>

            <div className="feature-card">
              <div className="feature-icon">🏷️</div>
              <h4>Categorized Content</h4>
              <p>Browse by category including operations, equipment, team, environmental, and milestone achievements.</p>
            </div>

            <div className="feature-card">
              <div className="feature-icon">📱</div>
              <h4>Mobile Optimized</h4>
              <p>Gallery is fully responsive and optimized for viewing on all devices, from desktop to mobile.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="gallery-cta">
        <div className="container">
          <div className="cta-content">
            <h2>Ready to Be Part of Our Story?</h2>
            <p>
              Join thousands of shareholders who are already part of Aureus Alliance Holdings' 
              journey toward sustainable gold production and shared prosperity.
            </p>
            <div className="cta-buttons">
              <button 
                className="btn-primary"
                onClick={() => onNavigate('investment-phases')}
              >
                View Investment Phases
              </button>
              <button 
                className="btn-secondary"
                onClick={() => onNavigate('calculator')}
              >
                Calculate Returns
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Update Notification */}
      <section className="gallery-notification">
        <div className="container">
          <div className="notification-card">
            <div className="notification-icon">📸</div>
            <div className="notification-content">
              <h3>Regular Gallery Updates</h3>
              <p>
                We regularly add new photos to this gallery during our trips to Zimbabwe and other operational countries.
                These images serve as proof of our ongoing work and demonstrate that this is a legitimate, active business.
              </p>
              <div className="notification-meta">
                <span className="update-frequency">Updated: Weekly during operational visits</span>
                <span className="verification-badge">✓ Verified Operations</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default GalleryPage;
