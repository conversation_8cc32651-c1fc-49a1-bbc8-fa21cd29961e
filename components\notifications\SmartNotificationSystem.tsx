import React, { useState, useEffect } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';

interface SmartNotification {
  id: string;
  user_id: number;
  type: 'phase_transition' | 'commission_earned' | 'purchase_confirmation' | 'system_alert' | 'marketing_tip';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  title: string;
  message: string;
  action_url?: string;
  action_text?: string;
  is_read: boolean;
  is_dismissed: boolean;
  scheduled_for?: string;
  expires_at?: string;
  data: any;
  created_at: string;
}

interface NotificationPreferences {
  email_enabled: boolean;
  push_enabled: boolean;
  telegram_enabled: boolean;
  phase_transitions: boolean;
  commission_updates: boolean;
  marketing_tips: boolean;
  system_alerts: boolean;
  quiet_hours_start?: string;
  quiet_hours_end?: string;
}

interface SmartNotificationSystemProps {
  userId: number;
  showPreferences?: boolean;
}

export const SmartNotificationSystem: React.FC<SmartNotificationSystemProps> = ({
  userId,
  showPreferences = false
}) => {
  const [notifications, setNotifications] = useState<SmartNotification[]>([]);
  const [preferences, setPreferences] = useState<NotificationPreferences | null>(null);
  const [loading, setLoading] = useState(true);
  const [unreadCount, setUnreadCount] = useState(0);
  const [showNotifications, setShowNotifications] = useState(false);
  const [filter, setFilter] = useState<'all' | 'unread' | 'high_priority'>('all');

  useEffect(() => {
    loadNotifications();
    loadPreferences();
    
    // Set up real-time subscription
    const subscription = supabase
      .channel('notifications')
      .on('postgres_changes', 
        { 
          event: 'INSERT', 
          schema: 'public', 
          table: 'smart_notifications',
          filter: `user_id=eq.${userId}`
        }, 
        (payload) => {
          const newNotification = payload.new as SmartNotification;
          setNotifications(prev => [newNotification, ...prev]);
          setUnreadCount(prev => prev + 1);
          
          // Show browser notification if enabled
          if (preferences?.push_enabled && 'Notification' in window) {
            showBrowserNotification(newNotification);
          }
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [userId, preferences?.push_enabled]);

  const loadNotifications = async () => {
    try {
      const serviceClient = getServiceRoleClient()
      const { data, error } = await serviceClient
        .from('smart_notifications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) throw error;

      setNotifications(data || []);
      setUnreadCount(data?.filter(n => !n.is_read).length || 0);

    } catch (error) {
      console.error('Error loading notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadPreferences = async () => {
    try {
      const { data, error } = await supabase
        .from('notification_preferences')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      setPreferences(data || {
        email_enabled: true,
        push_enabled: true,
        telegram_enabled: true,
        phase_transitions: true,
        commission_updates: true,
        marketing_tips: true,
        system_alerts: true
      });

    } catch (error) {
      console.error('Error loading preferences:', error);
    }
  };

  const showBrowserNotification = (notification: SmartNotification) => {
    if (Notification.permission === 'granted') {
      const browserNotification = new Notification(notification.title, {
        body: notification.message,
        icon: '/icons/icon-192x192.png',
        badge: '/icons/badge-72x72.png',
        tag: notification.id,
        requireInteraction: notification.priority === 'urgent'
      });

      browserNotification.onclick = () => {
        if (notification.action_url) {
          window.open(notification.action_url, '_blank');
        }
        browserNotification.close();
      };

      // Auto-close after 5 seconds for non-urgent notifications
      if (notification.priority !== 'urgent') {
        setTimeout(() => browserNotification.close(), 5000);
      }
    }
  };

  const markAsRead = async (notificationId: string) => {
    try {
      await supabase
        .from('smart_notifications')
        .update({ is_read: true })
        .eq('id', notificationId)
        .eq('user_id', userId);

      setNotifications(prev =>
        prev.map(n => n.id === notificationId ? { ...n, is_read: true } : n)
      );
      setUnreadCount(prev => Math.max(0, prev - 1));

    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const markAllAsRead = async () => {
    try {
      await supabase
        .from('smart_notifications')
        .update({ is_read: true })
        .eq('user_id', userId)
        .eq('is_read', false);

      setNotifications(prev =>
        prev.map(n => ({ ...n, is_read: true }))
      );
      setUnreadCount(0);

    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  const dismissNotification = async (notificationId: string) => {
    try {
      await supabase
        .from('smart_notifications')
        .update({ is_dismissed: true })
        .eq('id', notificationId)
        .eq('user_id', userId);

      setNotifications(prev =>
        prev.filter(n => n.id !== notificationId)
      );

    } catch (error) {
      console.error('Error dismissing notification:', error);
    }
  };

  const updatePreferences = async (newPreferences: Partial<NotificationPreferences>) => {
    try {
      const updatedPreferences = { ...preferences, ...newPreferences };
      
      await supabase
        .from('notification_preferences')
        .upsert({
          user_id: userId,
          ...updatedPreferences,
          updated_at: new Date().toISOString()
        });

      setPreferences(updatedPreferences);

    } catch (error) {
      console.error('Error updating preferences:', error);
    }
  };

  const requestNotificationPermission = async () => {
    if ('Notification' in window && Notification.permission === 'default') {
      const permission = await Notification.requestPermission();
      if (permission === 'granted') {
        await updatePreferences({ push_enabled: true });
      }
    }
  };

  const getNotificationIcon = (type: string, priority: string) => {
    const icons = {
      phase_transition: '🚀',
      commission_earned: '💰',
      purchase_confirmation: '✅',
      system_alert: priority === 'urgent' ? '🚨' : '📢',
      marketing_tip: '💡'
    };
    return icons[type as keyof typeof icons] || '📬';
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      low: '#9ca3af',
      medium: '#60a5fa',
      high: '#f59e0b',
      urgent: '#ef4444'
    };
    return colors[priority as keyof typeof colors] || '#9ca3af';
  };

  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'unread') return !notification.is_read;
    if (filter === 'high_priority') return ['high', 'urgent'].includes(notification.priority);
    return true;
  });

  return (
    <div style={{ position: 'relative' }}>
      {/* Notification Bell */}
      <button
        onClick={() => setShowNotifications(!showNotifications)}
        style={{
          position: 'relative',
          padding: '8px',
          backgroundColor: 'transparent',
          border: 'none',
          borderRadius: '8px',
          color: '#9ca3af',
          fontSize: '20px',
          cursor: 'pointer',
          transition: 'color 0.2s ease'
        }}
        onMouseEnter={(e) => e.currentTarget.style.color = '#f59e0b'}
        onMouseLeave={(e) => e.currentTarget.style.color = '#9ca3af'}
      >
        🔔
        {unreadCount > 0 && (
          <span style={{
            position: 'absolute',
            top: '2px',
            right: '2px',
            backgroundColor: '#ef4444',
            color: 'white',
            borderRadius: '50%',
            width: '18px',
            height: '18px',
            fontSize: '10px',
            fontWeight: 'bold',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </button>

      {/* Notification Dropdown */}
      {showNotifications && (
        <div style={{
          position: 'absolute',
          top: '100%',
          right: 0,
          width: '400px',
          maxWidth: '90vw',
          backgroundColor: 'rgba(31, 41, 55, 0.95)',
          border: '1px solid #374151',
          borderRadius: '12px',
          boxShadow: '0 10px 25px rgba(0, 0, 0, 0.3)',
          zIndex: 1000,
          backdropFilter: 'blur(10px)'
        }}>
          {/* Header */}
          <div style={{
            padding: '16px',
            borderBottom: '1px solid #374151',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <h4 style={{ color: '#f59e0b', fontSize: '16px', fontWeight: 'bold', margin: 0 }}>
              🔔 Notifications
            </h4>
            <div style={{ display: 'flex', gap: '8px' }}>
              {unreadCount > 0 && (
                <button
                  onClick={markAllAsRead}
                  style={{
                    padding: '4px 8px',
                    backgroundColor: 'rgba(59, 130, 246, 0.2)',
                    border: '1px solid #3b82f6',
                    borderRadius: '4px',
                    color: '#60a5fa',
                    fontSize: '10px',
                    cursor: 'pointer'
                  }}
                >
                  Mark All Read
                </button>
              )}
              <button
                onClick={() => setShowNotifications(false)}
                style={{
                  padding: '4px 8px',
                  backgroundColor: 'transparent',
                  border: 'none',
                  color: '#9ca3af',
                  fontSize: '14px',
                  cursor: 'pointer'
                }}
              >
                ✕
              </button>
            </div>
          </div>

          {/* Filter Tabs */}
          <div style={{
            padding: '12px 16px',
            borderBottom: '1px solid #374151',
            display: 'flex',
            gap: '8px'
          }}>
            {[
              { key: 'all', label: 'All' },
              { key: 'unread', label: 'Unread' },
              { key: 'high_priority', label: 'Priority' }
            ].map(tab => (
              <button
                key={tab.key}
                onClick={() => setFilter(tab.key as any)}
                style={{
                  padding: '6px 12px',
                  backgroundColor: filter === tab.key ? 'rgba(59, 130, 246, 0.2)' : 'transparent',
                  border: filter === tab.key ? '1px solid #3b82f6' : '1px solid #4b5563',
                  borderRadius: '6px',
                  color: filter === tab.key ? '#60a5fa' : '#9ca3af',
                  fontSize: '12px',
                  cursor: 'pointer'
                }}
              >
                {tab.label}
              </button>
            ))}
          </div>

          {/* Notifications List */}
          <div style={{
            maxHeight: '400px',
            overflowY: 'auto'
          }}>
            {loading ? (
              <div style={{ padding: '20px', textAlign: 'center', color: '#9ca3af' }}>
                Loading notifications...
              </div>
            ) : filteredNotifications.length === 0 ? (
              <div style={{ padding: '40px 20px', textAlign: 'center', color: '#9ca3af' }}>
                <div style={{ fontSize: '32px', marginBottom: '8px' }}>📭</div>
                <div style={{ fontSize: '14px' }}>No notifications</div>
              </div>
            ) : (
              filteredNotifications.map(notification => (
                <div
                  key={notification.id}
                  style={{
                    padding: '12px 16px',
                    borderBottom: '1px solid #374151',
                    backgroundColor: notification.is_read ? 'transparent' : 'rgba(59, 130, 246, 0.05)',
                    cursor: 'pointer'
                  }}
                  onClick={() => !notification.is_read && markAsRead(notification.id)}
                >
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '4px' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <span style={{ fontSize: '16px' }}>
                        {getNotificationIcon(notification.type, notification.priority)}
                      </span>
                      <span style={{
                        color: '#f3f4f6',
                        fontSize: '14px',
                        fontWeight: notification.is_read ? 'normal' : 'bold'
                      }}>
                        {notification.title}
                      </span>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                      <div style={{
                        width: '6px',
                        height: '6px',
                        borderRadius: '50%',
                        backgroundColor: getPriorityColor(notification.priority)
                      }} />
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          dismissNotification(notification.id);
                        }}
                        style={{
                          padding: '2px',
                          backgroundColor: 'transparent',
                          border: 'none',
                          color: '#6b7280',
                          fontSize: '12px',
                          cursor: 'pointer'
                        }}
                      >
                        ✕
                      </button>
                    </div>
                  </div>
                  
                  <p style={{
                    color: '#9ca3af',
                    fontSize: '13px',
                    margin: '0 0 8px 24px',
                    lineHeight: '1.4'
                  }}>
                    {notification.message}
                  </p>
                  
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginLeft: '24px' }}>
                    <span style={{ color: '#6b7280', fontSize: '11px' }}>
                      {new Date(notification.created_at).toLocaleString()}
                    </span>
                    {notification.action_url && (
                      <a
                        href={notification.action_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{
                          color: '#60a5fa',
                          fontSize: '11px',
                          textDecoration: 'none'
                        }}
                        onClick={(e) => e.stopPropagation()}
                      >
                        {notification.action_text || 'View'} →
                      </a>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Footer */}
          {showPreferences && (
            <div style={{
              padding: '12px 16px',
              borderTop: '1px solid #374151',
              textAlign: 'center'
            }}>
              <button
                onClick={() => {/* Open preferences modal */}}
                style={{
                  padding: '6px 12px',
                  backgroundColor: 'rgba(245, 158, 11, 0.2)',
                  border: '1px solid #f59e0b',
                  borderRadius: '6px',
                  color: '#f59e0b',
                  fontSize: '12px',
                  cursor: 'pointer'
                }}
              >
                ⚙️ Notification Settings
              </button>
            </div>
          )}
        </div>
      )}

      {/* Request Permission Banner */}
      {preferences?.push_enabled && 'Notification' in window && Notification.permission === 'default' && (
        <div style={{
          position: 'fixed',
          top: '20px',
          right: '20px',
          backgroundColor: 'rgba(59, 130, 246, 0.9)',
          border: '1px solid #3b82f6',
          borderRadius: '8px',
          padding: '12px 16px',
          color: 'white',
          fontSize: '14px',
          zIndex: 1001,
          maxWidth: '300px'
        }}>
          <div style={{ marginBottom: '8px' }}>
            🔔 Enable browser notifications to stay updated!
          </div>
          <button
            onClick={requestNotificationPermission}
            style={{
              padding: '6px 12px',
              backgroundColor: 'white',
              border: 'none',
              borderRadius: '4px',
              color: '#3b82f6',
              fontSize: '12px',
              cursor: 'pointer',
              marginRight: '8px'
            }}
          >
            Enable
          </button>
          <button
            onClick={() => updatePreferences({ push_enabled: false })}
            style={{
              padding: '6px 12px',
              backgroundColor: 'transparent',
              border: '1px solid white',
              borderRadius: '4px',
              color: 'white',
              fontSize: '12px',
              cursor: 'pointer'
            }}
          >
            Later
          </button>
        </div>
      )}
    </div>
  );
};
