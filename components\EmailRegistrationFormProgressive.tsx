import React, { useState, useEffect } from 'react'
import { registerUserProgressive, supabase } from '../lib/supabase'
import { validatePasswordStrength } from '../lib/passwordSecurity'
import { realtimeValidation, ValidationResult } from '../lib/realtimeValidation'
import { ValidationInput } from './ValidationFeedback'
import { API_BASE_URL } from '../constants'

interface EmailRegistrationFormProps {
  onRegistrationSuccess: (user: any) => void
  onSwitchToLogin: () => void
  onSwitchToTelegram?: () => void
}

// Progressive form steps
type RegistrationStep = 'email' | 'username' | 'complete'

export const EmailRegistrationFormProgressive: React.FC<EmailRegistrationFormProps> = ({
  onRegistrationSuccess,
  onSwitchToLogin,
  onSwitchToTelegram
}) => {
  // Progressive form state
  const [currentStep, setCurrentStep] = useState<RegistrationStep>('email')
  const [registrationMode, setRegistrationMode] = useState<'new' | 'telegram'>('new')
  const [generalError, setGeneralError] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Simplified form data (removed phone, country, telegram, fullName fields)
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    username: '',
    sponsorUsername: '',
    campaignSource: ''
  })

  // Email verification state
  const [emailVerification, setEmailVerification] = useState({
    isVerified: false,
    isModalOpen: false,
    tempUserId: null as number | null,
    isEmailValid: false,
    verificationCode: undefined as string | undefined,
    codeExpiry: undefined as number | undefined
  })

  // Real-time validation states
  const [emailValidation, setEmailValidation] = useState<ValidationResult | null>(null)
  const [usernameValidation, setUsernameValidation] = useState<ValidationResult | null>(null)

  // Password visibility states (default to visible)
  const [showPassword, setShowPassword] = useState(true)
  const [showConfirmPassword, setShowConfirmPassword] = useState(true)
  const [sponsorValidation, setSponsorValidation] = useState<ValidationResult | null>(null)
  const [errors, setErrors] = useState<{ [key: string]: string }>({})

  // Handle input changes with real-time validation
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))

    // Clear errors when user starts typing
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[name]
        return newErrors
      })
    }

    // Real-time validation for email
    if (name === 'email') {
      realtimeValidation.validateEmail(value, setEmailValidation)
      setEmailVerification(prev => ({
        ...prev,
        isEmailValid: /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value.trim())
      }))
    }

    // Real-time validation for username
    if (name === 'username') {
      realtimeValidation.validateUsername(value, setUsernameValidation)
    }

    // Real-time validation for sponsor username
    if (name === 'sponsorUsername' && value.trim().length > 0) {
      realtimeValidation.validateSponsorUsername(value.trim(), setSponsorValidation)
    } else if (name === 'sponsorUsername') {
      setSponsorValidation(null)
    }
  }

  // Handle email verification
  const handleEmailVerification = async (email: string) => {
    try {
      setGeneralError('')
      console.log('🔍 Starting email verification process for:', email)

      // Validate email format
      const emailValueRaw = email.trim()
      if (!emailValueRaw || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailValueRaw)) {
        setErrors(prev => ({ ...prev, email: 'Please enter a valid email address' }))
        return
      }

      // Check for duplicates
      const resp = await fetch(`${API_BASE_URL}/api/check-duplicates`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: emailValueRaw.toLowerCase() })
      })
      const dup = await resp.json()
      if (resp.ok && dup.emailDuplicate) {
        setErrors(prev => ({ ...prev, email: 'This email address is already registered. Please use a different email or try logging in.' }))
        return
      }

      // Generate verification code
      const verificationCode = Math.floor(100000 + Math.random() * 900000).toString()
      setEmailVerification(prev => ({
        ...prev,
        tempUserId: null,
        isModalOpen: true,
        verificationCode,
        codeExpiry: Date.now() + (15 * 60 * 1000) // 15 minutes
      }))

      // Send verification email
      const response = await fetch(`${API_BASE_URL}/api/send-verification-email`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: email,
          code: verificationCode,
          purpose: 'registration',
          userName: 'User',
          expiryMinutes: 15
        }),
      })

      const responseData = await response.json()
      if (!response.ok) {
        console.error('❌ API Error:', responseData)
        setGeneralError(`Failed to send verification email: ${responseData.error || 'Server error'}`)
        return
      }

      console.log('✅ Verification email sent successfully')
    } catch (error) {
      console.error('Email verification error:', error)
      setGeneralError(`Failed to send verification email: ${error instanceof Error ? error.message : 'Network error'}`)
    }
  }

  // Handle email verification success
  const handleEmailVerificationSuccess = () => {
    setEmailVerification(prev => ({
      ...prev,
      isVerified: true,
      isModalOpen: false,
      tempUserId: null,
      verificationCode: undefined,
      codeExpiry: undefined
    }))
    // Advance to username step after email verification
    setCurrentStep('username')
  }

  // Handle username validation completion
  const handleUsernameValidated = () => {
    // Advance to complete step after username is validated
    setCurrentStep('complete')
  }

  // Handle verification code submission
  const handleVerificationCodeSubmit = async (code: string) => {
    if (!emailVerification.verificationCode || !emailVerification.codeExpiry) {
      setGeneralError('No verification code found. Please request a new one.')
      return
    }

    if (Date.now() > emailVerification.codeExpiry) {
      setGeneralError('Verification code has expired. Please request a new one.')
      return
    }

    if (code === emailVerification.verificationCode) {
      handleEmailVerificationSuccess()
    } else {
      setGeneralError('Invalid verification code. Please try again.')
    }
  }

  const handleEmailVerificationClose = () => {
    setEmailVerification(prev => ({
      ...prev,
      isModalOpen: false,
      tempUserId: null,
      verificationCode: undefined,
      codeExpiry: undefined
    }))
  }

  // Form validation
  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {}

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Invalid email format'
    } else if (!emailVerification.isVerified) {
      newErrors.email = 'Please verify your email address first'
    }

    // Username validation
    if (!formData.username.trim()) {
      newErrors.username = 'Username is required'
    } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username) || formData.username.length < 3) {
      newErrors.username = 'Username must be at least 3 characters and contain only letters, numbers, and underscores'
    }



    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required'
    } else {
      const passwordValidation = validatePasswordStrength(formData.password)
      if (!passwordValidation.valid) {
        newErrors.password = passwordValidation.errors[0]
      }
    }

    // Confirm password validation
    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match'
    }

    // Sponsor username validation
    if (!formData.sponsorUsername.trim()) {
      newErrors.sponsorUsername = 'Sponsor username is required'
    } else if (sponsorValidation && !sponsorValidation.isValid) {
      newErrors.sponsorUsername = 'Please enter a valid sponsor username'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setGeneralError('')

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      // Register user with simplified progressive registration
      const userData = {
        email: formData.email.toLowerCase().trim(),
        password: formData.password,
        confirmPassword: formData.confirmPassword,
        username: formData.username.toLowerCase().trim(),
        sponsorUsername: formData.sponsorUsername.toLowerCase().trim(),
        campaignSource: formData.campaignSource || 'website'
      }

      const result = await registerUserProgressive(userData)

      if (result.success && result.user) {
        console.log('✅ Registration successful:', result.user)
        onRegistrationSuccess(result.user)
      } else {
        // Ensure error is a string, not an object
        const errorMessage = typeof result.error === 'string'
          ? result.error
          : result.error?.message || 'Registration failed. Please try again.'
        setGeneralError(errorMessage)
      }
    } catch (error) {
      console.error('❌ Registration error:', error)
      setGeneralError('Registration failed. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="bg-gray-900/50 backdrop-blur-xl rounded-3xl p-8 border border-gray-700/30 shadow-2xl">
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center">
          <h3 className="text-2xl font-bold text-white mb-2">
            Create Your Account
          </h3>
          <p className="text-gray-400">
            Join Aureus Alliance Holdings and start your gold share ownership journey
          </p>
        </div>

        {/* Progress Indicator */}
        <div className="flex items-center justify-center space-x-4 mb-8">
          <div className={`flex items-center space-x-2 ${currentStep === 'email' ? 'text-yellow-400' : currentStep === 'username' || currentStep === 'complete' ? 'text-green-400' : 'text-gray-500'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center border-2 ${currentStep === 'email' ? 'border-yellow-400 bg-yellow-400/20' : currentStep === 'username' || currentStep === 'complete' ? 'border-green-400 bg-green-400/20' : 'border-gray-500'}`}>
              {currentStep === 'username' || currentStep === 'complete' ? '✓' : '1'}
            </div>
            <span className="text-sm font-medium">Email</span>
          </div>
          <div className={`w-8 h-0.5 ${currentStep === 'username' || currentStep === 'complete' ? 'bg-green-400' : 'bg-gray-600'}`}></div>
          <div className={`flex items-center space-x-2 ${currentStep === 'username' ? 'text-yellow-400' : currentStep === 'complete' ? 'text-green-400' : 'text-gray-500'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center border-2 ${currentStep === 'username' ? 'border-yellow-400 bg-yellow-400/20' : currentStep === 'complete' ? 'border-green-400 bg-green-400/20' : 'border-gray-500'}`}>
              {currentStep === 'complete' ? '✓' : '2'}
            </div>
            <span className="text-sm font-medium">Username</span>
          </div>
          <div className={`w-8 h-0.5 ${currentStep === 'complete' ? 'bg-green-400' : 'bg-gray-600'}`}></div>
          <div className={`flex items-center space-x-2 ${currentStep === 'complete' ? 'text-yellow-400' : 'text-gray-500'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center border-2 ${currentStep === 'complete' ? 'border-yellow-400 bg-yellow-400/20' : 'border-gray-500'}`}>
              3
            </div>
            <span className="text-sm font-medium">Complete</span>
          </div>
        </div>

        {/* Telegram Integration Question - Only show on email step */}
        {currentStep === 'email' && (
          <div className="bg-blue-900/20 border border-blue-500/30 rounded-xl p-6 mb-6">
            <h4 className="text-lg font-semibold text-blue-300 mb-4">
              📱 Telegram Bot Integration
            </h4>
            <p className="text-gray-300 mb-4">
              Have you already used our Telegram bot (@AureusAllianceBot) to purchase shares or earn commissions?
            </p>
            <div className="space-y-3">
              <label className="flex items-center space-x-3 cursor-pointer">
                <input
                  type="radio"
                  name="registrationMode"
                  value="new"
                  checked={registrationMode === 'new'}
                  onChange={(e) => setRegistrationMode(e.target.value as 'new' | 'telegram')}
                  className="w-4 h-4 text-blue-500 bg-gray-800 border-gray-600 focus:ring-blue-500"
                />
                <span className="text-white">No, I'm new to Aureus Alliance</span>
              </label>
              <label className="flex items-center space-x-3 cursor-pointer">
                <input
                  type="radio"
                  name="registrationMode"
                  value="telegram"
                  checked={registrationMode === 'telegram'}
                  onChange={(e) => {
                    const value = e.target.value as 'new' | 'telegram';
                    setRegistrationMode(value);

                    // If user selects Telegram mode and we have a callback, switch to Telegram tab
                    if (value === 'telegram' && onSwitchToTelegram) {
                      console.log('🔄 User selected Telegram mode, switching to Telegram tab');
                      onSwitchToTelegram();
                    }
                  }}
                  className="w-4 h-4 text-blue-500 bg-gray-800 border-gray-600 focus:ring-blue-500"
                />
                <span className="text-white">Yes, I've used the Telegram bot before</span>
              </label>
            </div>
          </div>
        )}

        {/* Progressive Form Steps */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Step 1: Email Validation */}
          {currentStep === 'email' && (
            <div className="space-y-6">
              <div className="text-center">
                <h4 className="text-xl font-semibold text-white mb-2">Step 1: Email Verification</h4>
                <p className="text-gray-400">Enter your email address to get started</p>
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-semibold text-gray-300 mb-3">
                  Email Address <span className="text-red-400">*</span>
                </label>
                <ValidationInput
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  validation={emailValidation}
                  placeholder="Enter your email address"
                  autoComplete="email"
                  className="w-full px-4 py-4 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200"
                />

                {/* Send Code Button */}
                {registrationMode === 'new' && emailVerification.isEmailValid && !emailVerification.isVerified && (
                  <div className="mt-4 flex justify-center">
                    <button
                      type="button"
                      onClick={() => handleEmailVerification(formData.email)}
                      className="bg-yellow-600 hover:bg-yellow-500 disabled:bg-gray-600 disabled:cursor-not-allowed text-white text-sm font-medium px-6 py-3 rounded-lg transition-colors duration-200 shadow-sm"
                      disabled={
                        !emailVerification.isEmailValid ||
                        !!errors.email ||
                        (emailValidation && (!emailValidation.isValid || emailValidation.isChecking))
                      }
                    >
                      Send Verification Code
                    </button>
                  </div>
                )}

                {registrationMode === 'new' && emailVerification.isEmailValid && !emailVerification.isVerified && (
                  <p className="mt-2 text-sm text-yellow-400 text-center">
                    📧 Click "Send Verification Code" to receive a 6-digit code via email
                  </p>
                )}

                {registrationMode === 'new' && emailVerification.isVerified && (
                  <p className="mt-2 text-sm text-green-400 text-center">
                    ✅ Email verified successfully! You can now proceed to the next step.
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Step 2: Username Selection */}
          {currentStep === 'username' && (
            <div className="space-y-6">
              <div className="text-center">
                <h4 className="text-xl font-semibold text-white mb-2">Step 2: Choose Username</h4>
                <p className="text-gray-400">Select a unique username for your account</p>
              </div>

              <div>
                <label htmlFor="username" className="block text-sm font-semibold text-gray-300 mb-3">
                  Username <span className="text-red-400">*</span>
                </label>
                <ValidationInput
                  id="username"
                  name="username"
                  type="text"
                  value={formData.username}
                  onChange={handleInputChange}
                  validation={usernameValidation}
                  placeholder="Enter your username"
                  autoComplete="username"
                  className="w-full px-4 py-4 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200"
                />

                {/* Continue Button */}
                {usernameValidation && usernameValidation.isValid && (
                  <div className="mt-4 flex justify-center">
                    <button
                      type="button"
                      onClick={handleUsernameValidated}
                      className="bg-yellow-600 hover:bg-yellow-500 text-white text-sm font-medium px-6 py-3 rounded-lg transition-colors duration-200 shadow-sm"
                    >
                      Continue to Final Step
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Step 3: Complete Registration */}
          {currentStep === 'complete' && (
            <div className="space-y-6">
              <div className="text-center">
                <h4 className="text-xl font-semibold text-white mb-2">Step 3: Complete Registration</h4>
                <p className="text-gray-400">Create your password and link to your sponsor</p>
              </div>

              {/* Password */}
              <div>
                <label htmlFor="password" className="block text-sm font-semibold text-gray-300 mb-3">
                  Password <span className="text-red-400">*</span>
                </label>
                <div className="relative">
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    value={formData.password}
                    onChange={handleInputChange}
                    className="w-full px-4 py-4 pr-12 bg-gray-800/60 border border-gray-600/50 rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200"
                    placeholder="Create a strong password"
                    autoComplete="new-password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors duration-200"
                  >
                    {showPassword ? (
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 11-4.243-4.243m4.242 4.242L9.88 9.88" />
                      </svg>
                    ) : (
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="mt-2 text-sm text-red-400">{errors.password}</p>
                )}
              </div>

              {/* Confirm Password */}
              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-semibold text-gray-300 mb-3">
                  Confirm Password <span className="text-red-400">*</span>
                </label>
                <div className="relative">
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    className="w-full px-4 py-4 pr-12 bg-gray-800/60 border border-gray-600/50 rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200"
                    placeholder="Confirm your password"
                    autoComplete="new-password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors duration-200"
                  >
                    {showConfirmPassword ? (
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 11-4.243-4.243m4.242 4.242L9.88 9.88" />
                      </svg>
                    ) : (
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    )}
                  </button>
                </div>
                {errors.confirmPassword && (
                  <p className="mt-2 text-sm text-red-400">{errors.confirmPassword}</p>
                )}
              </div>

              {/* Sponsor Username */}
              <div>
                <label htmlFor="sponsorUsername" className="block text-sm font-semibold text-gray-300 mb-3">
                  Sponsor Username <span className="text-red-400">*</span>
                </label>
                <ValidationInput
                  id="sponsorUsername"
                  name="sponsorUsername"
                  type="text"
                  value={formData.sponsorUsername}
                  onChange={handleInputChange}
                  validation={sponsorValidation}
                  placeholder="Enter your sponsor's username"
                  className="w-full px-4 py-4 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200"
                />
                <p className="mt-2 text-sm text-gray-400">
                  This links your account to your sponsor for affiliate tracking
                </p>
              </div>

              {/* Submit Button */}
              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-500 hover:to-yellow-400 disabled:from-gray-600 disabled:to-gray-600 disabled:cursor-not-allowed text-black font-bold py-4 px-8 rounded-xl transition-all duration-200 shadow-lg text-lg"
              >
                {isSubmitting ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-black"></div>
                    <span>Creating Account...</span>
                  </div>
                ) : (
                  <span>Create Account</span>
                )}
              </button>
            </div>
          )}
        </form>

        {/* General Error Display */}
        {generalError && (
          <div className="bg-red-900/50 border border-red-500 rounded-lg p-4">
            <p className="text-red-400 text-sm">{generalError}</p>
          </div>
        )}

        {/* Switch to Login */}
        <div className="text-center">
          <p className="text-gray-300 mb-4">Already have an account?</p>
          <button
            onClick={onSwitchToLogin}
            className="text-yellow-400 hover:text-yellow-300 font-semibold transition-colors duration-200"
          >
            Sign in instead
          </button>
        </div>
      </div>

      {/* Email Verification Modal */}
      {emailVerification.isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-xl p-6 max-w-md w-full mx-4">
            <h3 className="text-xl font-semibold text-white mb-4">
              📧 Verify Your Email Address
            </h3>
            <p className="text-gray-300 mb-6">
              We've sent a 6-digit verification code to <strong>{formData.email}</strong>.
              Please enter it below to continue with your registration.
            </p>

            {/* Show general error if any */}
            {generalError && (
              <div className="mb-4 p-3 bg-red-900/50 border border-red-500 rounded-lg">
                <p className="text-red-400 text-sm">{generalError}</p>
              </div>
            )}

            <VerificationCodeInput
              onCodeSubmit={handleVerificationCodeSubmit}
              onClose={handleEmailVerificationClose}
              onResendCode={() => handleEmailVerification(formData.email)}
            />
          </div>
        </div>
      )}
    </div>
  )
}

// Simple verification code input component
const VerificationCodeInput: React.FC<{
  onCodeSubmit: (code: string) => void
  onClose: () => void
  onResendCode: () => void
}> = ({ onCodeSubmit, onClose, onResendCode }) => {
  const [code, setCode] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (code.length !== 6) {
      return
    }

    setIsSubmitting(true)
    onCodeSubmit(code)
    setIsSubmitting(false)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 6)
    setCode(value)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Verification Code
        </label>
        <input
          type="text"
          value={code}
          onChange={handleInputChange}
          placeholder="Enter 6-digit code"
          className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white text-center text-lg tracking-widest focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
          maxLength={6}
          autoComplete="off"
        />
      </div>

      <div className="flex space-x-2">
        <button
          type="button"
          onClick={onClose}
          className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
        >
          Cancel
        </button>
        <button
          type="button"
          onClick={onResendCode}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm"
        >
          Resend
        </button>
        <button
          type="submit"
          disabled={code.length !== 6 || isSubmitting}
          className="flex-1 px-4 py-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
        >
          {isSubmitting ? 'Verifying...' : 'Verify'}
        </button>
      </div>
    </form>
  )
}
