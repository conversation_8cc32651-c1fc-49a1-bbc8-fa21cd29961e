# ============================================================================
# AUREUS ALLIANCE HOLDINGS - COMPREHENSIVE .GITIGNORE
# ============================================================================
# This file protects sensitive information and follows security best practices
# for a Next.js/React application with Supabase integration.
#
# SECURITY WARNING: Never commit files containing:
# - Database credentials, API keys, service role keys
# - User data, financial information, or personal data
# - Authentication tokens, passwords, or certificates
# ============================================================================

# ============================================================================
# 🔐 ENVIRONMENT VARIABLES & SENSITIVE CONFIGURATION
# ============================================================================
# These files contain sensitive data like Supabase service role keys,
# database URLs, API keys, and other credentials that must NEVER be committed

# All environment files
.env
.env.local
.env.development
.env.production
.env.test
.env.*.local

# Environment file backups and examples with real data
.env.backup
.env.bak
.env.orig

# Configuration files that might contain sensitive data
config/production.json
config/development.json
config/local.json
secrets.json
credentials.json

# ============================================================================
# 🗄️ DATABASE & SUPABASE SPECIFIC
# ============================================================================
# Protect database dumps, migration files with sensitive data, and Supabase configs

# Database dumps and backups
*.sql
*.dump
*.backup
database/
db_backups/
migrations/data/

# Supabase local development files
supabase/.env
supabase/config.toml
.supabase/

# ============================================================================
# 📦 NODE.JS & NPM/YARN
# ============================================================================
# Standard Node.js exclusions for dependencies and package manager files

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Package manager lock files (keep only one type)
# Uncomment the ones you want to exclude:
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# NPM/Yarn cache and temporary files
.npm
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# ============================================================================
# 🏗️ BUILD OUTPUTS & GENERATED FILES
# ============================================================================
# Exclude all build artifacts and generated files

# Next.js build outputs
.next/
out/
build/
dist/
dist-ssr/

# Static file generation
.vercel/
.netlify/

# TypeScript build info
*.tsbuildinfo
next-env.d.ts

# ============================================================================
# 📝 LOGS & DEBUGGING
# ============================================================================
# Exclude all log files that might contain sensitive information

# Application logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime logs
.pnpm-debug.log*

# Diagnostic reports
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# ============================================================================
# 🔧 DEVELOPMENT TOOLS & IDE
# ============================================================================
# IDE and editor specific files that might contain sensitive paths or configs

# Visual Studio Code
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json

# JetBrains IDEs
.idea/
*.swp
*.swo
*~

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
.vim/
*.swp
*.swo

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc

# ============================================================================
# 🖥️ OPERATING SYSTEM FILES
# ============================================================================
# OS-specific files that should not be tracked

# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
<<<<<<< HEAD

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ============================================================================
# 🔒 SECURITY & CERTIFICATES
# ============================================================================
# Exclude all security-related files and certificates

# SSL certificates and private keys
*.pem
*.key
*.crt
*.cert
*.p12
*.pfx
*.jks
*.keystore

# SSH keys
id_rsa
id_rsa.pub
id_ed25519
id_ed25519.pub
known_hosts

# GPG keys
*.gpg
*.asc

# ============================================================================
# 💰 FINANCIAL & USER DATA
# ============================================================================
# Protect any files that might contain financial or user data

# Data exports and backups
exports/
backups/
user_data/
financial_data/
*.csv
*.xlsx
*.xls

# Test data with real information
test_data/
sample_data/
mock_data/

# ============================================================================
# 🧪 TESTING & COVERAGE
# ============================================================================
# Test outputs and coverage reports

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output

# Jest
jest-coverage/

# Cypress
cypress/videos/
cypress/screenshots/

# ============================================================================
# 📱 MOBILE & NATIVE
# ============================================================================
# Mobile development files (if applicable)

# React Native
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.orig.*
web-build/

# ============================================================================
# 🔄 TEMPORARY & CACHE FILES
# ============================================================================
# Various temporary and cache files

# Temporary folders
tmp/
temp/
.tmp/
.temp/

# Cache directories
.cache/
.parcel-cache/
.eslintcache
.stylelintcache

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# ============================================================================
# 📊 ANALYTICS & MONITORING
# ============================================================================
# Analytics and monitoring files that might contain sensitive data

# Error tracking
.sentry/
sentry.properties

# Analytics
analytics/
metrics/

# ============================================================================
# 🚀 DEPLOYMENT & CI/CD
# ============================================================================
# Deployment and CI/CD files with sensitive information

# Deployment scripts with credentials
deploy.sh
deployment/
.deploy/

# Docker files with secrets
docker-compose.override.yml
.dockerignore

# Kubernetes secrets
k8s-secrets/
*.secret.yaml

# ============================================================================
# 🔐 AUREUS-SPECIFIC SENSITIVE FILES
# ============================================================================
# Project-specific files that contain sensitive Aureus Alliance Holdings data

# Bot configuration files
aureus-bot-new.js.backup
bot-config.json
telegram-bot-config.json

# Database schema exports with real data
currentdatabase.json.backup
schema-with-data.sql

# User certificates and generated documents
certificates/
generated-certificates/
user-documents/

# Commission and financial calculation files
commission-calculations/
financial-reports/
payment-records/

# ============================================================================
# END OF .GITIGNORE
# ============================================================================
# Remember to regularly review this file and update it as the project evolves.
# Always test that sensitive files are properly excluded before committing.
# Use 'git status' to verify no sensitive files are staged for commit.
#
# SECURITY CHECKLIST:
# ✅ Environment files (.env*) are excluded
# ✅ Database credentials and API keys are protected
# ✅ User data and financial information is excluded
# ✅ Certificates and private keys are protected
# ✅ Build artifacts and logs are excluded
# ============================================================================
=======
.env
>>>>>>> main
