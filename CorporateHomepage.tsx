import React, { useState } from 'react';
import HomePage from './pages/HomePage';
import InvestmentPhasesPage from './pages/InvestmentPhasesPage';
import CalculatorPage from './pages/CalculatorPage';
import FinancialDataPage from './pages/FinancialDataPage';
import MineProductionPage from './pages/MineProductionPage';
import GalleryPage from './pages/GalleryPage';
import CSRPage from './pages/CSRPage';
import './styles/ModernWebsite.css';

// Modern Multi-Page Website for Aureus Alliance Holdings
const CorporateHomepage: React.FC = () => {
    const [currentPage, setCurrentPage] = useState<string>('home');

    const handleNavigate = (page: string) => {
        setCurrentPage(page);
        window.scrollTo(0, 0);
    };

    return (
        <div className="modern-website" style={{
            background: '#FFFFFF',
            color: '#1F2937',
            fontFamily: 'Inter, Segoe UI, system-ui, -apple-system, sans-serif',
            lineHeight: '1.6',
            letterSpacing: 'normal',
            fontSize: '1rem',
            minHeight: '100vh'
        }}>
            {/* Header */}
            <header className="clean-header" style={{
                background: '#FFFFFF',
                borderBottom: '1px solid #E5E7EB',
                padding: '1rem 0',
                position: 'sticky',
                top: 0,
                zIndex: 1000,
                boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)'
            }}>
                <div className="container" style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 1rem' }}>
                    <div className="header-content" style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center'
                    }}>
                        <div className="logo">
                            <h1 style={{
                                fontSize: '1.25rem',
                                fontWeight: '700',
                                color: '#D4AF37',
                                marginBottom: '0.25rem',
                                letterSpacing: 'normal'
                            }}>Aureus Alliance Holdings</h1>
                            <p style={{
                                fontSize: '0.875rem',
                                color: '#4B5563',
                                fontWeight: '500',
                                letterSpacing: 'normal'
                            }}>Real Gold • Real Shares • Real Ownership</p>
                        </div>
                        <nav className="nav-menu">
                            <button
                                className={`nav-item ${currentPage === 'home' ? 'active' : ''}`}
                                onClick={() => handleNavigate('home')}
                            >
                                Home
                            </button>
                            <button
                                className={`nav-item ${currentPage === 'investment-phases' ? 'active' : ''}`}
                                onClick={() => handleNavigate('investment-phases')}
                            >
                                Investment Phases
                            </button>
                            <button
                                className={`nav-item ${currentPage === 'calculator' ? 'active' : ''}`}
                                onClick={() => handleNavigate('calculator')}
                            >
                                Calculator
                            </button>
                            <button
                                className={`nav-item ${currentPage === 'financial-data' ? 'active' : ''}`}
                                onClick={() => handleNavigate('financial-data')}
                            >
                                Financial Data
                            </button>
                            <button
                                className={`nav-item ${currentPage === 'mine-production' ? 'active' : ''}`}
                                onClick={() => handleNavigate('mine-production')}
                            >
                                Mine Production
                            </button>
                            <button
                                className={`nav-item ${currentPage === 'gallery' ? 'active' : ''}`}
                                onClick={() => handleNavigate('gallery')}
                            >
                                Gallery
                            </button>
                            <button className="cta-btn" onClick={() => window.location.href = '/purchase-shares'}>
                                Purchase Shares
                            </button>
                        </nav>
                    </div>
                </div>
            </header>

            {/* Page Content */}
            <main>
                {currentPage === 'home' && <HomePage onNavigate={handleNavigate} />}
                {currentPage === 'investment-phases' && <InvestmentPhasesPage onNavigate={handleNavigate} />}
                {currentPage === 'calculator' && <CalculatorPage onNavigate={handleNavigate} />}
                {currentPage === 'financial-data' && <FinancialDataPage onNavigate={handleNavigate} />}
                {currentPage === 'mine-production' && <MineProductionPage onNavigate={handleNavigate} />}
                {currentPage === 'gallery' && <GalleryPage onNavigate={handleNavigate} />}
                {currentPage === 'csr' && <CSRPage onNavigate={handleNavigate} />}
                {currentPage === 'community-impact' && <CSRPage onNavigate={handleNavigate} />}
                {(currentPage === 'expansion-plan' || currentPage === 'company-info') && (
                    <div className="page">
                        <section className="page-header">
                            <div className="container">
                                <div className="breadcrumb">
                                    <button onClick={() => handleNavigate('home')} className="breadcrumb-link">
                                        Home
                                    </button>
                                    <span className="breadcrumb-separator">→</span>
                                    <span className="breadcrumb-current">
                                        {currentPage === 'expansion-plan' ? 'Expansion Plan' : 'Company Information'}
                                    </span>
                                </div>
                                <h1 className="page-title">
                                    {currentPage === 'expansion-plan' ? 'Expansion Plan' : 'Company Information'}
                                </h1>
                                <p className="page-subtitle">
                                    {currentPage === 'expansion-plan'
                                        ? '5-year growth strategy scaling to 200+ plants across Africa'
                                        : 'Corporate details, team, history, and operational background'
                                    }
                                </p>
                            </div>
                        </section>
                        <section className="content-section">
                            <div className="container">
                                <div className="coming-soon-card">
                                    <div className="coming-soon-icon">🚧</div>
                                    <h2>Coming Soon</h2>
                                    <p>This section is currently under development. Please check back soon for detailed information.</p>
                                    <button
                                        className="btn btn-primary"
                                        onClick={() => handleNavigate('home')}
                                    >
                                        Return to Home
                                    </button>
                                </div>
                            </div>
                        </section>
                    </div>
                )}
            </main>

            {/* Footer */}
            <footer className="clean-footer">
                <div className="container">
                    <div className="footer-content">
                        <div className="footer-info">
                            <h3>Aureus Alliance Holdings (Pty) Ltd</h3>
                            <p>CIPC-registered gold mining company • Real shares backed by real gold production</p>
                        </div>
                        <div className="footer-links">
                            <button onClick={() => alert('Coming Soon')}>Privacy Policy</button>
                            <button onClick={() => alert('Coming Soon')}>Terms & Conditions</button>
                            <button onClick={() => alert('Coming Soon')}>Legal Disclaimer</button>
                            <button
                                onClick={() => window.location.href = '/affiliate'}
                                style={{ color: '#D4AF37', textDecoration: 'underline' }}
                            >
                                Become an Affiliate
                            </button>
                        </div>
                    </div>
                    <div className="footer-bottom">
                        <p>&copy; 2024 Aureus Alliance Holdings (Pty) Ltd. All rights reserved.</p>
                    </div>
                </div>
            </footer>
        </div>
    );
};

export default CorporateHomepage;
