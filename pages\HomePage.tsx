import React from 'react';

interface HomePageProps {
  onNavigate: (page: string) => void;
}

const HomePage: React.FC<HomePageProps> = ({ onNavigate }) => {
  // Key metrics from aureus.md
  const keyMetrics = [
    { value: "1.4M", label: "Total Shares Available" },
    { value: "$5.00", label: "Presale Price" },
    { value: "200K", label: "Presale Shares" },
    { value: "2026", label: "Operations Start" },
    { value: "$15-$50", label: "First Dividend April 2026" }
  ];

  return (
    <div className="page">
      {/* Hero Section */}
      <section className="hero">
        <div className="container">
          <div className="hero-content">
            <h1 className="hero-title">
              Real Gold • Real Shares • Real Ownership
            </h1>
            <p className="hero-subtitle">
              Aureus Alliance Holdings offers real gold equity through CIPC-registered shares, 
              giving shareholders full legal ownership in a structured, professionally managed 
              mining venture built on trust, security, and long-term value.
            </p>
            
            {/* Key Metrics */}
            <div className="metrics-grid">
              {keyMetrics.map((metric, index) => (
                <div key={index} className="metric-card">
                  <div className="metric-value">{metric.value}</div>
                  <div className="metric-label">{metric.label}</div>
                </div>
              ))}
            </div>

            {/* CTA Buttons */}
            <div className="cta-buttons">
              <button 
                className="btn btn-primary"
                onClick={() => onNavigate('investment-phases')}
              >
                View Investment Phases
              </button>
              <button 
                className="btn btn-secondary"
                onClick={() => onNavigate('calculator')}
              >
                Calculate Returns
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Company Overview */}
      <section className="overview">
        <div className="container">
          <div className="section-header">
            <h2>About Aureus Alliance Holdings</h2>
            <p>Building gold-backed impact ventures across Africa</p>
          </div>

          <div className="overview-grid">
            <div className="overview-card">
              <div className="card-icon">🏗️</div>
              <h3>Operations Timeline</h3>
              <ul>
                <li><strong>January 2026:</strong> Operations begin with 10 wash plants</li>
                <li><strong>April 2026:</strong> First dividend payout ($15-$50 per share)</li>
                <li><strong>2030:</strong> 200+ plants across 6,000 hectares</li>
                <li><strong>Multi-Country:</strong> Zimbabwe, Zambia, Ghana, Tanzania, South Africa</li>
              </ul>
            </div>

            <div className="overview-card">
              <div className="card-icon">💰</div>
              <h3>Financial Projections</h3>
              <ul>
                <li><strong>2026:</strong> 3.5 tons gold output, $145/share dividend</li>
                <li><strong>2030:</strong> 108+ tons gold output, $4,000/share dividend</li>
                <li><strong>Recovery Rate:</strong> 70% projected, targeting 95%</li>
                <li><strong>Gold Price:</strong> $109,026 per kg current</li>
              </ul>
            </div>

            <div className="overview-card">
              <div className="card-icon">🤝</div>
              <h3>Community Impact</h3>
              <ul>
                <li><strong>1,000,000+</strong> children fed through programs</li>
                <li><strong>30,000+</strong> school scholarships funded</li>
                <li><strong>200+</strong> water boreholes installed</li>
                <li><strong>50+</strong> rural schools built</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Trust Indicators */}
      <section className="trust-section">
        <div className="container">
          <div className="section-header">
            <h2>Why Choose Aureus Alliance Holdings</h2>
            <p>Established credibility and proven commitment</p>
          </div>

          <div className="trust-grid">
            <div className="trust-item">
              <div className="trust-icon">🏛️</div>
              <div className="trust-content">
                <h4>CIPC Registered</h4>
                <p>Legally registered company with full regulatory compliance</p>
              </div>
            </div>
            <div className="trust-item">
              <div className="trust-icon">🔒</div>
              <div className="trust-content">
                <h4>Blockchain Secured</h4>
                <p>NFT share certificates for transparent ownership verification</p>
              </div>
            </div>
            <div className="trust-item">
              <div className="trust-icon">🌍</div>
              <div className="trust-content">
                <h4>6+ Years Experience</h4>
                <p>Proven track record in Zimbabwe gold mining operations</p>
              </div>
            </div>
            <div className="trust-item">
              <div className="trust-icon">♻️</div>
              <div className="trust-content">
                <h4>Eco-Friendly Operations</h4>
                <p>Renewable energy powered sustainable mining practices</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery & Video Section */}
      <section className="gallery-section">
        <div className="container">
          <div className="section-header">
            <h2>See Our Operations in Action</h2>
            <p>Visual proof of our commitment to sustainable gold mining</p>
          </div>

          <div className="media-grid">
            {/* Featured Video */}
            <div className="featured-video">
              <div className="video-placeholder">
                <div className="video-icon">▶️</div>
                <h3>Aureus Operations Overview</h3>
                <p>Watch our eco-friendly wash plants in action and see how we're transforming gold mining in Africa</p>
                <button className="play-button">Watch Video</button>
              </div>
            </div>

            {/* Gallery Images */}
            <div className="gallery-grid">
              <div className="gallery-item">
                <div className="image-placeholder">
                  <span>🏭</span>
                  <p>Wash Plant Operations</p>
                </div>
              </div>
              <div className="gallery-item">
                <div className="image-placeholder">
                  <span>🌱</span>
                  <p>Land Rehabilitation</p>
                </div>
              </div>
              <div className="gallery-item">
                <div className="image-placeholder">
                  <span>👥</span>
                  <p>Community Engagement</p>
                </div>
              </div>
              <div className="gallery-item">
                <div className="image-placeholder">
                  <span>🏥</span>
                  <p>CSR Initiatives</p>
                </div>
              </div>
              <div className="gallery-item">
                <div className="image-placeholder">
                  <span>🎓</span>
                  <p>Education Programs</p>
                </div>
              </div>
              <div className="gallery-item">
                <div className="image-placeholder">
                  <span>💧</span>
                  <p>Clean Water Projects</p>
                </div>
              </div>
            </div>
          </div>

          <div className="gallery-cta">
            <button
              className="btn btn-secondary"
              onClick={() => onNavigate('mine-production')}
            >
              Learn About Our Technology
            </button>
            <button
              className="btn btn-secondary"
              onClick={() => onNavigate('csr')}
            >
              View CSR Impact
            </button>
          </div>
        </div>
      </section>

      {/* Quick Links */}
      <section className="quick-links">
        <div className="container">
          <div className="section-header">
            <h2>Explore Our Offering</h2>
            <p>Comprehensive information about our mining venture</p>
          </div>

          <div className="links-grid">
            <div className="link-card" onClick={() => onNavigate('investment-phases')}>
              <div className="link-icon">📊</div>
              <h3>Investment Phases</h3>
              <p>Complete breakdown of all 20 phases from $5 to $1,000 per share</p>
              <span className="link-arrow">→</span>
            </div>

            <div className="link-card" onClick={() => onNavigate('expansion-plan')}>
              <div className="link-icon">🚀</div>
              <h3>Expansion Plan</h3>
              <p>5-year growth strategy scaling to 200+ plants across Africa</p>
              <span className="link-arrow">→</span>
            </div>

            <div className="link-card" onClick={() => onNavigate('community-impact')}>
              <div className="link-icon">🌍</div>
              <h3>Community Impact</h3>
              <p>CSR initiatives feeding 1M+ children and building infrastructure</p>
              <span className="link-arrow">→</span>
            </div>

            <div className="link-card" onClick={() => onNavigate('calculator')}>
              <div className="link-icon">🧮</div>
              <h3>Returns Calculator</h3>
              <p>Calculate your potential dividends and investment returns</p>
              <span className="link-arrow">→</span>
            </div>

            <div className="link-card" onClick={() => onNavigate('financial-data')}>
              <div className="link-icon">📈</div>
              <h3>Financial Data</h3>
              <p>Detailed projections, yield tables, and production forecasts</p>
              <span className="link-arrow">→</span>
            </div>

            <div className="link-card" onClick={() => onNavigate('company-info')}>
              <div className="link-icon">🏢</div>
              <h3>Company Information</h3>
              <p>Corporate details, team, history, and operational background</p>
              <span className="link-arrow">→</span>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
