import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import FacialRecognitionKYC from './FacialRecognitionKYC';

interface KYCFormData {
  firstName: string;
  lastName: string;
  idType: 'national_id' | 'passport' | 'drivers_license';
  idNumber: string;
  phoneNumber: string;
  emailAddress: string;
  streetAddress: string;
  city: string;
  postalCode: string;
  countryCode: string;
  countryName: string;
  dataConsent: boolean;
  privacyPolicyAccepted: boolean;
}

interface DocumentFile {
  file: File;
  preview: string;
  type: 'id_document' | 'proof_of_residence';
  uploaded?: boolean;
  url?: string;
}

interface KYCVerificationFormProps {
  userId: number;
  onKYCComplete?: (status: string) => void;
  existingKYC?: any;
}

export const KYCVerificationForm: React.FC<KYCVerificationFormProps> = ({
  userId,
  onKYCComplete,
  existingKYC
}) => {
  const [formData, setFormData] = useState<KYCFormData>({
    firstName: existingKYC?.first_name || '',
    lastName: existingKYC?.last_name || '',
    idType: existingKYC?.id_type || 'national_id',
    idNumber: '',
    phoneNumber: existingKYC?.phone_number || '',
    emailAddress: existingKYC?.email_address || '',
    streetAddress: existingKYC?.street_address || '',
    city: existingKYC?.city || '',
    postalCode: existingKYC?.postal_code || '',
    countryCode: existingKYC?.country_code || 'ZAF',
    countryName: existingKYC?.country_name || 'South Africa',
    dataConsent: existingKYC?.data_consent_given || false,
    privacyPolicyAccepted: existingKYC?.privacy_policy_accepted || false
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isEditing, setIsEditing] = useState(!existingKYC);
  const [documents, setDocuments] = useState<{
    id_document?: DocumentFile;
    proof_of_residence?: DocumentFile;
  }>({});
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const [showFacialRecognition, setShowFacialRecognition] = useState(false);
  const [facialRecognitionResults, setFacialRecognitionResults] = useState<any>(null);

  const countries = [
    { code: 'ZAF', name: 'South Africa' },
    { code: 'USA', name: 'United States' },
    { code: 'GBR', name: 'United Kingdom' },
    { code: 'CAN', name: 'Canada' },
    { code: 'AUS', name: 'Australia' },
    { code: 'DEU', name: 'Germany' },
    { code: 'FRA', name: 'France' },
    { code: 'NLD', name: 'Netherlands' },
    { code: 'CHE', name: 'Switzerland' },
    { code: 'SWE', name: 'Sweden' }
  ];

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';
    if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';
    if (!formData.idNumber.trim()) newErrors.idNumber = 'ID/Passport number is required';
    if (!formData.phoneNumber.trim()) newErrors.phoneNumber = 'Phone number is required';
    if (!formData.emailAddress.trim()) newErrors.emailAddress = 'Email address is required';
    if (!formData.streetAddress.trim()) newErrors.streetAddress = 'Street address is required';
    if (!formData.city.trim()) newErrors.city = 'City is required';
    if (!formData.postalCode.trim()) newErrors.postalCode = 'Postal code is required';
    if (!formData.dataConsent) newErrors.dataConsent = 'Data consent is required';
    if (!formData.privacyPolicyAccepted) newErrors.privacyPolicyAccepted = 'Privacy policy acceptance is required';

    // Document validation
    if (!documents.id_document && !existingKYC) {
      newErrors.id_document = 'ID/Passport document is required';
    }
    if (!documents.proof_of_residence && !existingKYC) {
      newErrors.proof_of_residence = 'Proof of residence is required';
    }

    // Facial recognition validation
    if (!facialRecognitionResults && !existingKYC) {
      newErrors.facial_recognition = 'Facial recognition verification is required';
    } else if (facialRecognitionResults && facialRecognitionResults.confidence_score < 0.7) {
      newErrors.facial_recognition = 'Facial recognition confidence too low. Please retry verification.';
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (formData.emailAddress && !emailRegex.test(formData.emailAddress)) {
      newErrors.emailAddress = 'Please enter a valid email address';
    }

    // Phone validation (basic)
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    if (formData.phoneNumber && !phoneRegex.test(formData.phoneNumber.replace(/\s/g, ''))) {
      newErrors.phoneNumber = 'Please enter a valid phone number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleFileUpload = (type: 'id_document' | 'proof_of_residence', file: File) => {
    // Validate file
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];

    if (file.size > maxSize) {
      setErrors(prev => ({ ...prev, [type]: 'File size must be less than 10MB' }));
      return;
    }

    if (!allowedTypes.includes(file.type)) {
      setErrors(prev => ({ ...prev, [type]: 'Only JPEG, PNG, and PDF files are allowed' }));
      return;
    }

    // Create preview
    const preview = URL.createObjectURL(file);

    setDocuments(prev => ({
      ...prev,
      [type]: { file, preview, type }
    }));

    // Clear error
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[type];
      return newErrors;
    });
  };

  const uploadDocument = async (type: 'id_document' | 'proof_of_residence', file: File): Promise<string> => {
    const fileExt = file.name.split('.').pop();
    const timestamp = Date.now();
    const fileName = `kyc_${type}_${userId}_${timestamp}.${fileExt}`;

    setUploadProgress(prev => ({ ...prev, [type]: 0 }));

    try {
      const { data, error } = await supabase.storage
        .from('proof')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) throw error;

      setUploadProgress(prev => ({ ...prev, [type]: 100 }));

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('proof')
        .getPublicUrl(fileName);

      return publicUrl;
    } catch (error) {
      console.error(`Error uploading ${type}:`, error);
      throw new Error(`Failed to upload ${type}: ${error.message}`);
    }
  };

  const hashIdNumber = async (idNumber: string): Promise<string> => {
    const encoder = new TextEncoder();
    const data = encoder.encode(idNumber);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      // Upload documents first
      const documentUrls: Record<string, string> = {};

      for (const [type, doc] of Object.entries(documents)) {
        if (doc && doc.file) {
          try {
            const url = await uploadDocument(type as 'id_document' | 'proof_of_residence', doc.file);
            documentUrls[type] = url;
          } catch (uploadError) {
            throw new Error(`Failed to upload ${type}: ${uploadError.message}`);
          }
        }
      }

      // Hash the ID number for duplicate checking
      const idNumberHash = await hashIdNumber(formData.idNumber);

      // Simple encryption (in production, this should be done server-side)
      const encryptedIdNumber = btoa(formData.idNumber);

      const kycData = {
        user_id: userId,
        first_name: formData.firstName.trim(),
        last_name: formData.lastName.trim(),
        id_type: formData.idType,
        id_number_encrypted: encryptedIdNumber,
        id_number_hash: idNumberHash,
        phone_number: formData.phoneNumber.trim(),
        email_address: formData.emailAddress.trim(),
        street_address: formData.streetAddress.trim(),
        city: formData.city.trim(),
        postal_code: formData.postalCode.trim(),
        country_code: formData.countryCode,
        country_name: formData.countryName,
        data_consent_given: formData.dataConsent,
        privacy_policy_accepted: formData.privacyPolicyAccepted,
        kyc_status: 'completed',
        kyc_completed_at: new Date().toISOString(),
        // Store document URLs and facial recognition data in certificate_data field as JSON
        certificate_data: {
          documents: documentUrls,
          facial_recognition: facialRecognitionResults,
          uploaded_at: new Date().toISOString()
        }
      };

      let result;
      if (existingKYC) {
        // Update existing KYC
        const { data, error } = await supabase
          .from('kyc_information')
          .update(kycData)
          .eq('id', existingKYC.id)
          .eq('user_id', userId)
          .select()
          .single();
        
        if (error) throw error;
        result = data;
      } else {
        // Create new KYC
        const { data, error } = await supabase
          .from('kyc_information')
          .insert(kycData)
          .select()
          .single();
        
        if (error) throw error;
        result = data;
      }

      // Log the KYC action (optional - only if function exists)
      try {
        await supabase.rpc('log_kyc_action', {
          p_kyc_id: result.id,
          p_user_id: userId,
          p_action: existingKYC ? 'updated' : 'created',
          p_performed_by_username: 'web_user'
        });
      } catch (logError) {
        console.log('Note: KYC audit logging not available:', logError);
      }

      alert(existingKYC ? 'KYC information updated successfully!' : 'KYC verification completed successfully!');
      setIsEditing(false);
      
      if (onKYCComplete) {
        onKYCComplete('completed');
      }

    } catch (error: any) {
      console.error('Error submitting KYC:', error);
      
      if (error.message?.includes('duplicate')) {
        setErrors({ idNumber: 'This ID/Passport number is already registered' });
      } else {
        alert(`Error: ${error.message || 'Failed to submit KYC information'}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof KYCFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }

    // Auto-update country name when country code changes
    if (field === 'countryCode') {
      const country = countries.find(c => c.code === value);
      if (country) {
        setFormData(prev => ({ ...prev, countryName: country.name }));
      }
    }
  };

  if (existingKYC && !isEditing) {
    return (
      <div className="bg-green-900/30 rounded-lg p-6 border border-green-700">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <span className="text-green-400 text-2xl">✅</span>
            <div>
              <h3 className="text-green-400 font-semibold text-lg">KYC Verification Complete</h3>
              <p className="text-green-200 text-sm">Your identity has been verified successfully</p>
            </div>
          </div>
          <button
            onClick={() => setIsEditing(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm"
          >
            Edit Information
          </button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-400">Full Name:</span>
            <span className="text-white ml-2">{existingKYC.full_legal_name}</span>
          </div>
          <div>
            <span className="text-gray-400">ID Type:</span>
            <span className="text-white ml-2">{existingKYC.id_type === 'national_id' ? 'National ID' : 'Passport'}</span>
          </div>
          <div>
            <span className="text-gray-400">Phone:</span>
            <span className="text-white ml-2">{existingKYC.phone_number}</span>
          </div>
          <div>
            <span className="text-gray-400">Email:</span>
            <span className="text-white ml-2">{existingKYC.email_address}</span>
          </div>
          <div>
            <span className="text-gray-400">Country:</span>
            <span className="text-white ml-2">{existingKYC.country_name}</span>
          </div>
          <div>
            <span className="text-gray-400">Status:</span>
            <span className="text-green-400 ml-2 font-semibold">
              {existingKYC.kyc_status === 'completed' ? 'Verified' : existingKYC.kyc_status}
            </span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
      <div className="mb-6">
        <h3 className="text-xl font-semibold text-white mb-2">
          {existingKYC ? 'Update KYC Information' : 'Complete KYC Verification'}
        </h3>
        <p className="text-gray-400 text-sm">
          Please provide accurate information for identity verification. This is required to access your share certificates.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Personal Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              First Name *
            </label>
            <input
              type="text"
              value={formData.firstName}
              onChange={(e) => handleInputChange('firstName', e.target.value)}
              className={`w-full px-3 py-2 bg-gray-700 border rounded-lg text-white ${
                errors.firstName ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder="Enter your first name"
            />
            {errors.firstName && <p className="text-red-400 text-xs mt-1">{errors.firstName}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Last Name *
            </label>
            <input
              type="text"
              value={formData.lastName}
              onChange={(e) => handleInputChange('lastName', e.target.value)}
              className={`w-full px-3 py-2 bg-gray-700 border rounded-lg text-white ${
                errors.lastName ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder="Enter your last name"
            />
            {errors.lastName && <p className="text-red-400 text-xs mt-1">{errors.lastName}</p>}
          </div>
        </div>

        {/* ID Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              ID Type *
            </label>
            <select
              value={formData.idType}
              onChange={(e) => handleInputChange('idType', e.target.value as 'national_id' | 'passport' | 'drivers_license')}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
            >
              <option value="national_id">National ID</option>
              <option value="passport">Passport</option>
              <option value="drivers_license">Driver's License</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              {formData.idType === 'passport' ? 'Passport Number' :
               formData.idType === 'drivers_license' ? 'Driver\'s License Number' : 'ID Number'} *
            </label>
            <input
              type="text"
              value={formData.idNumber}
              onChange={(e) => handleInputChange('idNumber', e.target.value)}
              className={`w-full px-3 py-2 bg-gray-700 border rounded-lg text-white ${
                errors.idNumber ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder={`Enter your ${
                formData.idType === 'passport' ? 'passport' :
                formData.idType === 'drivers_license' ? 'driver\'s license' : 'ID'
              } number`}
            />
            {errors.idNumber && <p className="text-red-400 text-xs mt-1">{errors.idNumber}</p>}
          </div>
        </div>

        {/* Document Upload Section */}
        <div className="space-y-6 pt-6 border-t border-gray-700">
          <h4 className="text-lg font-semibold text-white">📄 Document Upload</h4>

          {/* ID Document Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {formData.idType === 'passport' ? 'Passport Document' :
               formData.idType === 'drivers_license' ? 'Driver\'s License' : 'National ID Document'} *
            </label>
            <div className="space-y-2">
              {!documents.id_document ? (
                <div className="border-2 border-dashed border-gray-600 rounded-lg p-6 text-center">
                  <div className="text-4xl mb-2">📄</div>
                  <p className="text-gray-400 mb-4">
                    Upload a clear photo or scan of your {
                      formData.idType === 'passport' ? 'passport' :
                      formData.idType === 'drivers_license' ? 'driver\'s license' : 'national ID'
                    }
                  </p>
                  <input
                    type="file"
                    accept="image/*,application/pdf"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) handleFileUpload('id_document', file);
                    }}
                    className="hidden"
                    id="id-document-upload"
                  />
                  <label
                    htmlFor="id-document-upload"
                    className="inline-block px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 cursor-pointer"
                  >
                    Choose File
                  </label>
                  <p className="text-xs text-gray-500 mt-2">
                    Supported: JPG, PNG, PDF • Max 10MB
                  </p>
                </div>
              ) : (
                <div className="bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">📄</span>
                      <div>
                        <p className="text-white font-medium">{documents.id_document.file.name}</p>
                        <p className="text-gray-400 text-sm">
                          {(documents.id_document.file.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={() => {
                        setDocuments(prev => ({ ...prev, id_document: undefined }));
                        setUploadProgress(prev => ({ ...prev, id_document: 0 }));
                      }}
                      className="text-red-400 hover:text-red-300"
                    >
                      ✕
                    </button>
                  </div>
                  {uploadProgress.id_document !== undefined && uploadProgress.id_document < 100 && (
                    <div className="mt-2">
                      <div className="bg-gray-600 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${uploadProgress.id_document}%` }}
                        />
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
            {errors.id_document && <p className="text-red-400 text-xs mt-1">{errors.id_document}</p>}
          </div>

          {/* Proof of Residence Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Proof of Residence *
            </label>
            <div className="space-y-2">
              {!documents.proof_of_residence ? (
                <div className="border-2 border-dashed border-gray-600 rounded-lg p-6 text-center">
                  <div className="text-4xl mb-2">🏠</div>
                  <p className="text-gray-400 mb-4">
                    Upload a recent utility bill, bank statement, or lease agreement
                  </p>
                  <input
                    type="file"
                    accept="image/*,application/pdf"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) handleFileUpload('proof_of_residence', file);
                    }}
                    className="hidden"
                    id="proof-residence-upload"
                  />
                  <label
                    htmlFor="proof-residence-upload"
                    className="inline-block px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 cursor-pointer"
                  >
                    Choose File
                  </label>
                  <p className="text-xs text-gray-500 mt-2">
                    Supported: JPG, PNG, PDF • Max 10MB
                  </p>
                </div>
              ) : (
                <div className="bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">🏠</span>
                      <div>
                        <p className="text-white font-medium">{documents.proof_of_residence.file.name}</p>
                        <p className="text-gray-400 text-sm">
                          {(documents.proof_of_residence.file.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={() => {
                        setDocuments(prev => ({ ...prev, proof_of_residence: undefined }));
                        setUploadProgress(prev => ({ ...prev, proof_of_residence: 0 }));
                      }}
                      className="text-red-400 hover:text-red-300"
                    >
                      ✕
                    </button>
                  </div>
                  {uploadProgress.proof_of_residence !== undefined && uploadProgress.proof_of_residence < 100 && (
                    <div className="mt-2">
                      <div className="bg-gray-600 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${uploadProgress.proof_of_residence}%` }}
                        />
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
            {errors.proof_of_residence && <p className="text-red-400 text-xs mt-1">{errors.proof_of_residence}</p>}

            <div className="mt-2 p-3 bg-blue-900/30 rounded-lg border border-blue-700">
              <p className="text-blue-200 text-xs">
                <strong>Acceptable documents:</strong> Utility bill (electricity, water, gas), bank statement,
                lease agreement, or municipal rates bill dated within the last 3 months.
              </p>
            </div>
          </div>

          {/* Facial Recognition Verification */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Facial Recognition Verification *
            </label>
            <div className="space-y-2">
              {!facialRecognitionResults ? (
                <div className="border-2 border-dashed border-gray-600 rounded-lg p-6 text-center">
                  <div className="text-4xl mb-2">🔐</div>
                  <p className="text-gray-400 mb-4">
                    Complete facial recognition verification to confirm your identity
                  </p>
                  <button
                    type="button"
                    onClick={() => setShowFacialRecognition(true)}
                    className="inline-block px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium"
                  >
                    Start Facial Verification
                  </button>
                  <p className="text-xs text-gray-500 mt-2">
                    This process takes 2-3 minutes and includes liveness detection
                  </p>
                </div>
              ) : (
                <div className="bg-green-900/30 rounded-lg p-4 border border-green-700">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">✅</span>
                      <div>
                        <p className="text-green-400 font-medium">Facial Verification Complete</p>
                        <p className="text-green-200 text-sm">
                          Confidence Score: {(facialRecognitionResults.confidence_score * 100).toFixed(1)}%
                        </p>
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={() => {
                        setFacialRecognitionResults(null);
                        setShowFacialRecognition(true);
                      }}
                      className="text-blue-400 hover:text-blue-300 text-sm"
                    >
                      Retry
                    </button>
                  </div>

                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div className="flex items-center space-x-2">
                      <span className={facialRecognitionResults.liveness_checks.blink_detection ? 'text-green-400' : 'text-red-400'}>
                        {facialRecognitionResults.liveness_checks.blink_detection ? '✅' : '❌'}
                      </span>
                      <span className="text-gray-300">Blink Detection</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={facialRecognitionResults.liveness_checks.head_movement ? 'text-green-400' : 'text-red-400'}>
                        {facialRecognitionResults.liveness_checks.head_movement ? '✅' : '❌'}
                      </span>
                      <span className="text-gray-300">Head Movement</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={facialRecognitionResults.liveness_checks.smile_detection ? 'text-green-400' : 'text-red-400'}>
                        {facialRecognitionResults.liveness_checks.smile_detection ? '✅' : '❌'}
                      </span>
                      <span className="text-gray-300">Smile Detection</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={facialRecognitionResults.liveness_checks.face_centering ? 'text-green-400' : 'text-red-400'}>
                        {facialRecognitionResults.liveness_checks.face_centering ? '✅' : '❌'}
                      </span>
                      <span className="text-gray-300">Face Centering</span>
                    </div>
                  </div>

                  <div className="mt-3 p-2 bg-gray-800 rounded text-xs">
                    <p className="text-gray-400">
                      Session ID: {facialRecognitionResults.metadata.session_id}
                    </p>
                    <p className="text-gray-400">
                      Verified: {new Date(facialRecognitionResults.metadata.timestamp).toLocaleString()}
                    </p>
                  </div>
                </div>
              )}
            </div>
            {errors.facial_recognition && <p className="text-red-400 text-xs mt-1">{errors.facial_recognition}</p>}

            <div className="mt-2 p-3 bg-purple-900/30 rounded-lg border border-purple-700">
              <p className="text-purple-200 text-xs">
                <strong>Facial Recognition:</strong> This advanced verification includes liveness detection
                to prevent fraud. You'll be guided through simple tasks like blinking and head movement.
                The process is secure and your biometric data is encrypted.
              </p>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Phone Number *
            </label>
            <input
              type="tel"
              value={formData.phoneNumber}
              onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
              className={`w-full px-3 py-2 bg-gray-700 border rounded-lg text-white ${
                errors.phoneNumber ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder="+27 ************"
            />
            {errors.phoneNumber && <p className="text-red-400 text-xs mt-1">{errors.phoneNumber}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Email Address *
            </label>
            <input
              type="email"
              value={formData.emailAddress}
              onChange={(e) => handleInputChange('emailAddress', e.target.value)}
              className={`w-full px-3 py-2 bg-gray-700 border rounded-lg text-white ${
                errors.emailAddress ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder="<EMAIL>"
            />
            {errors.emailAddress && <p className="text-red-400 text-xs mt-1">{errors.emailAddress}</p>}
          </div>
        </div>

        {/* Address Information */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Street Address *
          </label>
          <textarea
            value={formData.streetAddress}
            onChange={(e) => handleInputChange('streetAddress', e.target.value)}
            className={`w-full px-3 py-2 bg-gray-700 border rounded-lg text-white ${
              errors.streetAddress ? 'border-red-500' : 'border-gray-600'
            }`}
            rows={2}
            placeholder="Enter your full street address"
          />
          {errors.streetAddress && <p className="text-red-400 text-xs mt-1">{errors.streetAddress}</p>}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              City *
            </label>
            <input
              type="text"
              value={formData.city}
              onChange={(e) => handleInputChange('city', e.target.value)}
              className={`w-full px-3 py-2 bg-gray-700 border rounded-lg text-white ${
                errors.city ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder="City"
            />
            {errors.city && <p className="text-red-400 text-xs mt-1">{errors.city}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Postal Code *
            </label>
            <input
              type="text"
              value={formData.postalCode}
              onChange={(e) => handleInputChange('postalCode', e.target.value)}
              className={`w-full px-3 py-2 bg-gray-700 border rounded-lg text-white ${
                errors.postalCode ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder="Postal Code"
            />
            {errors.postalCode && <p className="text-red-400 text-xs mt-1">{errors.postalCode}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Country *
            </label>
            <select
              value={formData.countryCode}
              onChange={(e) => handleInputChange('countryCode', e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
            >
              {countries.map(country => (
                <option key={country.code} value={country.code}>
                  {country.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Consent Checkboxes */}
        <div className="space-y-3 pt-4 border-t border-gray-700">
          <div className="flex items-start space-x-3">
            <input
              type="checkbox"
              id="dataConsent"
              checked={formData.dataConsent}
              onChange={(e) => handleInputChange('dataConsent', e.target.checked)}
              className="mt-1"
            />
            <label htmlFor="dataConsent" className="text-sm text-gray-300">
              I consent to the collection and processing of my personal data for KYC verification purposes. *
            </label>
          </div>
          {errors.dataConsent && <p className="text-red-400 text-xs">{errors.dataConsent}</p>}

          <div className="flex items-start space-x-3">
            <input
              type="checkbox"
              id="privacyPolicy"
              checked={formData.privacyPolicyAccepted}
              onChange={(e) => handleInputChange('privacyPolicyAccepted', e.target.checked)}
              className="mt-1"
            />
            <label htmlFor="privacyPolicy" className="text-sm text-gray-300">
              I have read and accept the Privacy Policy and Terms of Service. *
            </label>
          </div>
          {errors.privacyPolicyAccepted && <p className="text-red-400 text-xs">{errors.privacyPolicyAccepted}</p>}
        </div>

        {/* Submit Button */}
        <div className="pt-4">
          <button
            type="submit"
            disabled={loading}
            className="w-full px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
          >
            {loading ? 'Processing...' : (existingKYC ? 'Update KYC Information' : 'Complete KYC Verification')}
          </button>
        </div>
      </form>

      {/* Facial Recognition Modal */}
      {showFacialRecognition && (
        <FacialRecognitionKYC
          userId={userId}
          onComplete={(results) => {
            setFacialRecognitionResults(results);
            setShowFacialRecognition(false);
            // Clear any existing facial recognition errors
            setErrors(prev => {
              const newErrors = { ...prev };
              delete newErrors.facial_recognition;
              return newErrors;
            });
          }}
          onCancel={() => setShowFacialRecognition(false)}
        />
      )}
    </div>
  );
};

export default KYCVerificationForm;
