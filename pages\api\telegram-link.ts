import { NextApiRequest, NextApiResponse } from 'next';
import { getServiceRoleClient } from '../../lib/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { user_id, telegram_id } = req.body;

    // Validate input
    if (!user_id || !telegram_id) {
      return res.status(400).json({ error: 'Missing user_id or telegram_id' });
    }

    const telegramIdNum = parseInt(telegram_id);
    if (isNaN(telegramIdNum)) {
      return res.status(400).json({ error: 'Invalid telegram_id format' });
    }

    console.log(`🔗 Linking Telegram ID ${telegramIdNum} to user ${user_id}`);

    const serviceClient = getServiceRoleClient();

    // Check if this Telegram ID is already linked to another user
    const { data: existingLink, error: checkError } = await serviceClient
      .from('users')
      .select('id, username, email')
      .eq('telegram_id', telegramIdNum)
      .neq('id', user_id)
      .single();

    if (!checkError && existingLink) {
      return res.status(409).json({ 
        error: 'This Telegram ID is already linked to another account',
        details: `Linked to user: ${existingLink.username} (${existingLink.email})`
      });
    }

    // Check if the user already has a different Telegram ID linked
    const { data: currentUser, error: userError } = await serviceClient
      .from('users')
      .select('telegram_id, username, email')
      .eq('id', user_id)
      .single();

    if (userError) {
      return res.status(404).json({ error: 'User not found' });
    }

    if (currentUser.telegram_id && currentUser.telegram_id !== telegramIdNum) {
      return res.status(409).json({ 
        error: 'This account is already linked to a different Telegram ID',
        details: `Currently linked to: ${currentUser.telegram_id}`
      });
    }

    // Update the user record with the Telegram ID
    const { error: updateError } = await serviceClient
      .from('users')
      .update({ 
        telegram_id: telegramIdNum,
        updated_at: new Date().toISOString()
      })
      .eq('id', user_id);

    if (updateError) {
      console.error('❌ Error updating user with Telegram ID:', updateError);
      return res.status(500).json({ error: 'Failed to link Telegram account' });
    }

    // Check if there's an existing telegram_users record for this Telegram ID
    const { data: telegramUser, error: telegramUserError } = await serviceClient
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramIdNum)
      .single();

    if (!telegramUserError && telegramUser) {
      // Update existing telegram_users record to link to this user
      const { error: linkError } = await serviceClient
        .from('telegram_users')
        .update({ 
          user_id: user_id,
          updated_at: new Date().toISOString()
        })
        .eq('telegram_id', telegramIdNum);

      if (linkError) {
        console.warn('⚠️ Could not update telegram_users record:', linkError);
      } else {
        console.log('✅ Updated existing telegram_users record');
      }
    }

    console.log('✅ Telegram account linked successfully');
    return res.status(200).json({ 
      success: true,
      message: 'Telegram account linked successfully',
      telegram_id: telegramIdNum,
      user_id: user_id
    });

  } catch (error) {
    console.error('❌ Telegram link API error:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      message: 'Failed to link Telegram account'
    });
  }
}
