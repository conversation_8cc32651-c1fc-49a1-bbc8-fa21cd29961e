const { Resend } = require('resend');
require('dotenv').config();

async function testResendDirect() {
  console.log('🔧 Testing Resend API directly...');
  
  const apiKey = process.env.RESEND_API_KEY;
  const fromEmail = process.env.RESEND_FROM_EMAIL;
  const fromName = process.env.RESEND_FROM_NAME;
  
  console.log('📋 Configuration:', {
    hasApiKey: !!apiKey,
    apiKeyPrefix: apiKey ? apiKey.substring(0, 10) + '...' : 'none',
    fromEmail,
    fromName
  });
  
  if (!apiKey) {
    console.error('❌ No RESEND_API_KEY found in environment');
    return;
  }
  
  const resend = new Resend(apiKey);
  
  try {
    console.log('📧 Sending test email...');
    
    const result = await resend.emails.send({
      from: `${fromName} <${fromEmail}>`,
      to: ['<EMAIL>'], // Use your actual email
      subject: 'Test Email from Aureus Alliance',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #D4AF37;">Test Email</h2>
          <p>This is a test email to verify the Resend configuration is working.</p>
          <p>If you receive this email, the Resend API is configured correctly.</p>
          <p>Time sent: ${new Date().toISOString()}</p>
        </div>
      `,
      text: 'This is a test email to verify the Resend configuration is working.'
    });
    
    if (result.error) {
      console.error('❌ Resend API error:', result.error);
    } else {
      console.log('✅ Email sent successfully!');
      console.log('📊 Result:', {
        id: result.data?.id,
        from: result.data?.from,
        to: result.data?.to
      });
    }
  } catch (error) {
    console.error('❌ Exception:', error.message);
  }
}

testResendDirect();
