import { supabase, getServiceRoleClient } from '../supabase'
import { PaymentAllocationService } from './paymentAllocationService'

/**
 * PAYMENT MANAGER SERVICE
 * 
 * Handles all payment processing logic including:
 * - Share purchase processing
 * - Commission calculations and distribution
 * - Database updates
 * - Audit logging
 */

export interface PaymentProcessingData {
  transactionId: string
  userId: number
  amount: number
  shares: number
  paymentMethod: 'crypto' | 'bank'
  adminProcessed?: boolean
}

export interface PaymentResult {
  success: boolean
  error?: string
  transactionId?: string
  sharePurchaseId?: string
  commissionsDistributed?: number
}

export class PaymentManager {
  
  /**
   * Process a payment and handle all related business logic
   */
  static async processPayment(data: PaymentProcessingData): Promise<PaymentResult> {
    try {
      console.log('🔄 Processing payment:', data)

      // Step 1: Get current investment phase
      const { data: currentPhase, error: phaseError } = await supabase
        .from('investment_phases')
        .select('*')
        .eq('is_active', true)
        .single()

      if (phaseError || !currentPhase) {
        throw new Error('No active investment phase found')
      }

      // Step 2: Create share purchase record
      const serviceClient = getServiceRoleClient()
      const { data: sharePurchase, error: purchaseError } = await serviceClient
        .from('aureus_share_purchases')
        .insert({
          user_id: data.userId,
          shares_purchased: data.shares,
          total_amount: data.amount,
          payment_method: data.paymentMethod === 'crypto' ? 'Cryptocurrency' : 'Bank Transfer',
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select('id')
        .single()

      if (purchaseError) {
        throw new Error(`Failed to create share purchase: ${purchaseError.message}`)
      }

      // Step 3: Update payment transaction status
      const { error: transactionUpdateError } = await supabase
        .from('crypto_payment_transactions')
        .update({
          status: 'approved',
          approved_at: new Date().toISOString(),
          investment_id: sharePurchase.id,
          updated_at: new Date().toISOString()
        })
        .eq('id', data.transactionId)

      if (transactionUpdateError) {
        console.error('Warning: Failed to update transaction status:', transactionUpdateError)
      }

      // Step 4: Create automatic payment allocation
      const allocationResult = await PaymentAllocationService.createAllocation(
        data.transactionId,
        data.userId,
        data.amount,
        data.adminProcessed ? data.userId : undefined // Track if admin-processed
      )

      if (!allocationResult.success) {
        console.error('⚠️  Payment allocation failed:', allocationResult.error)
        // Continue processing - allocation failure shouldn't stop payment
      } else {
        console.log('✅ Payment allocated to sections:', allocationResult.allocationId)
      }

      // Step 5: Calculate and distribute commissions
      const commissionsDistributed = await this.calculateAndDistributeCommissions(
        data.userId,
        data.amount,
        data.shares
      )

      // Step 6: Update user's commission balance (ensure it exists)
      await this.ensureCommissionBalance(data.userId)

      console.log('✅ Payment processed successfully')

      return {
        success: true,
        transactionId: data.transactionId,
        sharePurchaseId: sharePurchase.id,
        commissionsDistributed
      }

    } catch (error) {
      console.error('❌ Payment processing failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Calculate and distribute referral commissions
   */
  private static async calculateAndDistributeCommissions(
    userId: number,
    amount: number,
    shares: number
  ): Promise<number> {
    try {
      console.log('💰 Calculating commissions for user:', userId)

      // Get user's referral chain
      const referralChain = await this.getReferralChain(userId)
      
      if (referralChain.length === 0) {
        console.log('ℹ️  No referral chain found - no commissions to distribute')
        return 0
      }

      let totalCommissionsDistributed = 0

      // Standard commission rates (15% USDT + 15% Shares)
      const usdtCommissionRate = 0.15
      const shareCommissionRate = 0.15

      for (const referral of referralChain) {
        const usdtCommission = amount * usdtCommissionRate
        const shareCommission = shares * shareCommissionRate

        // Update sponsor's commission balance
        const { error: commissionError } = await serviceClient
          .from('commission_balances')
          .upsert({
            user_id: referral.referrer_id,
            usdt_balance: serviceClient.raw(`COALESCE(usdt_balance, 0) + ${usdtCommission}`),
            share_balance: serviceClient.raw(`COALESCE(share_balance, 0) + ${shareCommission}`),
            total_earned_usdt: serviceClient.raw(`COALESCE(total_earned_usdt, 0) + ${usdtCommission}`),
            total_earned_shares: serviceClient.raw(`COALESCE(total_earned_shares, 0) + ${shareCommission}`),
            last_updated: new Date().toISOString()
          }, {
            onConflict: 'user_id'
          })

        if (commissionError) {
          console.error(`Error updating commission for user ${referral.referrer_id}:`, commissionError)
        } else {
          console.log(`✅ Commission distributed to user ${referral.referrer_id}: $${usdtCommission} + ${shareCommission} shares`)
          totalCommissionsDistributed += usdtCommission
        }

        // For now, we only distribute to direct sponsor (level 1)
        // This can be extended for multi-level commissions later
        break
      }

      return totalCommissionsDistributed

    } catch (error) {
      console.error('❌ Commission calculation failed:', error)
      return 0
    }
  }

  /**
   * Get the referral chain for a user
   */
  private static async getReferralChain(userId: number): Promise<any[]> {
    try {
      const { data: referrals, error } = await supabase
        .from('referrals')
        .select('referrer_id, commission_rate, status')
        .eq('referred_id', userId)
        .eq('status', 'active')

      if (error) {
        console.error('Error getting referral chain:', error)
        return []
      }

      return referrals || []

    } catch (error) {
      console.error('Error in getReferralChain:', error)
      return []
    }
  }

  /**
   * Ensure user has a commission balance record
   */
  private static async ensureCommissionBalance(userId: number): Promise<void> {
    try {
      const { data: existing, error: checkError } = await supabase
        .from('commission_balances')
        .select('id')
        .eq('user_id', userId)
        .single()

      if (checkError && checkError.code === 'PGRST116') {
        // No commission balance exists, create one
        const serviceClient = getServiceRoleClient()
        const { error: createError } = await serviceClient
          .from('commission_balances')
          .insert({
            user_id: userId,
            usdt_balance: 0,
            share_balance: 0,
            total_earned_usdt: 0,
            total_earned_shares: 0,
            escrowed_amount: 0,
            total_withdrawn: 0,
            created_at: new Date().toISOString()
          })

        if (createError) {
          console.error('Error creating commission balance:', createError)
        } else {
          console.log(`✅ Commission balance created for user ${userId}`)
        }
      }

    } catch (error) {
      console.error('Error in ensureCommissionBalance:', error)
    }
  }

  /**
   * Get payment statistics for a user
   */
  static async getUserPaymentStats(userId: number): Promise<{
    totalInvested: number
    totalShares: number
    totalCommissions: number
    paymentCount: number
  }> {
    try {
      // Get share purchases
      const { data: purchases, error: purchaseError } = await supabase
        .from('aureus_share_purchases')
        .select('shares_purchased, total_amount')
        .eq('user_id', userId)

      if (purchaseError) {
        console.error('Error getting purchase stats:', purchaseError)
      }

      // Get commission balance
      const { data: commissions, error: commissionError } = await supabase
        .from('commission_balances')
        .select('total_earned_usdt')
        .eq('user_id', userId)
        .single()

      if (commissionError && commissionError.code !== 'PGRST116') {
        console.error('Error getting commission stats:', commissionError)
      }

      // Get payment count
      const { count: paymentCount, error: countError } = await supabase
        .from('crypto_payment_transactions')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('status', 'approved')

      if (countError) {
        console.error('Error getting payment count:', countError)
      }

      const totalInvested = (purchases || []).reduce((sum, p) => sum + (p.total_amount || 0), 0)
      const totalShares = (purchases || []).reduce((sum, p) => sum + (p.shares_purchased || 0), 0)
      const totalCommissions = commissions?.total_earned_usdt || 0

      return {
        totalInvested,
        totalShares,
        totalCommissions,
        paymentCount: paymentCount || 0
      }

    } catch (error) {
      console.error('Error getting user payment stats:', error)
      return {
        totalInvested: 0,
        totalShares: 0,
        totalCommissions: 0,
        paymentCount: 0
      }
    }
  }

  /**
   * Validate payment data before processing
   */
  static validatePaymentData(data: PaymentProcessingData): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!data.transactionId) errors.push('Transaction ID is required')
    if (!data.userId || data.userId <= 0) errors.push('Valid user ID is required')
    if (!data.amount || data.amount <= 0) errors.push('Amount must be greater than 0')
    if (!data.shares || data.shares <= 0) errors.push('Shares must be greater than 0')
    if (!['crypto', 'bank'].includes(data.paymentMethod)) errors.push('Invalid payment method')

    return {
      valid: errors.length === 0,
      errors
    }
  }
}
