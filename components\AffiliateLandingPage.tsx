import React from 'react'
import GoldDiggersClub from './competition/GoldDiggersClub'

interface AffiliateLandingPageProps {
  onBackHome?: () => void
  onGoToAuth?: (mode: 'login' | 'register') => void
  onLogoutAndRegister?: () => void
}

const Bullet: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <li className="flex items-start gap-3 mb-3">
    <span className="mt-1 inline-block w-2 h-2 rounded-full bg-gradient-to-r from-yellow-400 to-yellow-600 shadow-lg" />
    <span className="text-gray-700 leading-relaxed">{children}</span>
  </li>
)

export const AffiliateLandingPage: React.FC<AffiliateLandingPageProps> = ({ onBackHome, onGoToAuth, onLogoutAndRegister }) => {
  console.log('🏢 AffiliateLandingPage component rendering...');

  return (
    <div className="bg-gradient-to-br from-gray-50 via-white to-gray-100 text-gray-900 min-h-screen">
      {/* Enhanced Header with Modern Design */}
      <header className="fixed top-0 left-0 right-0 z-50 backdrop-filter backdrop-blur-xl bg-white/90 border-b border-yellow-500/20 shadow-lg">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Logo Section */}
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center shadow-lg">
                <img
                  src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo.png?v=2"
                  alt="Aureus Alliance Holdings Logo"
                  className="w-8 h-8 object-contain"
                />
              </div>
              <button
                className="text-2xl font-black tracking-wider bg-transparent border-none text-gray-900 cursor-pointer hover:text-yellow-600 transition-all duration-300 hover:scale-105"
                onClick={() => onLogoutAndRegister?.()}
                title="Return to Registration"
              >
                AUREUS
                <div className="text-xs font-normal text-gray-600 tracking-normal">Affiliate Program</div>
              </button>
            </div>

            {/* Navigation Actions */}
            <div className="flex items-center gap-4">
              <button
                className="px-6 py-2.5 rounded-xl border border-gray-300 hover:border-yellow-500/50 hover:bg-gray-50 transition-all duration-300 font-medium text-gray-700"
                onClick={() => onBackHome?.()}
              >
                ← Back to Website
              </button>
              <button
                className="px-6 py-2.5 rounded-xl bg-gradient-to-r from-yellow-500 to-yellow-600 text-white font-semibold hover:from-yellow-400 hover:to-yellow-500 transition-all duration-300 shadow-lg hover:shadow-yellow-500/25"
                onClick={() => onGoToAuth?.('login')}
              >
                Affiliate Login
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content with Enhanced Design */}
      <main className="pt-24 pb-16">
        {/* Hero Section */}
        <section className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-yellow-500/10 via-transparent to-yellow-500/10"></div>
          <div className="container mx-auto px-6 py-20">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center gap-2 bg-yellow-500/10 border border-yellow-500/20 rounded-full px-6 py-2 mb-8">
                <span className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></span>
                <span className="text-yellow-600 font-medium">Exclusive Affiliate Opportunity</span>
              </div>

              <h1 className="text-5xl md:text-6xl font-black mb-6 bg-gradient-to-r from-yellow-500 via-yellow-600 to-yellow-700 bg-clip-text text-transparent leading-tight">
                Aureus Affiliate Program
              </h1>

              <p className="text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed mb-8">
                Apply to become an Aureus Affiliate and build a portfolio of rewards by sharing Aureus with your audience.
                This program is separate from the shareholder platform and includes its own tools, analytics, and commission tracking.
              </p>

              {/* Key Benefits */}
              <div className="grid md:grid-cols-3 gap-6 mt-12 mb-16">
                <div className="bg-white/80 backdrop-blur-sm border border-gray-200 rounded-2xl p-6 shadow-lg">
                  <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mb-4 mx-auto">
                    <span className="text-2xl">💰</span>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-2">High Commissions</h3>
                  <p className="text-gray-600 text-sm">Earn up to 15% USDT + 15% bonus shares on qualified referrals</p>
                </div>

                <div className="bg-white/80 backdrop-blur-sm border border-gray-200 rounded-2xl p-6 shadow-lg">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-xl flex items-center justify-center mb-4 mx-auto">
                    <span className="text-2xl">📊</span>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-2">Advanced Analytics</h3>
                  <p className="text-gray-600 text-sm">Real-time tracking, detailed reports, and performance insights</p>
                </div>

                <div className="bg-white/80 backdrop-blur-sm border border-gray-200 rounded-2xl p-6 shadow-lg">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mb-4 mx-auto">
                    <span className="text-2xl">🏆</span>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-2">Competition Prizes</h3>
                  <p className="text-gray-600 text-sm">Compete for massive USDT prizes in our Gold Diggers Club</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Gold Diggers Club Competition */}
        <section className="container mx-auto px-6">
          <div className="max-w-6xl mx-auto">
            <GoldDiggersClub />
          </div>
        </section>

        {/* Important Notice */}
        <section className="container mx-auto px-6 mt-16">
          <div className="max-w-4xl mx-auto">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-2xl p-8 text-center shadow-lg">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">ℹ️</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Important Notice</h3>
              <p className="text-gray-700 leading-relaxed">
                The affiliate program is entirely separate from the shareholder registration. Shareholders can purchase shares directly without any sponsor or referral requirements.
              </p>
            </div>
          </div>
        </section>
      </main>
    </div>
  )
}

export default AffiliateLandingPage

