import React from 'react'
import GoldDiggersClub from './competition/GoldDiggersClub'

interface AffiliateLandingPageProps {
  onBackHome?: () => void
  onGoToAuth?: (mode: 'login' | 'register') => void
}

const Bullet: React.FC = ({ children }) => (
  <li className="flex items-start gap-3 mb-3">
    <span className="mt-1 inline-block w-2 h-2 rounded-full bg-yellow-500" />
    <span className="text-gray-200">{children}</span>
  </li>
)

export const AffiliateLandingPage: React.FC<AffiliateLandingPageProps> = ({ onBackHome, onGoToAuth }) => {
  return (
    <div className="bg-gradient-to-br from-gray-900 via-black to-gray-900 text-white min-h-screen">
      <header className="border-b border-gray-800/60 sticky top-0 z-20 bg-black/40 backdrop-blur">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="text-2xl font-black tracking-wider">AUREUS</div>
          <div className="flex items-center gap-3">
            <button
              className="px-4 py-2 rounded-lg border border-gray-700 hover:bg-gray-800/60"
              onClick={() => onBackHome?.()}
            >Back to Website</button>
            <button
              className="px-4 py-2 rounded-lg bg-yellow-500 text-black font-semibold hover:bg-yellow-400"
              onClick={() => onGoToAuth?.('login')}
            >Affiliate Login</button>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-12">
        <div className="max-w-5xl mx-auto">
          <div className="text-center mb-10">
            <h1 className="text-4xl font-extrabold mb-4 text-yellow-400">Aureus Affiliate Program</h1>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Apply to become an Aureus Affiliate and build a portfolio of rewards by sharing Aureus with your audience.
              This program is separate from the shareholder platform and includes its own tools, analytics, and commission tracking.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 mb-12">
            <div className="bg-gray-900/60 rounded-2xl border border-gray-800 p-6">
              <h2 className="text-2xl font-bold mb-3">Program Benefits</h2>
              <ul>
                <Bullet>Personal affiliate link and QR tools</Bullet>
                <Bullet>Real-time commission tracking and analytics</Bullet>
                <Bullet>Downline overview and performance insights</Bullet>
                <Bullet>Marketing toolkit with approved creatives</Bullet>
                <Bullet>Payout options and transparent statements</Bullet>
              </ul>
            </div>
            <div className="bg-gray-900/60 rounded-2xl border border-gray-800 p-6">
              <h2 className="text-2xl font-bold mb-3">Tools & Dashboard</h2>
              <ul>
                <Bullet>Commission calculator and forecasting</Bullet>
                <Bullet>Referral link generator and management</Bullet>
                <Bullet>Campaign source tracking</Bullet>
                <Bullet>Notifications and performance alerts</Bullet>
                <Bullet>Ability to hold shares as an affiliate</Bullet>
              </ul>
            </div>
          </div>

          <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-2xl p-8 text-center mb-14">
            <h3 className="text-2xl font-bold text-yellow-400 mb-2">Ready to get started?</h3>
            <p className="text-gray-200 mb-6">Submit your application and create an affiliate account.</p>
            <div className="flex gap-4 justify-center">
              <button
                className="px-6 py-3 rounded-xl bg-yellow-500 text-black font-bold hover:bg-yellow-400"
                onClick={() => onGoToAuth?.('register')}
              >Apply to Become an Affiliate</button>
              <button
                className="px-6 py-3 rounded-xl border border-gray-700 hover:bg-gray-800/60"
                onClick={() => onGoToAuth?.('login')}
              >Affiliate Login</button>
            </div>
          </div>

          {/* Commission Structure Section */}
          <div className="mb-16">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-yellow-400 mb-4">Commission Structure</h2>
              <p className="text-gray-300 max-w-2xl mx-auto">
                Earn competitive commissions on every referral with our transparent reward system.
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8 mb-12">
              {/* Current Phase Commission */}
              <div className="bg-gradient-to-br from-yellow-500/20 to-yellow-600/10 border border-yellow-500/30 rounded-2xl p-6">
                <div className="text-center mb-4">
                  <div className="inline-block bg-yellow-500 text-black px-3 py-1 rounded-full text-sm font-bold mb-3">
                    ⭐ PRESALE EXCLUSIVE
                  </div>
                  <h3 className="text-xl font-bold text-white">Current Phase</h3>
                </div>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">USDT Commission</span>
                    <span className="text-2xl font-bold text-yellow-400">15%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Bonus NFT Shares</span>
                    <span className="text-2xl font-bold text-yellow-400">15%</span>
                  </div>
                  <div className="text-center mt-4 p-3 bg-yellow-500/10 rounded-lg">
                    <p className="text-sm text-yellow-300">⚡ Limited time: Double rewards!</p>
                  </div>
                </div>
              </div>

              {/* Future Phases Commission */}
              <div className="bg-gray-900/60 border border-gray-700 rounded-2xl p-6">
                <div className="text-center mb-4">
                  <div className="inline-block bg-gray-600 text-white px-3 py-1 rounded-full text-sm font-bold mb-3">
                    💼 PHASES 1-19
                  </div>
                  <h3 className="text-xl font-bold text-white">Future Phases</h3>
                </div>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">USDT Commission</span>
                    <span className="text-2xl font-bold text-blue-400">15%</span>
                  </div>
                  <div className="flex justify-between items-center opacity-50">
                    <span className="text-gray-400">Bonus NFT Shares</span>
                    <span className="text-xl text-gray-500">0%</span>
                  </div>
                  <div className="text-center mt-4 p-3 bg-gray-800/50 rounded-lg">
                    <p className="text-sm text-gray-400">USDT rewards continue</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Gold Diggers Club Competition */}
          <GoldDiggersClub />

          <div className="text-sm text-gray-400 text-center mt-12">
            Note: The affiliate program is entirely separate from the shareholder registration. Shareholders can purchase shares directly without any sponsor or referral requirements.
          </div>
        </div>
      </main>
    </div>
  )
}

export default AffiliateLandingPage

