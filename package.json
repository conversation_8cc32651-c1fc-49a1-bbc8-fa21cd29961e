{"name": "aureus-alliance-holdings", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --port 8000 --host 0.0.0.0", "server": "node server.js", "dev:full": "concurrently \"npm run server\" \"npm run dev\" --names \"API,FRONTEND\" --prefix-colors \"blue,green\"", "build": "npm run build:clean && npm run build:env-check && vite build && npm run build:verify", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:env-check": "node scripts/check-env-vars.js", "build:verify": "node scripts/verify-build.js", "build:production": "npm run build:clean && npm run build:env-check && vite build && npm run build:copy-server && npm run build:verify", "build:copy-server": "node scripts/copy-server-files.js", "build:analyze": "vite build --mode analyze && npx vite-bundle-analyzer dist/stats.html", "preview": "vite preview --host 0.0.0.0 --port 8000", "preview:production": "npm run build && npm run preview", "start": "node server.js", "start:production": "NODE_ENV=production npm run start", "deploy:build": "npm run build && npm run deploy:package", "deploy:package": "node scripts/package-for-deployment.js", "test:build": "npm run build && npm run test:build-integrity", "test:build-integrity": "node scripts/test-build-integrity.js", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "validate:css": "node scripts/validate-css-classes.js", "validate:all": "npm run validate:css && npm run lint", "health-check": "node scripts/health-check.js", "startup": "node scripts/startup.js", "setup": "npm install && npm run build:env-check && npm run health-check", "security-check": "node scripts/security-check.cjs", "pre-commit": "npm run security-check && npm run validate:all"}, "dependencies": {"@supabase/supabase-js": "^2.50.3", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.18.2", "jspdf": "^2.5.1", "jszip": "^3.10.1", "multer": "^2.0.2", "qrcode": "^1.5.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.3", "resend": "^6.0.1", "tailwindcss": "^3.4.0"}, "devDependencies": {"@types/jszip": "^3.4.1", "@types/node": "^22.14.0", "@types/qrcode": "^1.5.5", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "concurrently": "^8.2.2", "postcss": "^8.5.6", "rimraf": "^5.0.10", "serve": "^14.2.4", "typescript": "~5.7.2", "vite": "^6.2.0", "vite-bundle-analyzer": "^0.7.0"}}