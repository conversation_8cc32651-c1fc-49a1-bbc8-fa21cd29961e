import React, { useState, useEffect } from 'react'
import { supabase } from '../../lib/supabase'

interface TestUser {
  id: number
  username: string
  email: string
  full_name?: string
  created_at: string
  is_active: boolean
  total_shares: number
  total_spent: number
  commission_earned: number
}

interface AuditData {
  testUsers: TestUser[]
  totalTestUsers: number
  totalSharesPurchased: number
  totalAmountSpent: number
  totalCommissionsEarned: number
}

export const TestUserAuditReport: React.FC = () => {
  const [auditData, setAuditData] = useState<AuditData>({
    testUsers: [],
    totalTestUsers: 0,
    totalSharesPurchased: 0,
    totalAmountSpent: 0,
    totalCommissionsEarned: 0
  })
  const [loading, setLoading] = useState(true)
  const [selectedUser, setSelectedUser] = useState<TestUser | null>(null)

  useEffect(() => {
    loadAuditData()
  }, [])

  const loadAuditData = async () => {
    setLoading(true)
    try {
      // Get test users (users with specific patterns or flags)
      const { data: users, error } = await supabase
        .from('users')
        .select(`
          id,
          username,
          email,
          full_name,
          created_at,
          is_active
        `)
        .or('username.ilike.%test%,email.ilike.%test%,username.ilike.%demo%,email.ilike.%demo%')
        .order('created_at', { ascending: false })

      if (error) throw error

      // Get purchase data for these users
      const userIds = users?.map(u => u.id) || []
      
      const { data: purchases, error: purchaseError } = await supabase
        .from('share_purchases')
        .select('user_id, shares_purchased, total_amount')
        .in('user_id', userIds)
        .eq('status', 'approved')

      if (purchaseError) throw purchaseError

      // Get commission data
      const { data: commissions, error: commissionError } = await supabase
        .from('commission_transactions')
        .select('referrer_id, usdt_commission, share_commission')
        .in('referrer_id', userIds)
        .eq('status', 'approved')

      if (commissionError) throw commissionError

      // Process the data
      const testUsers: TestUser[] = (users || []).map(user => {
        const userPurchases = purchases?.filter(p => p.user_id === user.id) || []
        const userCommissions = commissions?.filter(c => c.referrer_id === user.id) || []

        const totalShares = userPurchases.reduce((sum, p) => sum + p.shares_purchased, 0)
        const totalSpent = userPurchases.reduce((sum, p) => sum + p.total_amount, 0)
        const commissionEarned = userCommissions.reduce((sum, c) => sum + (c.usdt_commission || 0), 0)

        return {
          ...user,
          total_shares: totalShares,
          total_spent: totalSpent,
          commission_earned: commissionEarned
        }
      })

      const totalStats = testUsers.reduce((acc, user) => ({
        totalTestUsers: acc.totalTestUsers + 1,
        totalSharesPurchased: acc.totalSharesPurchased + user.total_shares,
        totalAmountSpent: acc.totalAmountSpent + user.total_spent,
        totalCommissionsEarned: acc.totalCommissionsEarned + user.commission_earned
      }), {
        totalTestUsers: 0,
        totalSharesPurchased: 0,
        totalAmountSpent: 0,
        totalCommissionsEarned: 0
      })

      setAuditData({
        testUsers,
        ...totalStats
      })

    } catch (error) {
      console.error('Error loading audit data:', error)
    } finally {
      setLoading(false)
    }
  }

  const deleteTestUser = async (userId: number) => {
    if (!confirm('Are you sure you want to delete this test user? This action cannot be undone.')) {
      return
    }

    try {
      const { error } = await supabase
        .from('users')
        .delete()
        .eq('id', userId)

      if (error) throw error

      alert('Test user deleted successfully!')
      loadAuditData()
    } catch (error) {
      console.error('Error deleting test user:', error)
      alert('Error deleting test user: ' + (error as Error).message)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-white">Loading audit data...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white">Test User Audit Report</h2>
        <button
          onClick={loadAuditData}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
        >
          Refresh Data
        </button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-gray-800 p-6 rounded-lg border border-gray-600">
          <h3 className="text-lg font-semibold text-white mb-2">Total Test Users</h3>
          <p className="text-3xl font-bold text-blue-400">{auditData.totalTestUsers}</p>
        </div>
        <div className="bg-gray-800 p-6 rounded-lg border border-gray-600">
          <h3 className="text-lg font-semibold text-white mb-2">Total Shares</h3>
          <p className="text-3xl font-bold text-green-400">{auditData.totalSharesPurchased.toLocaleString()}</p>
        </div>
        <div className="bg-gray-800 p-6 rounded-lg border border-gray-600">
          <h3 className="text-lg font-semibold text-white mb-2">Total Spent</h3>
          <p className="text-3xl font-bold text-yellow-400">${auditData.totalAmountSpent.toFixed(2)}</p>
        </div>
        <div className="bg-gray-800 p-6 rounded-lg border border-gray-600">
          <h3 className="text-lg font-semibold text-white mb-2">Commissions Earned</h3>
          <p className="text-3xl font-bold text-purple-400">${auditData.totalCommissionsEarned.toFixed(2)}</p>
        </div>
      </div>

      {/* Test Users Table */}
      <div className="bg-gray-800 rounded-lg border border-gray-600 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-600">
          <h3 className="text-xl font-semibold text-white">Test Users</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Email
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Shares
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Total Spent
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Commissions
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-600">
              {auditData.testUsers.map((user) => (
                <tr key={user.id} className="hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-white">{user.username}</div>
                      <div className="text-sm text-gray-400">ID: {user.id}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                    {user.email}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                    {user.total_shares.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                    ${user.total_spent.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                    ${user.commission_earned.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      user.is_active
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {user.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setSelectedUser(user)}
                        className="text-blue-400 hover:text-blue-300"
                      >
                        View Details
                      </button>
                      <button
                        onClick={() => deleteTestUser(user.id)}
                        className="text-red-400 hover:text-red-300"
                      >
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* User Details Modal */}
      {selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 p-6 rounded-lg border border-gray-600 max-w-2xl w-full mx-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold text-white">User Details</h3>
              <button
                onClick={() => setSelectedUser(null)}
                className="text-gray-400 hover:text-white"
              >
                ✕
              </button>
            </div>
            <div className="space-y-4">
              <div>
                <label className="block text-gray-300 mb-1">Username</label>
                <p className="text-white">{selectedUser.username}</p>
              </div>
              <div>
                <label className="block text-gray-300 mb-1">Email</label>
                <p className="text-white">{selectedUser.email}</p>
              </div>
              <div>
                <label className="block text-gray-300 mb-1">Full Name</label>
                <p className="text-white">{selectedUser.full_name || 'Not provided'}</p>
              </div>
              <div>
                <label className="block text-gray-300 mb-1">Created At</label>
                <p className="text-white">{new Date(selectedUser.created_at).toLocaleString()}</p>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="block text-gray-300 mb-1">Total Shares</label>
                  <p className="text-white font-semibold">{selectedUser.total_shares.toLocaleString()}</p>
                </div>
                <div>
                  <label className="block text-gray-300 mb-1">Total Spent</label>
                  <p className="text-white font-semibold">${selectedUser.total_spent.toFixed(2)}</p>
                </div>
                <div>
                  <label className="block text-gray-300 mb-1">Commissions</label>
                  <p className="text-white font-semibold">${selectedUser.commission_earned.toFixed(2)}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
