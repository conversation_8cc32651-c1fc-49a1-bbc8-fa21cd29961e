import { NextApiRequest, NextApiResponse } from 'next'
import { Resend } from 'resend'

const resend = new Resend(process.env.RESEND_API_KEY)

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  console.log('📧 Email verification API called:', req.method)

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { email, code, purpose } = req.body
    console.log('📧 Request data:', { email, code: code ? '******' : 'missing', purpose })

    if (!email || !code || !purpose) {
      console.error('❌ Missing required fields:', { email: !!email, code: !!code, purpose: !!purpose })
      return res.status(400).json({ error: 'Missing required fields' })
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return res.status(400).json({ error: 'Invalid email format' })
    }

    // Validate code format (6 digits)
    if (!/^\d{6}$/.test(code)) {
      return res.status(400).json({ error: 'Invalid code format' })
    }

    // Generate email content
    const subject = 'Verify Your Email Address - Aureus Alliance'
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #f59e0b; margin: 0;">Aureus Alliance Holdings</h1>
        </div>

        <div style="background: #f8f9fa; padding: 30px; border-radius: 10px; text-align: center;">
          <h2 style="color: #333; margin-bottom: 20px;">Email Verification Required</h2>

          <p style="color: #666; font-size: 16px; margin-bottom: 30px;">
            Please use the following 6-digit code to verify your email address and complete your registration:
          </p>

          <div style="background: #fff; border: 2px solid #f59e0b; border-radius: 8px; padding: 20px; margin: 20px 0; display: inline-block;">
            <span style="font-size: 32px; font-weight: bold; color: #f59e0b; letter-spacing: 8px;">${code}</span>
          </div>

          <p style="color: #666; font-size: 14px; margin-top: 20px;">
            This code will expire in 15 minutes for security reasons.
          </p>

          <p style="color: #666; font-size: 14px; margin-top: 10px;">
            If you didn't request this verification, please ignore this email.
          </p>
        </div>

        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
          <p style="color: #999; font-size: 12px;">
            © 2024 Aureus Alliance Holdings. All rights reserved.
          </p>
        </div>
      </div>
    `

    const text = `
      Aureus Alliance Holdings - Email Verification

      Your verification code is: ${code}

      This code will expire in 15 minutes.

      If you didn't request this verification, please ignore this email.
    `

    // Check Resend configuration
    console.log('📧 Resend config check:', {
      hasApiKey: !!process.env.RESEND_API_KEY,
      fromEmail: process.env.RESEND_FROM_EMAIL || '<EMAIL>',
      fromName: process.env.RESEND_FROM_NAME || 'Aureus Alliance'
    })

    // Send email using Resend
    console.log('📧 Sending email to:', email)
    const result = await resend.emails.send({
      from: `${process.env.RESEND_FROM_NAME || 'Aureus Alliance'} <${process.env.RESEND_FROM_EMAIL || '<EMAIL>'}>`,
      to: [email],
      subject,
      html,
      text,
      tags: [
        { name: 'category', value: 'verification' },
        { name: 'purpose', value: purpose }
      ]
    })

    console.log('📧 Resend API response:', { success: !result.error, messageId: result.data?.id, error: result.error })

    if (result.error) {
      console.error('❌ Resend API error:', result.error)
      return res.status(500).json({
        error: 'Failed to send verification email',
        details: result.error.message || 'Unknown Resend error'
      })
    }

    console.log('✅ Verification email sent successfully:', result.data?.id)
    res.status(200).json({ success: true, message: 'Verification email sent successfully' })

  } catch (error) {
    console.error('API error:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
}
