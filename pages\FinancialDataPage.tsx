import React, { useState } from 'react';

interface FinancialDataPageProps {
  onNavigate: (page: string) => void;
}

const FinancialDataPage: React.FC<FinancialDataPageProps> = ({ onNavigate }) => {
  const [activeTable, setActiveTable] = useState<string>('production');

  // 5-Year Production & Dividend Data
  const productionData = [
    { 
      year: "2026", 
      plants: 10, 
      hectares: 250, 
      goldKg: 875, 
      revenue: "$95.4M", 
      ebit: "$52.5M",
      dividendPerShare: "$37.50",
      totalDividends: "$52.5M"
    },
    { 
      year: "2027", 
      plants: 25, 
      hectares: 625, 
      goldKg: 2188, 
      revenue: "$238.5M", 
      ebit: "$131.2M",
      dividendPerShare: "$93.75",
      totalDividends: "$131.2M"
    },
    { 
      year: "2028", 
      plants: 50, 
      hectares: 1250, 
      goldKg: 4375, 
      revenue: "$477M", 
      ebit: "$262.4M",
      dividendPerShare: "$187.50",
      totalDividends: "$262.4M"
    },
    { 
      year: "2029", 
      plants: 100, 
      hectares: 2500, 
      goldKg: 8750, 
      revenue: "$954M", 
      ebit: "$524.7M",
      dividendPerShare: "$375.00",
      totalDividends: "$524.7M"
    },
    { 
      year: "2030", 
      plants: 200, 
      hectares: 6000, 
      goldKg: 21000, 
      revenue: "$2.29B", 
      ebit: "$1.26B",
      dividendPerShare: "$900.00",
      totalDividends: "$1.26B"
    }
  ];

  // Phase Breakdown Data
  const phaseBreakdown = [
    { 
      phase: "Presale", 
      price: "$5.00", 
      shares: "200,000", 
      totalFunds: "$1,000,000",
      operations: "Launch First Wash Plant at Kadoma",
      csr: "Feed 2,000+ Children, Install 4 Boreholes"
    },
    { 
      phase: "Phase 1-3", 
      price: "$10-$20", 
      shares: "375,000", 
      totalFunds: "$5,625,000",
      operations: "Complete 3 Wash Plants, Begin Production",
      csr: "Feed 10,000+ Children, Build 6 Classrooms"
    },
    { 
      phase: "Phase 4-6", 
      price: "$25-$35", 
      shares: "175,000", 
      totalFunds: "$5,250,000",
      operations: "Launch 6th Plant, Expand Operations",
      csr: "Feed 15,000+ Children, Mobile Health Units"
    },
    { 
      phase: "Phase 7-10", 
      price: "$40-$100", 
      shares: "200,000", 
      totalFunds: "$14,000,000",
      operations: "10 Plants Total, Begin Mutare Expansion",
      csr: "Feed 22,000+ Children, Hospital Development"
    },
    { 
      phase: "Phase 11-15", 
      price: "$200-$600", 
      shares: "250,000", 
      totalFunds: "$100,000,000",
      operations: "87 Plants, Multi-Site Operations",
      csr: "Feed 150,000+ Children, Regional Hospital"
    },
    { 
      phase: "Phase 16-19", 
      price: "$700-$1,000", 
      shares: "200,000", 
      totalFunds: "$170,000,000",
      operations: "257 Plants, Multi-Country Expansion",
      csr: "Feed 250,000+ Children, Complete Hospital"
    }
  ];

  // Operational Metrics
  const operationalMetrics = [
    { metric: "Plant Capacity", value: "200 TPH", description: "Tonnes per hour processing capacity per plant" },
    { metric: "Operating Hours", value: "20 hours/day", description: "Daily operational hours per plant" },
    { metric: "Operating Days", value: "330 days/year", description: "Annual operating days accounting for maintenance" },
    { metric: "Land per Plant", value: "25 hectares", description: "Land area covered by each wash plant" },
    { metric: "In-Situ Grade", value: "0.9 g/m³", description: "Gold content in the gravel deposits" },
    { metric: "Recovery Rate", value: "70% (target 95%)", description: "Percentage of gold successfully extracted" },
    { metric: "Bulk Density", value: "1.8 T/m³", description: "Weight of gravel per cubic meter" },
    { metric: "Operating Costs", value: "45% of revenue", description: "Total operational expenses as percentage of revenue" }
  ];

  return (
    <div className="page">
      {/* Page Header */}
      <section className="page-header">
        <div className="container">
          <div className="breadcrumb">
            <button onClick={() => onNavigate('home')} className="breadcrumb-link">
              Home
            </button>
            <span className="breadcrumb-separator">→</span>
            <span className="breadcrumb-current">Financial Data</span>
          </div>
          
          <h1 className="page-title">Financial Data & Projections</h1>
          <p className="page-subtitle">
            Comprehensive financial tables, production forecasts, and operational metrics 
            based on proven mining parameters and conservative projections
          </p>
        </div>
      </section>

      {/* Data Navigation */}
      <section className="data-navigation">
        <div className="container">
          <div className="nav-tabs">
            <button 
              className={`nav-tab ${activeTable === 'production' ? 'active' : ''}`}
              onClick={() => setActiveTable('production')}
            >
              📊 Production & Dividends
            </button>
            <button 
              className={`nav-tab ${activeTable === 'phases' ? 'active' : ''}`}
              onClick={() => setActiveTable('phases')}
            >
              📈 Phase Breakdown
            </button>
            <button 
              className={`nav-tab ${activeTable === 'operations' ? 'active' : ''}`}
              onClick={() => setActiveTable('operations')}
            >
              ⚙️ Operational Metrics
            </button>
          </div>
        </div>
      </section>

      {/* Production & Dividends Table */}
      {activeTable === 'production' && (
        <section className="data-section">
          <div className="container">
            <div className="section-header">
              <h2>5-Year Production & Dividend Projections</h2>
              <p>Gold output and dividend forecasts based on expansion timeline</p>
            </div>

            <div className="table-wrapper">
              <div className="data-table">
                <table>
                  <thead>
                    <tr>
                      <th>Year</th>
                      <th>Plants</th>
                      <th>Hectares</th>
                      <th>Gold Output (kg)</th>
                      <th>Revenue</th>
                      <th>EBIT</th>
                      <th>Dividend/Share</th>
                      <th>Total Dividends</th>
                    </tr>
                  </thead>
                  <tbody>
                    {productionData.map((row, index) => (
                      <tr key={index}>
                        <td className="year-cell"><strong>{row.year}</strong></td>
                        <td>{row.plants}</td>
                        <td>{row.hectares.toLocaleString()}</td>
                        <td>{row.goldKg.toLocaleString()}</td>
                        <td>{row.revenue}</td>
                        <td>{row.ebit}</td>
                        <td className="highlight">{row.dividendPerShare}</td>
                        <td>{row.totalDividends}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            <div className="table-notes">
              <h3>Key Assumptions:</h3>
              <ul>
                <li>Gold price: $109,026 per kg (current market rate)</li>
                <li>Recovery rate: 70% (conservative estimate, targeting 95%)</li>
                <li>Operating costs: 45% of revenue (industry standard)</li>
                <li>Dividend payout: 100% of EBIT distributed to shareholders</li>
                <li>Total shares: 1,400,000 across all phases</li>
              </ul>
            </div>
          </div>
        </section>
      )}

      {/* Phase Breakdown Table */}
      {activeTable === 'phases' && (
        <section className="data-section">
          <div className="container">
            <div className="section-header">
              <h2>Investment Phase Fund Allocation</h2>
              <p>Detailed breakdown of how funds are allocated across operations and CSR</p>
            </div>

            <div className="table-wrapper">
              <div className="data-table">
                <table>
                  <thead>
                    <tr>
                      <th>Phase Group</th>
                      <th>Price Range</th>
                      <th>Total Shares</th>
                      <th>Total Funds</th>
                      <th>Operations Focus</th>
                      <th>CSR Impact</th>
                    </tr>
                  </thead>
                  <tbody>
                    {phaseBreakdown.map((row, index) => (
                      <tr key={index}>
                        <td className="phase-cell"><strong>{row.phase}</strong></td>
                        <td>{row.price}</td>
                        <td>{row.shares}</td>
                        <td className="highlight">{row.totalFunds}</td>
                        <td>{row.operations}</td>
                        <td>{row.csr}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            <div className="phase-summary">
              <div className="summary-cards">
                <div className="summary-card">
                  <h3>Total Funding Capacity</h3>
                  <div className="summary-value">$295.875M</div>
                  <p>Across all 20 phases when fully subscribed</p>
                </div>
                <div className="summary-card">
                  <h3>Operations Allocation</h3>
                  <div className="summary-value">~70%</div>
                  <p>Dedicated to mining operations and expansion</p>
                </div>
                <div className="summary-card">
                  <h3>CSR Allocation</h3>
                  <div className="summary-value">~30%</div>
                  <p>Community impact and social responsibility</p>
                </div>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Operational Metrics */}
      {activeTable === 'operations' && (
        <section className="data-section">
          <div className="container">
            <div className="section-header">
              <h2>Operational Parameters & Metrics</h2>
              <p>Technical specifications and operational assumptions used in calculations</p>
            </div>

            <div className="metrics-grid">
              {operationalMetrics.map((metric, index) => (
                <div key={index} className="metric-card">
                  <div className="metric-header">
                    <h3>{metric.metric}</h3>
                    <div className="metric-value">{metric.value}</div>
                  </div>
                  <p className="metric-description">{metric.description}</p>
                </div>
              ))}
            </div>

            <div className="operational-notes">
              <h3>Data Sources & Validation:</h3>
              <div className="notes-grid">
                <div className="note-card">
                  <h4>🔬 Geological Data</h4>
                  <p>Based on 2020 geotechnical report confirming site viability and gold content analysis</p>
                </div>
                <div className="note-card">
                  <h4>🏭 Equipment Specifications</h4>
                  <p>Plant capacity based on manufacturer specifications for jaw crushers, ball mills, and gravity separation systems</p>
                </div>
                <div className="note-card">
                  <h4>📊 Industry Benchmarks</h4>
                  <p>Operating costs and recovery rates benchmarked against similar alluvial gold mining operations</p>
                </div>
                <div className="note-card">
                  <h4>💰 Market Data</h4>
                  <p>Gold prices based on current London Bullion Market Association (LBMA) rates</p>
                </div>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Risk Factors */}
      <section className="risk-factors">
        <div className="container">
          <div className="section-header">
            <h2>Risk Factors & Disclaimers</h2>
            <p>Important considerations for potential investors</p>
          </div>

          <div className="risk-grid">
            <div className="risk-card">
              <div className="risk-icon">⚠️</div>
              <h3>Market Risk</h3>
              <p>Gold prices fluctuate based on global economic conditions, currency movements, and market sentiment</p>
            </div>
            <div className="risk-card">
              <div className="risk-icon">🏭</div>
              <h3>Operational Risk</h3>
              <p>Mining operations may face equipment failures, weather delays, or regulatory changes</p>
            </div>
            <div className="risk-card">
              <div className="risk-icon">🌍</div>
              <h3>Political Risk</h3>
              <p>Operations in multiple African countries subject to political and regulatory changes</p>
            </div>
            <div className="risk-card">
              <div className="risk-icon">📊</div>
              <h3>Performance Risk</h3>
              <p>Actual recovery rates and operational efficiency may differ from projections</p>
            </div>
          </div>

          <div className="disclaimer">
            <p>
              <strong>Investment Disclaimer:</strong> All projections are based on current operational 
              parameters, market conditions, and conservative estimates. Past performance does not 
              guarantee future results. Potential investors should conduct their own due diligence 
              and consider their risk tolerance before making investment decisions.
            </p>
          </div>
        </div>
      </section>
    </div>
  );
};

export default FinancialDataPage;
