import React from 'react';

interface CSRPageProps {
  onNavigate: (page: string) => void;
}

const CSRPage: React.FC<CSRPageProps> = ({ onNavigate }) => {
  return (
    <div className="page">
      {/* Page Header */}
      <section className="page-header">
        <div className="container">
          <div className="breadcrumb">
            <button onClick={() => onNavigate('home')} className="breadcrumb-link">
              Home
            </button>
            <span className="breadcrumb-separator">→</span>
            <span className="breadcrumb-current">Corporate Social Responsibility</span>
          </div>
          
          <h1 className="page-title">Corporate Social Responsibility</h1>
          <p className="page-subtitle">
            Aureus Alliance Holdings is committed to uplifting communities while driving sustainable growth. 
            Every phase of our expansion includes dedicated CSR initiatives that create lasting positive impact.
          </p>
        </div>
      </section>

      {/* CSR Mission */}
      <section className="mission-section">
        <div className="container">
          <div className="section-header">
            <h2>Our CSR Mission</h2>
            <p>Building gold-backed impact ventures that transform lives</p>
          </div>

          <div className="mission-grid">
            <div className="mission-card">
              <div className="mission-icon">🏥</div>
              <h3>Healthcare Access</h3>
              <p>
                Establishing mobile health units, maternal care fleets, and developing 
                regional hospitals to provide essential medical services to underserved communities.
              </p>
            </div>

            <div className="mission-card">
              <div className="mission-icon">🎓</div>
              <h3>Education Empowerment</h3>
              <p>
                Building solar-powered schools, funding thousands of scholarships, 
                and providing educational resources to empower future generations.
              </p>
            </div>

            <div className="mission-card">
              <div className="mission-icon">💧</div>
              <h3>Clean Water Access</h3>
              <p>
                Installing boreholes and water purification systems to ensure 
                communities have access to clean, safe drinking water.
              </p>
            </div>

            <div className="mission-card">
              <div className="mission-icon">🍽️</div>
              <h3>Nutrition Programs</h3>
              <p>
                Feeding hundreds of thousands of children through comprehensive 
                nutrition and food security programs across Africa.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Impact by Numbers */}
      <section className="impact-numbers">
        <div className="container">
          <div className="section-header">
            <h2>Our Projected Impact</h2>
            <p>Measurable change across our 19-phase expansion plan</p>
          </div>

          <div className="numbers-grid">
            <div className="number-card">
              <div className="number">250,000+</div>
              <div className="label">Children Fed</div>
              <p>Through comprehensive nutrition programs</p>
            </div>

            <div className="number-card">
              <div className="number">15,000+</div>
              <div className="label">School Scholarships</div>
              <p>Full scholarships to empower education</p>
            </div>

            <div className="number-card">
              <div className="number">100+</div>
              <div className="label">Water Systems</div>
              <p>Boreholes and purification tanks installed</p>
            </div>

            <div className="number-card">
              <div className="number">30+</div>
              <div className="label">Mobile Health Units</div>
              <p>Including surgical and maternal care fleets</p>
            </div>

            <div className="number-card">
              <div className="number">50+</div>
              <div className="label">Schools Built</div>
              <p>Solar-powered educational facilities</p>
            </div>

            <div className="number-card">
              <div className="number">1</div>
              <div className="label">Regional Hospital</div>
              <p>Advanced medical facility for communities</p>
            </div>
          </div>
        </div>
      </section>

      {/* Phase-by-Phase CSR Breakdown */}
      <section className="phase-breakdown">
        <div className="container">
          <div className="section-header">
            <h2>CSR Allocation by Phase</h2>
            <p>Structured community investment throughout our expansion</p>
          </div>

          <div className="phase-timeline">
            <div className="phase-item">
              <div className="phase-number">Phase 1-5</div>
              <div className="phase-content">
                <h3>Foundation Building</h3>
                <ul>
                  <li>Feed 2,000-12,000 children through food security programs</li>
                  <li>Install 4-20 boreholes for clean drinking water</li>
                  <li>Support 2-7 clinics with medical supplies</li>
                  <li>Fund 100-200+ school bursaries and uniforms</li>
                  <li>Build 2-6 new classrooms and school blocks</li>
                </ul>
              </div>
            </div>

            <div className="phase-item">
              <div className="phase-number">Phase 6-10</div>
              <div className="phase-content">
                <h3>Healthcare Expansion</h3>
                <ul>
                  <li>Feed 15,000-30,000 children through expanded programs</li>
                  <li>Establish mobile health units for rural areas</li>
                  <li>Install 6-15 water purification tanks</li>
                  <li>Begin development of regional hospital</li>
                  <li>Sponsor 200-1,000+ full school scholarships</li>
                </ul>
              </div>
            </div>

            <div className="phase-item">
              <div className="phase-number">Phase 11-15</div>
              <div className="phase-content">
                <h3>Regional Impact</h3>
                <ul>
                  <li>Feed 50,000-150,000 children across multiple countries</li>
                  <li>Deploy 2-5 mobile surgical units</li>
                  <li>Install 20-50 water purification systems</li>
                  <li>Continue hospital development</li>
                  <li>Sponsor 2,000-5,000+ scholarships</li>
                </ul>
              </div>
            </div>

            <div className="phase-item">
              <div className="phase-number">Phase 16-19</div>
              <div className="phase-content">
                <h3>Continental Transformation</h3>
                <ul>
                  <li>Feed 175,000-250,000 children across Africa</li>
                  <li>Deploy 10+ maternal & child care fleets</li>
                  <li>Install 60-100 water purification systems</li>
                  <li>Complete regional hospital infrastructure</li>
                  <li>Sponsor 7,500-15,000+ full scholarships</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Sustainability Focus */}
      <section className="sustainability-section">
        <div className="container">
          <div className="section-header">
            <h2>Sustainable Development Goals</h2>
            <p>Aligned with UN SDGs for maximum global impact</p>
          </div>

          <div className="sdg-grid">
            <div className="sdg-card">
              <h3>SDG 1: No Poverty</h3>
              <p>Creating jobs and economic opportunities in rural communities</p>
            </div>

            <div className="sdg-card">
              <h3>SDG 3: Good Health</h3>
              <p>Mobile health units and hospital development for healthcare access</p>
            </div>

            <div className="sdg-card">
              <h3>SDG 4: Quality Education</h3>
              <p>School construction and scholarship programs for educational empowerment</p>
            </div>

            <div className="sdg-card">
              <h3>SDG 6: Clean Water</h3>
              <p>Borehole installation and water purification systems</p>
            </div>

            <div className="sdg-card">
              <h3>SDG 7: Clean Energy</h3>
              <p>Solar-powered schools and renewable energy infrastructure</p>
            </div>

            <div className="sdg-card">
              <h3>SDG 8: Decent Work</h3>
              <p>Job creation and skills development in mining communities</p>
            </div>
          </div>
        </div>
      </section>

      {/* Community Partnerships */}
      <section className="partnerships-section">
        <div className="container">
          <div className="section-header">
            <h2>Community Partnerships</h2>
            <p>Working hand-in-hand with local leaders and organizations</p>
          </div>

          <div className="partnership-content">
            <div className="partnership-card">
              <h3>Local Government Collaboration</h3>
              <p>
                We work closely with local governments to ensure our CSR initiatives 
                align with community needs and development priorities.
              </p>
            </div>

            <div className="partnership-card">
              <h3>Traditional Leaders</h3>
              <p>
                Respecting and partnering with traditional leaders to ensure 
                cultural sensitivity and community acceptance of our programs.
              </p>
            </div>

            <div className="partnership-card">
              <h3>NGO Partnerships</h3>
              <p>
                Collaborating with established NGOs to leverage expertise and 
                ensure effective delivery of healthcare and education programs.
              </p>
            </div>

            <div className="partnership-card">
              <h3>Community Committees</h3>
              <p>
                Establishing local committees to oversee CSR program implementation 
                and ensure transparency and accountability.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="cta-section">
        <div className="container">
          <div className="cta-content">
            <h2>Your Investment Creates Real Impact</h2>
            <p>
              When you invest in Aureus Alliance Holdings, you're not just buying shares in a gold mining company. 
              You're becoming part of a movement that transforms communities and creates lasting positive change across Africa.
            </p>
            <div className="cta-buttons">
              <button 
                className="btn btn-primary"
                onClick={() => onNavigate('investment-phases')}
              >
                Start Your Impact Journey
              </button>
              <button 
                className="btn btn-secondary"
                onClick={() => onNavigate('home')}
              >
                Learn More About Aureus
              </button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default CSRPage;
