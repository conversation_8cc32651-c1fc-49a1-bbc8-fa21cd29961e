import React from 'react';
import { ValidationResult } from '../lib/realtimeValidation';

interface ValidationFeedbackProps {
  validation: ValidationResult | null;
  className?: string;
}

export const ValidationFeedback: React.FC<ValidationFeedbackProps> = ({ 
  validation, 
  className = '' 
}) => {
  if (!validation) {
    return null;
  }

  const getIconAndStyles = () => {
    switch (validation.type) {
      case 'success':
        return {
          icon: (
            <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          ),
          textColor: 'text-green-400',
          bgColor: 'bg-green-500/10',
          borderColor: 'border-green-500/30'
        };
      case 'error':
        return {
          icon: (
            <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          ),
          textColor: 'text-red-400',
          bgColor: 'bg-red-500/10',
          borderColor: 'border-red-500/30'
        };
      case 'checking':
        return {
          icon: (
            <svg className="w-5 h-5 text-blue-500 animate-spin" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          ),
          textColor: 'text-blue-400',
          bgColor: 'bg-blue-500/10',
          borderColor: 'border-blue-500/30'
        };
      default:
        return {
          icon: (
            <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          ),
          textColor: 'text-gray-400',
          bgColor: 'bg-gray-500/10',
          borderColor: 'border-gray-500/30'
        };
    }
  };

  const { icon, textColor, bgColor, borderColor } = getIconAndStyles();

  return (
    <div className={`flex items-center gap-2 mt-2 p-2 rounded-lg border ${bgColor} ${borderColor} ${className}`}>
      {icon}
      <span className={`text-sm font-medium ${textColor}`}>
        {validation.message}
      </span>
    </div>
  );
};

interface ValidationInputProps {
  id: string;
  name: string;
  type: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  validation: ValidationResult | null;
  placeholder?: string;
  autoComplete?: string;
  className?: string;
  label: string;
  required?: boolean;
}

export const ValidationInput: React.FC<ValidationInputProps> = ({
  id,
  name,
  type,
  value,
  onChange,
  validation,
  placeholder,
  autoComplete,
  className = '',
  label,
  required = false
}) => {
  const getBorderColor = () => {
    if (!validation) return 'border-gray-600/50';
    
    switch (validation.type) {
      case 'success':
        return 'border-green-500/50';
      case 'error':
        return 'border-red-500/50';
      case 'checking':
        return 'border-blue-500/50';
      default:
        return 'border-gray-600/50';
    }
  };

  const getIcon = () => {
    if (!validation || validation.isChecking) return null;
    
    if (validation.isValid) {
      return (
        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
          <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
      );
    } else {
      return (
        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
          <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </div>
      );
    }
  };

  return (
    <div>
      <label htmlFor={id} className="block text-sm font-semibold text-gray-300 mb-3">
        {label} {required && <span className="text-red-400">*</span>}
      </label>
      <div className="relative">
        <input
          id={id}
          name={name}
          type={type}
          value={value}
          onChange={onChange}
          autoComplete={autoComplete}
          className={`w-full px-4 py-4 pr-12 bg-gray-800/60 border rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200 ${getBorderColor()} ${className}`}
          placeholder={placeholder}
        />
        {getIcon()}
        {validation?.isChecking && (
          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
            <svg className="w-5 h-5 text-blue-500 animate-spin" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
        )}
      </div>
      <ValidationFeedback validation={validation} />
    </div>
  );
};
