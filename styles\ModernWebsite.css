/* Modern Professional Website Styling for Aureus Alliance Holdings */

/* CSS Variables for Consistent Color Scheme */
:root {
  /* Primary Colors */
  --primary-gold: #D4AF37;
  --primary-gold-dark: #B8941F;
  --primary-gold-light: #E6C55A;
  
  /* Neutral Colors */
  --white: #FFFFFF;
  --gray-50: #F9FAFB;
  --gray-100: #F3F4F6;
  --gray-200: #E5E7EB;
  --gray-300: #D1D5DB;
  --gray-400: #9CA3AF;
  --gray-500: #6B7280;
  --gray-600: #4B5563;
  --gray-700: #374151;
  --gray-800: #1F2937;
  --gray-900: #111827;
  
  /* Accent Colors */
  --blue-500: #3B82F6;
  --green-500: #10B981;
  --red-500: #EF4444;
  --yellow-500: #F59E0B;
  
  /* Gradients */
  --gradient-gold: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-dark) 100%);
  --gradient-subtle: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Spacing */
  --space-xs: 0.5rem;
  --space-sm: 1rem;
  --space-md: 1.5rem;
  --space-lg: 2rem;
  --space-xl: 3rem;
  --space-2xl: 4rem;
  
  /* Typography */
  --font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Override conflicting styles from index.css */
body {
  font-family: var(--font-family) !important;
  line-height: 1.6 !important;
  color: var(--theme-text) !important;
  background-color: var(--theme-bg) !important;
  background: var(--theme-bg) !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  letter-spacing: normal !important;
  font-size: var(--font-size-base) !important;
}

/* Override dark theme styles */
.modern-website {
  background: var(--theme-bg) !important;
  color: var(--theme-text) !important;
}

.modern-website * {
  letter-spacing: normal !important;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-sm);
}

/* Page Layout */
.page {
  min-height: 100vh;
  background-color: var(--theme-bg);
  color: var(--theme-text);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Header Styles */
.clean-header {
  background: var(--theme-bg);
  border-bottom: 1px solid var(--theme-border);
  padding: var(--space-sm) 0;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: var(--theme-shadow);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo h1 {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary-gold);
  margin-bottom: 0.25rem;
}

.logo p {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  font-weight: 500;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.nav-item {
  background: none;
  border: none;
  color: var(--theme-text-secondary);
  font-weight: 500;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
  transition: all 0.2s ease;
  font-size: var(--font-size-sm);
}

.nav-item:hover, .nav-item.active {
  color: var(--primary-gold);
  background: var(--gray-50);
}

.cta-btn {
  background: var(--gradient-gold);
  color: var(--white);
  border: none;
  padding: var(--space-xs) var(--space-md);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: var(--font-size-sm);
}

.cta-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Page Header */
.page-header {
  background: var(--gradient-subtle);
  padding: var(--space-2xl) 0 var(--space-xl);
  border-bottom: 1px solid var(--gray-200);
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  margin-bottom: var(--space-md);
  font-size: var(--font-size-sm);
}

.breadcrumb-link {
  background: none;
  border: none;
  color: var(--primary-gold);
  cursor: pointer;
  text-decoration: underline;
}

.breadcrumb-separator {
  color: var(--gray-400);
}

.breadcrumb-current {
  color: var(--gray-600);
}

.page-title {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-sm);
  line-height: 1.2;
}

.page-subtitle {
  font-size: var(--font-size-lg);
  color: var(--gray-600);
  max-width: 800px;
  line-height: 1.6;
}

/* Section Headers */
.section-header {
  text-align: center;
  margin-bottom: var(--space-xl);
}

.section-header h2 {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-sm);
}

.section-header p {
  font-size: var(--font-size-lg);
  color: var(--gray-600);
  max-width: 600px;
  margin: 0 auto;
}

/* Hero Section */
.hero {
  padding: var(--space-2xl) 0;
  background: var(--gradient-subtle);
}

.hero-content {
  text-align: center;
  max-width: 900px;
  margin: 0 auto;
}

.hero-title {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-md);
  line-height: 1.2;
}

.hero-subtitle {
  font-size: var(--font-size-lg);
  color: var(--gray-600);
  margin-bottom: var(--space-xl);
  line-height: 1.7;
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-md);
  margin-bottom: var(--space-xl);
}

.metric-card {
  background: var(--theme-card-bg);
  border: 1px solid var(--theme-border);
  border-radius: var(--radius-lg);
  padding: var(--space-md);
  text-align: center;
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
  color: var(--theme-text);
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.metric-value {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--primary-gold);
  margin-bottom: var(--space-xs);
}

.metric-label {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  font-weight: 500;
}

/* CTA Buttons */
.cta-buttons {
  display: flex;
  justify-content: center;
  gap: var(--space-md);
  flex-wrap: wrap;
}

.btn {
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-md);
  font-weight: 600;
  font-size: var(--font-size-base);
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 2px solid transparent;
}

.btn-primary {
  background: var(--gradient-gold);
  color: var(--white);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--theme-card-bg);
  color: var(--primary-gold);
  border-color: var(--primary-gold);
}

.btn-secondary:hover {
  background: var(--primary-gold);
  color: var(--white);
}

/* Grid Layouts */
.overview-grid,
.trust-grid,
.links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-lg);
}

/* Card Styles */
.overview-card,
.trust-item,
.link-card {
  background: var(--theme-card-bg);
  border: 1px solid var(--theme-border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
  color: var(--theme-text);
}

.overview-card:hover,
.trust-item:hover,
.link-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.card-icon,
.trust-icon,
.link-icon {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--space-sm);
  display: block;
}

.overview-card h3,
.trust-item h4,
.link-card h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-sm);
}

.overview-card ul {
  list-style: none;
}

.overview-card li {
  margin-bottom: var(--space-xs);
  color: var(--gray-600);
  line-height: 1.5;
}

.overview-card strong {
  color: var(--primary-gold);
}

/* Link Cards */
.link-card {
  cursor: pointer;
  position: relative;
}

.link-arrow {
  position: absolute;
  top: var(--space-md);
  right: var(--space-md);
  font-size: var(--font-size-xl);
  color: var(--primary-gold);
  transition: transform 0.2s ease;
}

.link-card:hover .link-arrow {
  transform: translateX(4px);
}

/* Sections */
.overview,
.trust-section,
.quick-links {
  padding: var(--space-2xl) 0;
}

.trust-section {
  background: var(--gray-50);
}

/* Footer */
.clean-footer {
  background: var(--gray-50);
  border-top: 1px solid var(--gray-200);
  padding: var(--space-xl) 0 var(--space-md);
  margin-top: var(--space-2xl);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

.footer-info h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--primary-gold);
  margin-bottom: var(--space-xs);
}

.footer-info p {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
}

.footer-links {
  display: flex;
  gap: var(--space-md);
}

.footer-links button {
  background: none;
  border: none;
  color: var(--gray-600);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: color 0.2s ease;
}

.footer-links button:hover {
  color: var(--primary-gold);
}

.footer-bottom {
  text-align: center;
  padding-top: var(--space-md);
  border-top: 1px solid var(--gray-200);
  color: var(--gray-500);
  font-size: var(--font-size-sm);
}

/* Phase Summary Cards */
.phase-summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-lg);
  margin-bottom: var(--space-2xl);
}

.phase-summary-card {
  background: var(--theme-card-bg);
  border: 1px solid var(--theme-border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
  color: var(--theme-text);
}

.phase-summary-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.phase-summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
  padding-bottom: var(--space-sm);
  border-bottom: 2px solid var(--gray-100);
}

.phase-group {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--gray-900);
}

.price-range {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--primary-gold);
}

.phase-summary-details {
  margin-bottom: var(--space-md);
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--space-xs);
}

.detail-label {
  color: var(--gray-600);
  font-weight: 500;
}

.detail-value {
  color: var(--gray-900);
  font-weight: 600;
}

.phase-highlight {
  display: flex;
  align-items: flex-start;
  gap: var(--space-sm);
  padding: var(--space-sm);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--primary-gold);
}

.highlight-icon {
  font-size: var(--font-size-lg);
  margin-top: 2px;
}

.phase-highlight p {
  color: var(--gray-700);
  font-size: var(--font-size-sm);
  line-height: 1.5;
  margin: 0;
}

/* Investment Points */
.investment-points,
.calculator-intro {
  padding: var(--space-2xl) 0;
  background: var(--theme-bg-secondary);
}

.points-grid,
.intro-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-lg);
}

.point-card,
.intro-card {
  background: var(--theme-card-bg);
  border: 1px solid var(--theme-border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  text-align: center;
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
  color: var(--theme-text);
}

.point-card:hover,
.intro-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.point-icon,
.intro-icon {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--space-sm);
  display: block;
}

.point-card h3,
.intro-card h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-sm);
}

.point-card p,
.intro-card p {
  color: var(--gray-600);
  line-height: 1.6;
}

/* Phase Display Integration */
.detailed-phases {
  padding: var(--space-2xl) 0;
}

.phase-display-wrapper {
  background: var(--theme-card-bg);
  border-radius: var(--radius-xl);
  padding: var(--space-lg);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--theme-border);
  color: var(--theme-text);
}

.phases-integrated {
  margin: 0;
}

/* Investment Strategy Cards */
.investment-strategy {
  padding: var(--space-2xl) 0;
  background: var(--theme-bg);
}

.strategy-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-lg);
}

.strategy-card {
  background: var(--theme-card-bg);
  border: 2px solid var(--theme-border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  position: relative;
  transition: all 0.2s ease;
  color: var(--theme-text);
}

.strategy-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.strategy-badge {
  position: absolute;
  top: -12px;
  left: var(--space-lg);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.strategy-badge.early {
  background: var(--green-500);
  color: var(--white);
}

.strategy-badge.growth {
  background: var(--blue-500);
  color: var(--white);
}

.strategy-badge.stable {
  background: var(--primary-gold);
  color: var(--white);
}

.strategy-card h3 {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--gray-900);
  margin: var(--space-sm) 0;
}

.strategy-price {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--primary-gold);
  margin-bottom: var(--space-md);
}

.strategy-benefits {
  list-style: none;
  margin-bottom: var(--space-md);
}

.strategy-benefits li {
  padding: var(--space-xs) 0;
  color: var(--gray-600);
  position: relative;
  padding-left: var(--space-md);
}

.strategy-benefits li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: var(--green-500);
  font-weight: bold;
}

.strategy-risk {
  padding: var(--space-sm);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  color: var(--gray-700);
}

/* Next Steps */
.next-steps {
  padding: var(--space-2xl) 0;
  background: var(--gray-50);
}

.next-steps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-lg);
}

.next-step-card {
  background: var(--theme-card-bg);
  border: 1px solid var(--theme-border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: var(--theme-text);
}

.next-step-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-gold);
}

.step-icon {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--space-sm);
  display: block;
}

.next-step-card h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-sm);
}

.next-step-card p {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
  line-height: 1.5;
}

/* Calculator Page Styles */
.calculator-assumptions {
  padding: var(--space-2xl) 0;
  background: var(--theme-bg);
}

.assumptions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-lg);
}

.assumption-group {
  background: var(--theme-card-bg);
  border: 1px solid var(--theme-border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
}

.assumption-group h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--theme-text);
  margin-bottom: var(--space-md);
}

.assumption-group ul {
  list-style: none;
}

.assumption-group li {
  padding: var(--space-xs) 0;
  color: var(--theme-text-secondary);
  font-size: var(--font-size-sm);
  line-height: 1.5;
}

.assumption-group strong {
  color: var(--theme-text);
}

.calculator-tool {
  padding: var(--space-2xl) 0;
  background: var(--theme-bg-secondary);
}

.calculator-wrapper {
  background: var(--theme-card-bg);
  border-radius: var(--radius-xl);
  padding: var(--space-lg);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--theme-border);
}

/* Example Scenarios */
.example-scenarios {
  padding: var(--space-2xl) 0;
  background: var(--theme-bg);
}

/* Gallery Page Styles */
.gallery-intro {
  padding: var(--space-2xl) 0;
  background: var(--theme-bg-secondary);
}

.gallery-notification {
  padding: var(--space-xl) 0;
  background: var(--theme-bg);
}

.notification-card {
  display: flex;
  align-items: flex-start;
  gap: var(--space-lg);
  background: var(--theme-card-bg);
  border: 1px solid var(--theme-border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--shadow-sm);
}

.notification-icon {
  font-size: 2rem;
  flex-shrink: 0;
  background: var(--primary-gold);
  color: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-content h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--theme-text);
  margin-bottom: var(--space-sm);
}

.notification-content p {
  color: var(--gray-700);
  line-height: 1.6;
  margin-bottom: var(--space-md);
}

.notification-meta {
  display: flex;
  gap: var(--space-md);
  flex-wrap: wrap;
}

.update-frequency {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  background: var(--gray-100);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
}

.verification-badge {
  font-size: var(--font-size-sm);
  color: var(--green-700);
  background: var(--green-100);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-weight: 500;
}

.gallery-content {
  padding: var(--space-2xl) 0;
  background: var(--theme-bg);
}

.gallery-wrapper {
  background: var(--theme-card-bg);
  border-radius: var(--radius-xl);
  padding: var(--space-lg);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--theme-border);
}

.gallery-features {
  padding: var(--space-2xl) 0;
  background: var(--theme-bg-secondary);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-lg);
  margin-top: var(--space-lg);
}

.feature-card {
  background: var(--theme-card-bg);
  border: 1px solid var(--theme-border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  text-align: center;
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: var(--space-sm);
}

.feature-card h4 {
  color: var(--theme-text);
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--space-sm);
}

.feature-card p {
  color: var(--theme-text-secondary);
  font-size: var(--font-size-sm);
  line-height: 1.5;
}

.gallery-cta {
  padding: var(--space-2xl) 0;
  background: var(--theme-bg);
  text-align: center;
}

.cta-content h2 {
  color: var(--theme-text);
  font-size: var(--font-size-2xl);
  font-weight: 700;
  margin-bottom: var(--space-sm);
}

.cta-content p {
  color: var(--theme-text-secondary);
  font-size: var(--font-size-lg);
  max-width: 600px;
  margin: 0 auto var(--space-lg);
  line-height: 1.6;
}

.cta-buttons {
  display: flex;
  gap: var(--space-md);
  justify-content: center;
  flex-wrap: wrap;
}

.scenarios-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
}

.scenario-card {
  background: var(--theme-card-bg);
  border: 2px solid var(--theme-border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
  color: var(--theme-text);
}

.scenario-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-gold);
}

.scenario-header {
  text-align: center;
  margin-bottom: var(--space-md);
  padding-bottom: var(--space-sm);
  border-bottom: 2px solid var(--gray-100);
}

.scenario-header h3 {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-xs);
}

.scenario-investment {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--primary-gold);
}

.scenario-details {
  text-align: center;
}

.scenario-shares {
  font-size: var(--font-size-base);
  color: var(--gray-700);
  margin-bottom: var(--space-md);
  font-weight: 500;
}

.scenario-projections {
  margin-bottom: var(--space-md);
}

.projection-year {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-xs) 0;
  font-size: var(--font-size-sm);
}

.year {
  color: var(--gray-600);
  font-weight: 500;
}

.dividend {
  color: var(--green-500);
  font-weight: 600;
}

.scenario-total {
  padding: var(--space-sm);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  color: var(--primary-gold);
  font-size: var(--font-size-sm);
}

.scenarios-disclaimer {
  background: var(--yellow-500);
  background-opacity: 0.1;
  border: 1px solid var(--yellow-500);
  border-radius: var(--radius-md);
  padding: var(--space-md);
  margin-top: var(--space-xl);
}

.scenarios-disclaimer p {
  color: var(--gray-700);
  font-size: var(--font-size-sm);
  line-height: 1.6;
  margin: 0;
}

/* Financial Data Page Styles */
.data-navigation {
  padding: var(--space-lg) 0;
  background: var(--theme-bg);
  border-bottom: 1px solid var(--theme-border);
}

.nav-tabs {
  display: flex;
  justify-content: center;
  gap: var(--space-sm);
  flex-wrap: wrap;
}

.nav-tab {
  background: var(--theme-card-bg);
  border: 2px solid var(--theme-border);
  color: var(--theme-text-secondary);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  font-size: var(--font-size-sm);
}

.nav-tab:hover {
  border-color: var(--primary-gold);
  color: var(--primary-gold);
}

.nav-tab.active {
  background: var(--primary-gold);
  border-color: var(--primary-gold);
  color: var(--white);
}

.data-section {
  padding: var(--space-2xl) 0;
}

/* Table Styles */
.table-wrapper {
  background: var(--theme-card-bg);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--theme-border);
  margin-bottom: var(--space-xl);
}

.data-table table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background: var(--gray-50);
  color: var(--gray-700);
  font-weight: 600;
  padding: var(--space-md);
  text-align: left;
  border-bottom: 2px solid var(--gray-200);
  font-size: var(--font-size-sm);
  white-space: nowrap;
}

.data-table td {
  padding: var(--space-md);
  border-bottom: 1px solid var(--gray-100);
  color: var(--gray-700);
  font-size: var(--font-size-sm);
}

.data-table tr:hover {
  background: var(--gray-50);
}

.data-table .year-cell,
.data-table .phase-cell {
  font-weight: 600;
  color: var(--gray-900);
}

.data-table .highlight {
  color: var(--primary-gold);
  font-weight: 600;
}

.table-notes {
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
}

.table-notes h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-sm);
}

.table-notes ul {
  list-style: none;
  padding-left: 0;
}

.table-notes li {
  padding: var(--space-xs) 0;
  color: var(--gray-600);
  font-size: var(--font-size-sm);
  line-height: 1.5;
  position: relative;
  padding-left: var(--space-md);
}

.table-notes li::before {
  content: "•";
  position: absolute;
  left: 0;
  color: var(--primary-gold);
  font-weight: bold;
}

/* Phase Summary Cards */
.phase-summary {
  margin-top: var(--space-xl);
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-lg);
}

.summary-card {
  background: var(--theme-card-bg);
  border: 1px solid var(--theme-border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  text-align: center;
  box-shadow: var(--shadow-sm);
  color: var(--theme-text);
}

.summary-card h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-sm);
}

.summary-value {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--primary-gold);
  margin-bottom: var(--space-xs);
}

.summary-card p {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
}

/* Operational Metrics */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-lg);
}

.metric-card {
  background: var(--theme-card-bg);
  border: 1px solid var(--theme-border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
  color: var(--theme-text);
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.metric-header {
  margin-bottom: var(--space-sm);
}

.metric-header h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-xs);
}

.metric-value {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary-gold);
}

.metric-description {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
  line-height: 1.5;
}

/* Operational Notes */
.operational-notes {
  margin-top: var(--space-xl);
}

.operational-notes h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-lg);
  text-align: center;
}

.notes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-lg);
}

.note-card {
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
}

.note-card h4 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-sm);
}

.note-card p {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
  line-height: 1.5;
}

/* Risk Factors */
.risk-factors {
  padding: var(--space-2xl) 0;
  background: var(--gray-50);
}

.risk-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
}

.risk-card {
  background: var(--theme-card-bg);
  border: 1px solid var(--theme-border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  text-align: center;
  box-shadow: var(--shadow-sm);
  color: var(--theme-text);
}

.risk-icon {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--space-sm);
  display: block;
}

.risk-card h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-sm);
}

.risk-card p {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
  line-height: 1.5;
}

.disclaimer {
  background: var(--red-500);
  background-opacity: 0.1;
  border: 1px solid var(--red-500);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  text-align: center;
}

.disclaimer p {
  color: var(--gray-700);
  font-size: var(--font-size-sm);
  line-height: 1.6;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--space-md);
  }

  .header-content {
    flex-direction: column;
    gap: var(--space-sm);
  }

  .nav-menu {
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--space-xs);
  }

  .nav-item {
    padding: var(--space-xs);
    font-size: var(--font-size-xs);
  }

  .page-title {
    font-size: var(--font-size-3xl);
  }

  .hero-title {
    font-size: var(--font-size-3xl);
  }

  .section-header h2 {
    font-size: var(--font-size-2xl);
  }

  .metrics-grid,
  .overview-grid,
  .trust-grid,
  .links-grid,
  .phase-summary-grid,
  .points-grid,
  .intro-grid,
  .strategy-grid,
  .next-steps-grid,
  .assumptions-grid,
  .scenarios-grid,
  .summary-cards,
  .notes-grid,
  .risk-grid {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 100%;
    max-width: 300px;
  }

  .footer-content {
    flex-direction: column;
    gap: var(--space-md);
    text-align: center;
  }

  .footer-links {
    flex-wrap: wrap;
    justify-content: center;
  }

  .nav-tabs {
    flex-direction: column;
    align-items: center;
  }

  .nav-tab {
    width: 100%;
    max-width: 300px;
    text-align: center;
  }

  .table-wrapper {
    overflow-x: auto;
  }

  .data-table table {
    min-width: 600px;
  }

  .data-table th,
  .data-table td {
    padding: var(--space-sm);
    font-size: var(--font-size-xs);
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: var(--space-xl) 0 var(--space-lg);
  }

  .page-title {
    font-size: var(--font-size-2xl);
  }

  .hero-title {
    font-size: var(--font-size-2xl);
  }

  .section-header h2 {
    font-size: var(--font-size-xl);
  }

  .overview,
  .trust-section,
  .quick-links,
  .investment-points,
  .calculator-intro,
  .detailed-phases,
  .investment-strategy,
  .next-steps,
  .calculator-assumptions,
  .calculator-tool,
  .example-scenarios,
  .data-section,
  .risk-factors {
    padding: var(--space-xl) 0;
  }

  .comparison-grid {
    grid-template-columns: 1fr;
  }

  .phase-item {
    flex-direction: column;
    text-align: center;
  }

  .phase-number {
    width: 80px;
    height: 80px;
    margin: 0 auto;
  }
}

/* New Page Styles */

/* Content Sections */
.content-section {
  padding: var(--space-2xl) 0;
  background: var(--theme-bg);
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-lg);
  margin-top: var(--space-xl);
}

.content-card {
  background: var(--theme-card-bg);
  border: 1px solid var(--theme-border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  text-align: center;
  box-shadow: var(--theme-shadow);
  transition: all 0.3s ease;
}

.content-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px var(--theme-shadow);
}

.card-icon {
  font-size: 3rem;
  margin-bottom: var(--space-md);
}

/* Comparison Section */
.comparison-section {
  padding: var(--space-2xl) 0;
  background: var(--theme-bg-secondary);
}

.comparison-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--space-xl);
  margin-top: var(--space-xl);
}

.comparison-card {
  background: var(--theme-card-bg);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  box-shadow: var(--theme-shadow);
}

.comparison-card.wash-plants {
  border-left: 4px solid var(--success-green);
}

.comparison-card.strip-mining {
  border-left: 4px solid #ef4444;
}

.benefit-list, .drawback-list {
  list-style: none;
  padding: 0;
  margin-top: var(--space-md);
}

.benefit-list li, .drawback-list li {
  padding: var(--space-xs) 0;
  font-weight: 500;
}

/* Technical Section */
.technical-section {
  padding: var(--space-2xl) 0;
  background: var(--theme-bg);
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-lg);
  margin-top: var(--space-xl);
}

.tech-card {
  background: var(--theme-card-bg);
  border: 1px solid var(--theme-border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  text-align: center;
  box-shadow: var(--theme-shadow);
}

.tech-stat {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--primary-gold);
  margin: var(--space-md) 0;
}

/* Environmental Section */
.environmental-section {
  padding: var(--space-2xl) 0;
  background: var(--theme-bg-secondary);
}

.environmental-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-lg);
  margin-top: var(--space-xl);
}

.env-benefit {
  background: var(--theme-card-bg);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  text-align: center;
  box-shadow: var(--theme-shadow);
}

.env-icon {
  font-size: 3rem;
  margin-bottom: var(--space-md);
}

/* Investment Section */
.investment-section {
  padding: var(--space-2xl) 0;
  background: var(--theme-bg);
}

.investment-points {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-lg);
  margin-top: var(--space-xl);
}

.investment-card {
  background: var(--theme-card-bg);
  border: 1px solid var(--theme-border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--theme-shadow);
}

/* CSR Specific Styles */
.mission-section {
  padding: var(--space-2xl) 0;
  background: var(--theme-bg);
}

.mission-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-lg);
  margin-top: var(--space-xl);
}

.mission-card {
  background: var(--theme-card-bg);
  border: 1px solid var(--theme-border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  text-align: center;
  box-shadow: var(--theme-shadow);
  transition: all 0.3s ease;
}

.mission-card:hover {
  transform: translateY(-4px);
}

.mission-icon {
  font-size: 3rem;
  margin-bottom: var(--space-md);
}

/* Impact Numbers */
.impact-numbers {
  padding: var(--space-2xl) 0;
  background: var(--theme-bg-secondary);
}

.numbers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-lg);
  margin-top: var(--space-xl);
}

.number-card {
  background: var(--theme-card-bg);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  text-align: center;
  box-shadow: var(--theme-shadow);
}

.number {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--primary-gold);
  margin-bottom: var(--space-xs);
}

.label {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--theme-text);
  margin-bottom: var(--space-sm);
}

/* Phase Timeline */
.phase-breakdown {
  padding: var(--space-2xl) 0;
  background: var(--theme-bg);
}

.phase-timeline {
  margin-top: var(--space-xl);
}

.phase-item {
  display: flex;
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
  padding: var(--space-lg);
  background: var(--theme-card-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--theme-shadow);
}

.phase-number {
  flex-shrink: 0;
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, var(--primary-gold), var(--gold-secondary));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  color: white;
  text-align: center;
}

.phase-content ul {
  list-style: none;
  padding: 0;
  margin-top: var(--space-md);
}

.phase-content li {
  padding: var(--space-xs) 0;
  position: relative;
  padding-left: var(--space-md);
}

.phase-content li::before {
  content: '•';
  color: var(--primary-gold);
  font-weight: bold;
  position: absolute;
  left: 0;
}

/* Sustainability Section */
.sustainability-section {
  padding: var(--space-2xl) 0;
  background: var(--theme-bg-secondary);
}

.sdg-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-lg);
  margin-top: var(--space-xl);
}

.sdg-card {
  background: var(--theme-card-bg);
  border: 1px solid var(--theme-border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--theme-shadow);
  transition: all 0.3s ease;
}

.sdg-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--theme-shadow);
}

/* Partnerships Section */
.partnerships-section {
  padding: var(--space-2xl) 0;
  background: var(--theme-bg);
}

.partnership-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-lg);
  margin-top: var(--space-xl);
}

.partnership-card {
  background: var(--theme-card-bg);
  border: 1px solid var(--theme-border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--theme-shadow);
}

/* Gallery & Video Section */
.gallery-section {
  padding: var(--space-2xl) 0;
  background: var(--theme-bg-secondary);
}

.media-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-xl);
  margin-top: var(--space-xl);
}

.featured-video {
  background: var(--theme-card-bg);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--theme-shadow);
}

.video-placeholder {
  padding: var(--space-xl);
  text-align: center;
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(245, 158, 11, 0.05));
}

.video-icon {
  font-size: 4rem;
  margin-bottom: var(--space-md);
  color: var(--primary-gold);
}

.play-button {
  background: var(--primary-gold);
  color: white;
  border: none;
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  margin-top: var(--space-md);
  transition: all 0.3s ease;
}

.play-button:hover {
  background: var(--gold-secondary);
  transform: translateY(-2px);
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-md);
}

.gallery-item {
  background: var(--theme-card-bg);
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--theme-shadow);
  transition: all 0.3s ease;
}

.gallery-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px var(--theme-shadow);
}

.image-placeholder {
  padding: var(--space-lg);
  text-align: center;
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.05), rgba(245, 158, 11, 0.02));
}

.image-placeholder span {
  font-size: 2rem;
  display: block;
  margin-bottom: var(--space-sm);
}

.image-placeholder p {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--theme-text);
  margin: 0;
}

.gallery-cta {
  display: flex;
  justify-content: center;
  gap: var(--space-md);
  margin-top: var(--space-xl);
}

/* Responsive Gallery */
@media (max-width: 768px) {
  .media-grid {
    grid-template-columns: 1fr;
  }

  .gallery-grid {
    grid-template-columns: 1fr;
  }

  .gallery-cta {
    flex-direction: column;
    align-items: center;
  }
}

/* Mine Production Page Styles */
.production-intro {
  padding: var(--space-2xl) 0;
  background: var(--theme-bg-secondary);
}

.operational-media {
  padding: var(--space-2xl) 0;
  background: var(--theme-bg);
}

.media-section {
  margin-bottom: var(--space-2xl);
}

.media-section h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--theme-text);
  margin-bottom: var(--space-lg);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
}

.video-placeholder {
  background: var(--theme-card-bg);
  border: 1px solid var(--theme-border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.video-placeholder:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.video-icon {
  font-size: 3rem;
  margin-bottom: var(--space-md);
  background: var(--primary-gold);
  color: white;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-md);
}

.video-placeholder h4 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--theme-text);
  margin-bottom: var(--space-sm);
}

.video-placeholder p {
  color: var(--gray-600);
  margin-bottom: var(--space-md);
  line-height: 1.5;
}

.placeholder-note {
  font-size: var(--font-size-sm);
  color: var(--gray-500);
  font-style: italic;
  background: var(--gray-100);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  display: inline-block;
}

.photo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-md);
  margin-bottom: var(--space-xl);
}

.photo-placeholder {
  background: var(--theme-card-bg);
  border: 1px solid var(--theme-border);
  border-radius: var(--radius-lg);
  padding: var(--space-md);
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.photo-placeholder:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.photo-icon {
  font-size: 2.5rem;
  margin-bottom: var(--space-sm);
  background: var(--blue-500);
  color: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-sm);
}

.photo-placeholder h4 {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--theme-text);
  margin-bottom: var(--space-xs);
}

.photo-placeholder p {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
  line-height: 1.4;
}

.media-note {
  display: flex;
  align-items: flex-start;
  gap: var(--space-md);
  background: var(--blue-50);
  border: 1px solid var(--blue-200);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  margin-top: var(--space-xl);
}

.note-icon {
  font-size: 1.5rem;
  color: var(--blue-600);
  flex-shrink: 0;
}

.note-content h4 {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--blue-800);
  margin-bottom: var(--space-xs);
}

.note-content p {
  color: var(--blue-700);
  line-height: 1.6;
  margin: 0;
}

/* Coming Soon Card */
.coming-soon-card {
  background: var(--theme-card-bg);
  border: 1px solid var(--theme-border);
  border-radius: var(--radius-xl);
  padding: var(--space-2xl);
  text-align: center;
  box-shadow: var(--shadow-lg);
  max-width: 600px;
  margin: 0 auto;
  color: var(--theme-text);
}

.coming-soon-icon {
  font-size: 4rem;
  margin-bottom: var(--space-lg);
  color: var(--primary-gold);
}

.coming-soon-card h2 {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-md);
}

.coming-soon-card p {
  font-size: var(--font-size-lg);
  color: var(--gray-600);
  line-height: 1.6;
  margin-bottom: var(--space-xl);
}
