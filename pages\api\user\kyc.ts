import { NextApiRequest, NextApiResponse } from 'next';
import { KYCService, KYCSubmissionData } from '../../../lib/services/kycService';
import { supabase } from '../../../lib/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Get user from session/auth
    const userId = parseInt(req.headers['x-user-id'] as string);
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    switch (req.method) {
      case 'GET':
        return await handleGetKYC(req, res, userId);
      case 'POST':
        return await handleSubmitKYC(req, res, userId);
      case 'PUT':
        return await handleUpdateKYC(req, res, userId);
      default:
        return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error('KYC API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

async function handleGetKYC(req: NextApiRequest, res: NextApiResponse, userId: number) {
  try {
    const kycData = await KYCService.getKYCByUserId(userId);
    
    if (!kycData) {
      return res.status(404).json({ error: 'KYC information not found' });
    }

    // Remove sensitive data before sending to client
    const sanitizedData = {
      ...kycData,
      id_number_encrypted: undefined,
      id_number_hash: undefined
    };

    return res.status(200).json(sanitizedData);
  } catch (error) {
    console.error('Error fetching KYC:', error);
    return res.status(500).json({ error: 'Failed to fetch KYC information' });
  }
}

async function handleSubmitKYC(req: NextApiRequest, res: NextApiResponse, userId: number) {
  try {
    const kycData: KYCSubmissionData = req.body;

    // Validate the data
    const validation = KYCService.validateKYCData(kycData);
    if (!validation.isValid) {
      return res.status(400).json({ 
        error: 'Validation failed', 
        errors: validation.errors 
      });
    }

    // Check if user already has KYC
    const existingKYC = await KYCService.getKYCByUserId(userId);
    if (existingKYC) {
      return res.status(409).json({ error: 'KYC information already exists' });
    }

    // Submit KYC
    const result = await KYCService.submitKYC(userId, kycData);

    // Sync with Telegram bot
    await KYCService.syncWithTelegramBot(userId);

    // Remove sensitive data before sending response
    const sanitizedResult = {
      ...result,
      id_number_encrypted: undefined,
      id_number_hash: undefined
    };

    return res.status(201).json(sanitizedResult);
  } catch (error) {
    console.error('Error submitting KYC:', error);
    return res.status(500).json({ error: 'Failed to submit KYC information' });
  }
}

async function handleUpdateKYC(req: NextApiRequest, res: NextApiResponse, userId: number) {
  try {
    const { kycId, ...updates } = req.body;

    if (!kycId) {
      return res.status(400).json({ error: 'KYC ID is required' });
    }

    // Validate the updates
    const validation = KYCService.validateKYCData(updates as KYCSubmissionData);
    if (!validation.isValid) {
      return res.status(400).json({ 
        error: 'Validation failed', 
        errors: validation.errors 
      });
    }

    // Update KYC
    const result = await KYCService.updateKYC(kycId, userId, updates);

    // Sync with Telegram bot
    await KYCService.syncWithTelegramBot(userId);

    // Remove sensitive data before sending response
    const sanitizedResult = {
      ...result,
      id_number_encrypted: undefined,
      id_number_hash: undefined
    };

    return res.status(200).json(sanitizedResult);
  } catch (error) {
    console.error('Error updating KYC:', error);
    return res.status(500).json({ error: 'Failed to update KYC information' });
  }
}
