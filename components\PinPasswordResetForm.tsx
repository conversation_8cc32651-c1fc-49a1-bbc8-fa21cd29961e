import React, { useState, useRef, useEffect } from 'react';
import { validatePasswordStrength, getPasswordStrengthScore } from '../lib/passwordSecurity';
import { signInWithEmailEnhanced } from '../lib/supabase';

interface PinPasswordResetFormProps {
  onResetSuccess: () => void;
  onResetError: (error: string) => void;
  onBack: () => void;
  onLoginSuccess?: (user: any) => void;
}

export const PinPasswordResetForm: React.FC<PinPasswordResetFormProps> = ({
  onResetSuccess,
  onResetError,
  onBack,
  onLoginSuccess
}) => {
  const [step, setStep] = useState<'email' | 'pin' | 'password'>('email');
  const [email, setEmail] = useState('');
  const [pin, setPin] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Real-time password match validation
  const passwordsMatch = newPassword.trim() === confirmPassword.trim();
  const showPasswordMatch = newPassword.length > 0 && confirmPassword.length > 0;
  
  // PIN input refs
  const pinInputRefs = useRef<(HTMLInputElement | null)[]>([]);
  
  // Password strength
  const [passwordStrength, setPasswordStrength] = useState({
    score: 0,
    label: '',
    color: ''
  });

  // Update password strength when password changes
  useEffect(() => {
    if (newPassword) {
      const strength = getPasswordStrengthScore(newPassword);
      setPasswordStrength(strength);
    } else {
      setPasswordStrength({ score: 0, label: '', color: '' });
    }
  }, [newPassword]);

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch('http://localhost:8002/api/password-reset', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const result = await response.json();

      if (result.success) {
        setSuccess('Verification code sent! Please check your email.');
        setStep('pin');
      } else {
        setError(result.message || 'Failed to send verification code');
      }
    } catch (err) {
      console.error('Email submission error:', err);
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handlePinInput = (index: number, value: string) => {
    // Only allow digits
    const digit = value.replace(/\D/g, '').slice(-1);
    
    const newPin = pin.split('');
    newPin[index] = digit;
    
    const updatedPin = newPin.join('').slice(0, 6);
    setPin(updatedPin);
    setError(null);

    // Auto-focus next input
    if (digit && index < 5) {
      pinInputRefs.current[index + 1]?.focus();
    }

    // Auto-verify when 6 digits entered
    if (updatedPin.length === 6) {
      verifyPin(updatedPin);
    }
  };

  const handlePinPaste = (event: React.ClipboardEvent) => {
    event.preventDefault();
    const pastedData = event.clipboardData.getData('text').replace(/\D/g, '').slice(0, 6);
    
    if (pastedData.length > 0) {
      setPin(pastedData);
      setError(null);
      
      // Focus the next empty input or last input
      const nextIndex = Math.min(pastedData.length, 5);
      pinInputRefs.current[nextIndex]?.focus();

      // Auto-verify if 6 digits pasted
      if (pastedData.length === 6) {
        verifyPin(pastedData);
      }
    }
  };

  const verifyPin = async (pinCode: string) => {
    if (pinCode.length !== 6) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('http://localhost:8002/api/password-reset', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, pin: pinCode }),
      });

      const result = await response.json();

      if (result.success) {
        setSuccess('PIN verified! Please set your new password.');
        setStep('password');
      } else {
        setError(result.message || 'Invalid PIN code');
        setPin(''); // Clear PIN on error
        pinInputRefs.current[0]?.focus();
      }
    } catch (err) {
      console.error('PIN verification error:', err);
      setError('Network error. Please try again.');
      setPin('');
      pinInputRefs.current[0]?.focus();
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('🔐 Password submit triggered!', { newPassword, confirmPassword, email, step });
    setLoading(true);
    setError(null);

    // Check if email is available
    if (!email) {
      console.error('❌ Email is missing!', { email, step });
      setError('Email is missing. Please start over.');
      setLoading(false);
      return;
    }

    // Validate passwords match (trim whitespace and normalize)
    const trimmedNewPassword = newPassword.trim();
    const trimmedConfirmPassword = confirmPassword.trim();

    console.log('🔍 Password comparison debug:', {
      newPassword: newPassword,
      confirmPassword: confirmPassword,
      trimmedNew: trimmedNewPassword,
      trimmedConfirm: trimmedConfirmPassword,
      lengthNew: newPassword.length,
      lengthConfirm: confirmPassword.length,
      match: trimmedNewPassword === trimmedConfirmPassword
    });

    if (trimmedNewPassword !== trimmedConfirmPassword) {
      setError('Passwords do not match');
      setLoading(false);
      return;
    }

    // Validate password strength
    const validation = validatePasswordStrength(trimmedNewPassword);
    console.log('🔍 Password validation result:', validation);
    if (!validation.valid) {
      console.log('❌ Password validation failed:', validation.errors);
      setError(validation.errors[0]);
      setLoading(false);
      return;
    }

    console.log('✅ Password validation passed, proceeding to API call...');

    try {
      console.log('🚀 Making API call to reset password...', { email, trimmedNewPassword: '***' });
      const response = await fetch('http://localhost:8002/api/password-reset', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, newPassword: trimmedNewPassword }),
      });

      const result = await response.json();
      console.log('📨 API response received:', result);

      if (result.success) {
        setSuccess('Password reset successful! Logging you in...');

        // Wait a moment for the password update to propagate
        setTimeout(async () => {
          try {
            console.log('🔐 Attempting automatic login after password reset...');
            const { user, error: loginError } = await signInWithEmailEnhanced(email, trimmedNewPassword);

            if (loginError) {
              console.error('❌ Auto-login error:', loginError.message);
              setSuccess('Password reset successful! Please log in with your new password.');
              setTimeout(() => onResetSuccess(), 2000);
            } else if (user && onLoginSuccess) {
              console.log('✅ Auto-login successful after password reset!', user);
              setSuccess('Password reset successful! Welcome back!');
              setTimeout(() => onLoginSuccess(user), 1500);
            } else {
              // Fallback to normal reset success
              setSuccess('Password reset successful! Please log in with your new password.');
              setTimeout(() => onResetSuccess(), 1500);
            }
          } catch (loginErr) {
            console.error('❌ Auto-login exception:', loginErr);
            setSuccess('Password reset successful! Please log in with your new password.');
            setTimeout(() => onResetSuccess(), 2000);
          }
        }, 1000); // Wait 1 second for password update to propagate
      } else {
        setError(result.message || 'Failed to reset password');
      }
    } catch (err) {
      console.error('❌ Password reset error:', err);
      console.error('❌ Error details:', {
        message: err.message,
        stack: err.stack,
        name: err.name
      });
      setError('Network error. Please try again.');
    } finally {
      console.log('🏁 Password reset function completed, setting loading to false');
      setLoading(false);
    }
  };

  const renderEmailStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-2">Reset Your Password</h2>
        <p className="text-gray-400">
          Enter your email address and we'll send you a verification code
        </p>
      </div>

      <form onSubmit={handleEmailSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Email Address
          </label>
          <input
            type="email"
            required
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            placeholder="Enter your email address"
            disabled={loading}
          />
        </div>

        <button
          type="submit"
          disabled={loading || !email}
          className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-600 disabled:to-gray-600 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 disabled:cursor-not-allowed"
        >
          {loading ? 'Sending...' : 'Send Verification Code'}
        </button>
      </form>

      <div className="text-center">
        <button
          type="button"
          onClick={onBack}
          className="text-gray-400 hover:text-gray-300 text-sm transition-colors duration-200"
        >
          Back to Login
        </button>
      </div>
    </div>
  );

  const renderPinStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-2">Enter Verification Code</h2>
        <p className="text-gray-400">
          We've sent a 6-digit code to <span className="text-white">{email}</span>
        </p>
      </div>

      <div className="space-y-4">
        <div className="flex justify-center space-x-3">
          {[0, 1, 2, 3, 4, 5].map((index) => (
            <input
              key={index}
              ref={(el) => (pinInputRefs.current[index] = el)}
              type="text"
              inputMode="numeric"
              maxLength={1}
              value={pin[index] || ''}
              onChange={(e) => handlePinInput(index, e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Backspace' && !pin[index] && index > 0) {
                  pinInputRefs.current[index - 1]?.focus();
                }
              }}
              onPaste={handlePinPaste}
              className="w-12 h-12 text-center text-xl font-bold bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              disabled={loading}
            />
          ))}
        </div>

        <div className="text-center">
          <button
            type="button"
            onClick={() => {
              setStep('email');
              setPin('');
              setError(null);
            }}
            className="text-gray-400 hover:text-gray-300 text-sm transition-colors duration-200"
          >
            Didn't receive the code? Try again
          </button>
        </div>
      </div>
    </div>
  );

  const renderPasswordStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-2">Set New Password</h2>
        <p className="text-gray-400">
          Choose a strong password for your account
        </p>
      </div>

      <form onSubmit={handlePasswordSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            New Password
          </label>
          <div className="relative">
            <input
              type={showPassword ? 'text' : 'password'}
              required
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              className="w-full px-4 py-3 pr-12 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              placeholder="Enter new password"
              disabled={loading}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
            >
              {showPassword ? '👁️' : '👁️‍🗨️'}
            </button>
          </div>
          {newPassword && (
            <div className="mt-2">
              <div className="flex items-center space-x-2">
                <div className="flex-1 bg-gray-700 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${passwordStrength.color}`}
                    style={{ width: `${(passwordStrength.score / 4) * 100}%` }}
                  />
                </div>
                <span className={`text-sm font-medium ${passwordStrength.color ? passwordStrength.color.replace('bg-', 'text-') : 'text-gray-400'}`}>
                  {passwordStrength.label}
                </span>
              </div>
            </div>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Confirm Password
          </label>
          <div className="relative">
            <input
              type={showConfirmPassword ? 'text' : 'password'}
              required
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              className="w-full px-4 py-3 pr-12 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              placeholder="Confirm new password"
              disabled={loading}
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
            >
              {showConfirmPassword ? '👁️' : '👁️‍🗨️'}
            </button>
          </div>

          {/* Password match indicator */}
          {showPasswordMatch && (
            <div className={`text-sm mt-2 ${passwordsMatch ? 'text-green-400' : 'text-red-400'}`}>
              {passwordsMatch ? '✅ Passwords match' : '❌ Passwords do not match'}
            </div>
          )}
        </div>

        <button
          type="submit"
          disabled={loading || !newPassword || !confirmPassword || !passwordsMatch}
          onClick={(e) => {
            console.log('🔘 Button clicked!', { loading, newPassword: !!newPassword, confirmPassword: !!confirmPassword, passwordsMatch });
            // Don't prevent default - let form submission handle it
          }}
          className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-600 disabled:to-gray-600 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 disabled:cursor-not-allowed"
        >
          {loading ? 'Updating...' : 'Update Password'}
        </button>
      </form>
    </div>
  );

  return (
    <div className="w-full max-w-md mx-auto bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-xl p-8">
      {/* Progress indicator */}
      <div className="flex justify-center mb-6">
        <div className="flex space-x-2">
          {['email', 'pin', 'password'].map((stepName, index) => (
            <div
              key={stepName}
              className={`w-3 h-3 rounded-full transition-colors duration-200 ${
                step === stepName
                  ? 'bg-purple-500'
                  : ['email', 'pin', 'password'].indexOf(step) > index
                  ? 'bg-green-500'
                  : 'bg-gray-600'
              }`}
            />
          ))}
        </div>
      </div>

      {/* Error/Success Messages */}
      {error && (
        <div className="mb-4 p-3 bg-red-500/20 border border-red-500/50 rounded-lg text-red-300 text-sm">
          {error}
        </div>
      )}

      {success && (
        <div className="mb-4 p-3 bg-green-500/20 border border-green-500/50 rounded-lg text-green-300 text-sm">
          {success}
        </div>
      )}

      {/* Step Content */}
      {step === 'email' && renderEmailStep()}
      {step === 'pin' && renderPinStep()}
      {step === 'password' && renderPasswordStep()}
    </div>
  );
};
