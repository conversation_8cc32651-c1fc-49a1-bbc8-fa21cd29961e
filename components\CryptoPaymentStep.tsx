import React, { useState } from 'react';
import { supabase } from '../lib/supabase';

interface PaymentNetwork {
  id: string;
  name: string;
  technical: string;
  icon: string;
  wallet_address: string;
}

interface CurrentPhase {
  id: number;
  phase_number: number;
  phase_name: string;
  price_per_share: number;
  total_shares_available: number;
  shares_sold: number;
  is_active: boolean;
}

interface User {
  id: number;
  email: string;
  username?: string;
}

interface CryptoPaymentStepProps {
  amount: string;
  shares: number;
  network: PaymentNetwork;
  currentPhase: CurrentPhase | null;
  user: User | null;
  onBack: () => void;
  onComplete: () => void;
}

const CryptoPaymentStep: React.FC<CryptoPaymentStepProps> = ({
  amount,
  shares,
  network,
  currentPhase,
  user,
  onBack,
  onComplete
}) => {
  const [proofFile, setProofFile] = useState<File | null>(null);
  const [senderWallet, setSenderWallet] = useState('');
  const [transactionHash, setTransactionHash] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const totalCost = shares * (currentPhase?.price_per_share || 0);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        setError('Please upload a valid image file (JPG, PNG, WEBP)');
        return;
      }
      
      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setError('File size must be less than 5MB');
        return;
      }
      
      setProofFile(file);
      setError('');
    }
  };

  const handleSubmitPayment = async () => {
    if (!proofFile) {
      setError('Please upload payment proof');
      return;
    }

    if (!senderWallet.trim()) {
      setError('Please enter your wallet address');
      return;
    }

    if (!transactionHash.trim()) {
      setError('Please enter the transaction hash');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Get user ID first (same logic as BankTransferStep)
      console.log('🔍 DEBUG: User object in CryptoPaymentStep:', {
        hasUser: !!user,
        userId: user?.id,
        userKeys: user ? Object.keys(user) : [],
        hasUserMetadata: !!user?.user_metadata,
        hasDatabaseUser: !!user?.database_user,
        userMetadataUserId: user?.user_metadata?.user_id,
        databaseUserId: user?.database_user?.id
      });

      let userId = null;
      if (user?.database_user?.id) {
        userId = user.database_user.id;
        console.log('✅ Using database_user.id for crypto payment:', userId);
      } else if (user?.user_metadata?.user_id) {
        userId = user.user_metadata.user_id;
        console.log('✅ Using user_metadata.user_id for crypto payment:', userId);
      } else if (user?.id?.startsWith('db_')) {
        userId = parseInt(user.id.replace('db_', ''));
        console.log('✅ Using db_ prefixed ID for crypto payment:', userId);
      } else {
        console.log('❌ No valid user ID found for crypto payment');
        throw new Error('User ID not found. Please ensure you are properly logged in.');
      }

      // Upload proof file to Supabase Storage (matching Telegram bot format)
      const fileExt = proofFile.name.split('.').pop();
      const timestamp = Date.now();
      const fileName = `payment_${userId}_${timestamp}.${fileExt}`;
      const filePath = fileName; // Store directly in bucket root like Telegram bot

      console.log('📤 Uploading file to Supabase Storage:', filePath);

      // Upload to existing 'proof' bucket used by Telegram bot

      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('proof')
        .upload(filePath, proofFile);

      if (uploadError) {
        console.error('Upload error:', uploadError);

        // Provide specific error messages based on the error type
        if (uploadError.message.includes('Bucket not found')) {
          throw new Error('Proof bucket not found. Please check Supabase Storage configuration.');
        } else if (uploadError.message.includes('row-level security policy')) {
          throw new Error('Storage permission denied. Please run the storage policy fix script: sql/fix_storage_policies.sql');
        } else if (uploadError.message.includes('new row violates')) {
          throw new Error('Upload permission denied. Please contact admin to configure storage policies.');
        }

        throw new Error('Failed to upload proof file: ' + uploadError.message);
      }

      console.log('✅ File uploaded successfully:', uploadData);

      // Get public URL for the uploaded file
      const { data: { publicUrl } } = supabase.storage
        .from('proof')
        .getPublicUrl(filePath);

      const fileUrl = publicUrl;
      console.log('📎 File URL:', fileUrl);

      // Use the userId we already extracted above
      console.log('✅ Using userId for payment data:', userId);

      // Create payment transaction directly in Supabase
      const paymentData = {
        user_id: userId, // Use integer ID we extracted above
        amount: totalCost,
        shares_to_purchase: shares,
        network: network.id,
        currency: 'USDT',
        sender_wallet: senderWallet.trim(),
        receiver_wallet: network.wallet_address,
        transaction_hash: transactionHash.trim(),
        screenshot_url: fileUrl,
        status: 'pending',
        created_at: new Date().toISOString()
      };

      const { data: paymentResult, error: paymentError } = await supabase
        .from('crypto_payment_transactions')
        .insert([paymentData])
        .select()
        .single();

      if (paymentError) {
        console.error('Payment creation error:', paymentError);
        throw new Error('Failed to create payment transaction: ' + paymentError.message);
      }

      console.log('Payment created:', paymentResult);
      
      onComplete();
    } catch (err) {
      console.error('Payment submission error:', err);
      setError(err instanceof Error ? err.message : 'Failed to submit payment');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <h2 style={{ fontSize: '20px', fontWeight: 'bold', marginBottom: '20px', textAlign: 'center' }}>
        {network.icon} {network.name} Payment
      </h2>

      {/* Payment Instructions */}
      <div style={{
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        border: '1px solid rgba(59, 130, 246, 0.3)',
        borderRadius: '12px',
        padding: '20px',
        marginBottom: '20px'
      }}>
        <h3 style={{ color: '#60a5fa', fontWeight: 'bold', margin: '0 0 16px 0' }}>
          💰 PAYMENT INSTRUCTIONS:
        </h3>
        <div style={{ color: '#d1d5db', fontSize: '14px', lineHeight: '1.6' }}>
          <p style={{ margin: '0 0 8px 0' }}>• Amount: {totalCost.toFixed(2)} USDT</p>
          <p style={{ margin: '0 0 8px 0' }}>• Network: {network.icon} {network.name} ({network.technical})</p>
          <p style={{ margin: '0 0 8px 0' }}>• Shares: {shares}</p>
          <p style={{ margin: 0 }}>• No additional fees</p>
        </div>
      </div>

      {/* Wallet Address */}
      <div style={{ marginBottom: '20px' }}>
        <label style={{ display: 'block', color: '#9ca3af', marginBottom: '8px', fontSize: '14px' }}>
          Company Wallet Address ({network.technical})
        </label>
        <div style={{
          display: 'flex',
          backgroundColor: 'rgba(55, 65, 81, 0.5)',
          borderRadius: '8px',
          border: '2px solid #374151'
        }}>
          <input
            type="text"
            value={network.wallet_address}
            readOnly
            style={{
              flex: 1,
              padding: '12px',
              backgroundColor: 'transparent',
              border: 'none',
              color: 'white',
              fontSize: '14px',
              fontFamily: 'monospace'
            }}
          />
          <button
            onClick={() => copyToClipboard(network.wallet_address)}
            style={{
              padding: '12px 16px',
              backgroundColor: '#3b82f6',
              border: 'none',
              borderRadius: '0 6px 6px 0',
              color: 'white',
              fontSize: '14px',
              cursor: 'pointer'
            }}
          >
            Copy
          </button>
        </div>
      </div>

      {/* Sender Wallet Input */}
      <div style={{ marginBottom: '20px' }}>
        <label style={{ display: 'block', color: '#9ca3af', marginBottom: '8px', fontSize: '14px' }}>
          Your Wallet Address (where you're sending from)
        </label>
        <input
          type="text"
          value={senderWallet}
          onChange={(e) => setSenderWallet(e.target.value)}
          placeholder="Enter your wallet address"
          style={{
            width: '100%',
            padding: '12px',
            backgroundColor: 'rgba(55, 65, 81, 0.5)',
            border: '2px solid #374151',
            borderRadius: '8px',
            color: 'white',
            fontSize: '14px'
          }}
        />
      </div>

      {/* Transaction Hash Input */}
      <div style={{ marginBottom: '20px' }}>
        <label style={{ display: 'block', color: '#9ca3af', marginBottom: '8px', fontSize: '14px' }}>
          Transaction Hash (from your payment)
        </label>
        <input
          type="text"
          value={transactionHash}
          onChange={(e) => setTransactionHash(e.target.value)}
          placeholder="Enter the transaction hash"
          style={{
            width: '100%',
            padding: '12px',
            backgroundColor: 'rgba(55, 65, 81, 0.5)',
            border: '2px solid #374151',
            borderRadius: '8px',
            color: 'white',
            fontSize: '14px'
          }}
        />
      </div>

      {/* Proof Upload */}
      <div style={{ marginBottom: '20px' }}>
        <label style={{ display: 'block', color: '#9ca3af', marginBottom: '8px', fontSize: '14px' }}>
          Upload Payment Proof (Screenshot)
        </label>
        <input
          type="file"
          accept="image/*"
          onChange={handleFileUpload}
          style={{
            width: '100%',
            padding: '12px',
            backgroundColor: 'rgba(55, 65, 81, 0.5)',
            border: '2px solid #374151',
            borderRadius: '8px',
            color: 'white',
            fontSize: '14px'
          }}
        />
        {proofFile && (
          <p style={{ color: '#10b981', fontSize: '14px', marginTop: '8px' }}>
            ✅ File selected: {proofFile.name}
          </p>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div style={{
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          border: '1px solid rgba(239, 68, 68, 0.3)',
          borderRadius: '8px',
          padding: '12px',
          marginBottom: '20px',
          color: '#f87171'
        }}>
          ❌ {error}
        </div>
      )}

      {/* Warning */}
      <div style={{
        backgroundColor: 'rgba(245, 158, 11, 0.1)',
        border: '1px solid rgba(245, 158, 11, 0.3)',
        borderRadius: '12px',
        padding: '16px',
        marginBottom: '20px'
      }}>
        <h3 style={{ color: '#fbbf24', fontWeight: 'bold', margin: '0 0 8px 0' }}>
          ⚠️ IMPORTANT:
        </h3>
        <p style={{ color: '#d1d5db', fontSize: '14px', margin: 0 }}>
          Make sure to send USDT on the {network.name} network only. Wrong network = lost funds.
        </p>
      </div>

      {/* Submit Button */}
      <button
        onClick={handleSubmitPayment}
        disabled={loading || !proofFile || !senderWallet.trim()}
        style={{
          width: '100%',
          padding: '16px',
          backgroundColor: (!loading && proofFile && senderWallet.trim()) ? '#10b981' : '#374151',
          border: 'none',
          borderRadius: '12px',
          color: 'white',
          fontSize: '18px',
          fontWeight: 'bold',
          cursor: (!loading && proofFile && senderWallet.trim()) ? 'pointer' : 'not-allowed',
          marginBottom: '16px'
        }}
      >
        {loading ? 'Submitting Payment...' : 'Submit Payment for Review'}
      </button>

      {/* Back Button */}
      <button
        onClick={onBack}
        disabled={loading}
        style={{
          width: '100%',
          padding: '12px',
          backgroundColor: 'transparent',
          border: '2px solid #374151',
          borderRadius: '8px',
          color: '#9ca3af',
          fontSize: '16px',
          cursor: loading ? 'not-allowed' : 'pointer'
        }}
      >
        ← Back to Network Selection
      </button>
    </div>
  );
};

export default CryptoPaymentStep;
