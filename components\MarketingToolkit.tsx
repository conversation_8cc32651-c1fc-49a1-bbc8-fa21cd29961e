import React, { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'
import { SocialMediaContentGenerator } from './marketing/SocialMediaContentGenerator'
import { TrainingCenter } from './marketing/TrainingCenter'

interface MarketingToolkitProps {
  user: any
  getReferralUsername: (user: any) => string
}

// Social media platform configurations for referral links
const SOCIAL_PLATFORMS = [
  { id: 'facebook', name: 'Facebook', icon: '📘', color: '#1877F2', description: 'Facebook posts and ads' },
  { id: 'instagram', name: 'Instagram', icon: '📷', color: '#E4405F', description: 'Instagram stories and posts' },
  { id: 'twitter', name: 'Twitter/X', icon: '🐦', color: '#1DA1F2', description: 'Twitter posts and threads' },
  { id: 'linkedin', name: 'LinkedIn', icon: '💼', color: '#0A66C2', description: 'Professional networking' },
  { id: 'youtube', name: 'YouTube', icon: '📺', color: '#FF0000', description: 'Video descriptions and comments' },
  { id: 'email', name: 'Email', icon: '📧', color: '#34495E', description: 'Email newsletters and campaigns' },
  { id: 'whatsapp', name: 'WhatsApp', icon: '💬', color: '#25D366', description: 'WhatsApp messages and status' },
  { id: 'telegram', name: 'Telegram', icon: '✈️', color: '#0088CC', description: 'Telegram channels and groups' }
]

export const MarketingToolkit: React.FC<MarketingToolkitProps> = ({ user, getReferralUsername }) => {
  const [activeTab, setActiveTab] = useState<'generator' | 'analytics' | 'content' | 'training'>('generator')
  const [copiedLink, setCopiedLink] = useState<string>('')
  const [referralAnalytics, setReferralAnalytics] = useState<any[]>([])
  const [analyticsLoading, setAnalyticsLoading] = useState(false)
  const [telegramBotLink, setTelegramBotLink] = useState<string>('')
  const [webReferralLink, setWebReferralLink] = useState<string>('')

  // Generate proper referral links
  const generateReferralLinks = () => {
    const username = getReferralUsername(user)

    // Web registration referral link
    const webLink = `https://aureus.africa/register?ref=${username}`

    // Telegram bot referral link
    const telegramLink = `https://t.me/AureusAllianceBot?start=${username}`

    setWebReferralLink(webLink)
    setTelegramBotLink(telegramLink)

    return { webLink, telegramLink }
  }

  // Copy link to clipboard
  const copyToClipboard = async (link: string, platformName: string) => {
    try {
      await navigator.clipboard.writeText(link)
      setCopiedLink(platformName)
      setTimeout(() => setCopiedLink(''), 2000)
    } catch (error) {
      console.error('Failed to copy link:', error)
    }
  }

  // Generate platform-specific sharing messages with improved UX
  const generatePlatformMessage = (platform: string, referralLink: string) => {
    switch (platform) {
      case 'whatsapp':
        return `💰 Own shares in REAL gold mining operations!

🏆 LEGITIMATE BUSINESS OPPORTUNITY:
✅ CIPC-registered South African company
✅ 2x200tph gold plants by Jan 2026
✅ 1 ton gold target by March 2026
✅ $20-$50 dividend per share projected
✅ $5-$50,000+ share purchase range

🔥 Join shareholders in Africa's gold boom!

⚡ SECURE SHARES: ${referralLink}

🚨 PHASE 1 PRICING ENDS SOON! 🚨`

      case 'email':
        return `Subject: Exclusive Gold Mining Share Opportunity - Aureus Alliance Holdings

Dear Friend,

I wanted to share an exclusive gold mining share ownership opportunity with you.

Aureus Alliance Holdings (Pty) Ltd is a CIPC-registered South African company with operational gold mining sites. They're offering Phase 1 share pricing before expanding to full production capacity.

Key Highlights:
• CIPC-registered company with physical gold extraction
• Target: 2 x 200tph gold wash plants by January 2026
• Projected minimum 1 ton gold yield by March 2026
• Expanding to 200+ mining sites by 2030
• Share purchase range: $5-$50,000+

Projected Shareholder Benefits:
• $20-$50 dividend per share from 1 ton gold production
• 15% USDT + 15% Share referral commissions
• Dividends from actual gold production & sales
• Ownership in expanding mining operations

This is a legitimate business opportunity with real gold assets and operational targets.

Learn more and secure your shares: ${referralLink}

Best regards`

      case 'social':
        return `💰 Own shares in Aureus Alliance Holdings - CIPC-registered South African gold mining company!

✅ 2x200tph plants by Jan 2026
✅ 1 ton gold target by March 2026
✅ $20-$50 dividend per share projected
✅ Real business, real gold, real profits

🔥 Join the gold boom: ${referralLink}

🚨 PHASE 1 PRICING ENDS SOON!

#GoldMining #ShareOwnership #AureusAfrica #GoldShares #WealthBuilding #Mining #Investment`

      case 'sms':
        return `Own shares in real South African gold mines. CIPC-registered company. $20-$50 dividend/share projected. $5+ shares available. Secure now: ${referralLink} 🚨 PHASE 1 ENDING!`

      default:
        return `💰 Own shares in Aureus Alliance Holdings - CIPC-registered South African gold mining company! Learn more: ${referralLink}`
    }
  }

  // Generate social media sharing URLs
  const generateSocialShareUrl = (platform: string, referralLink: string) => {
    const shareText = encodeURIComponent(generatePlatformMessage('social', referralLink))
    const encodedUrl = encodeURIComponent(referralLink)

    switch (platform) {
      case 'facebook':
        return `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}&quote=${shareText}`
      case 'twitter':
        return `https://twitter.com/intent/tweet?text=${shareText}`
      case 'linkedin':
        return `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}&summary=${shareText}`
      case 'whatsapp':
        return `https://wa.me/?text=${encodeURIComponent(generatePlatformMessage('whatsapp', referralLink))}`
      case 'telegram':
        return `https://t.me/share/url?url=${encodedUrl}&text=${shareText}`
      default:
        return referralLink
    }
  }

  // Load real referral analytics from database
  const loadReferralAnalytics = async () => {
    setAnalyticsLoading(true)
    try {
      const userId = user.database_user?.id
      if (!userId) {
        console.log('No user ID found for analytics')
        setReferralAnalytics([])
        return
      }

      // Get referral analytics from database
      const { data: analytics, error } = await supabase
        .from('referrals')
        .select(`
          *,
          referred_user:users!referrals_referred_id_fkey(
            username,
            email,
            full_name,
            created_at
          )
        `)
        .eq('referrer_id', userId)
        .eq('status', 'active')

      if (error) {
        console.error('Error loading referral analytics:', error)
        setReferralAnalytics([])
        return
      }

      if (error) {
        console.error('Error loading referral analytics:', error)
        setReferralAnalytics([])
        return
      }

      // Get share purchases for referred users
      const referredUserIds = analytics?.map(r => r.referred_id) || []
      let purchases = []

      if (referredUserIds.length > 0) {
        const { data: purchaseData, error: purchaseError } = await supabase
          .from('aureus_share_purchases')
          .select('*')
          .in('user_id', referredUserIds)
          .eq('status', 'active')

        if (!purchaseError) {
          purchases = purchaseData || []
        }
      }

      // Process analytics data
      const processedAnalytics = analytics?.map(referral => {
        const userPurchases = purchases.filter(p => p.user_id === referral.referred_id)
        return {
          referral_code: referral.referral_code,
          referred_user: referral.referred_user,
          total_commission: referral.total_commission || 0,
          purchases: userPurchases,
          created_at: referral.created_at
        }
      }) || []

      setReferralAnalytics(processedAnalytics)
    } catch (error) {
      console.error('Failed to load analytics:', error)
      setReferralAnalytics([])
    } finally {
      setAnalyticsLoading(false)
    }
  }

  // Generate referral links on component mount
  useEffect(() => {
    generateReferralLinks()
  }, [user])

  // Load analytics when analytics tab is active
  useEffect(() => {
    if (activeTab === 'analytics') {
      loadReferralAnalytics()
    }
  }, [activeTab, user])

  return (
    <div style={{
      backgroundColor: 'rgba(31, 41, 55, 0.9)',
      borderRadius: '16px',
      padding: '32px',
      marginBottom: '32px',
      border: '1px solid #374151'
    }}>
      {/* Header */}
      <div style={{ marginBottom: '32px', textAlign: 'center' }}>
        <h2 style={{ 
          fontSize: '28px', 
          fontWeight: 'bold', 
          color: 'white', 
          margin: '0 0 12px 0',
          background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent'
        }}>
          🚀 Marketing Toolkit
        </h2>
        <p style={{ color: '#9ca3af', fontSize: '16px', margin: 0 }}>
          Professional-grade marketing tools to maximize your referral success
        </p>
      </div>

      {/* Tab Navigation */}
      <div style={{
        display: 'flex',
        gap: '8px',
        marginBottom: '32px',
        borderBottom: '1px solid #374151',
        paddingBottom: '16px'
      }}>
        {[
          { id: 'generator', label: '🔗 Link Generator', desc: 'Create campaign links' },
          { id: 'analytics', label: '📊 Analytics', desc: 'Track performance' },
          { id: 'content', label: '✨ AI Content', desc: 'Generate marketing content' },
          { id: 'training', label: '🎓 Training', desc: 'Learn best practices' }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            style={{
              flex: 1,
              padding: '16px',
              backgroundColor: activeTab === tab.id ? 'rgba(59, 130, 246, 0.2)' : 'transparent',
              border: activeTab === tab.id ? '1px solid #3b82f6' : '1px solid #374151',
              borderRadius: '12px',
              color: activeTab === tab.id ? '#60a5fa' : '#9ca3af',
              cursor: 'pointer',
              transition: 'all 0.2s',
              textAlign: 'center'
            }}
          >
            <div style={{ fontSize: '14px', fontWeight: '600', marginBottom: '4px' }}>
              {tab.label}
            </div>
            <div style={{ fontSize: '12px', opacity: 0.8 }}>
              {tab.desc}
            </div>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      {activeTab === 'generator' && (
        <div>
          {/* Main Referral Links */}
          <div style={{ marginBottom: '32px' }}>
            <h3 style={{ color: 'white', fontSize: '20px', marginBottom: '16px' }}>
              🔗 Your Referral Links
            </h3>

            {/* Web Registration Link */}
            <div style={{
              backgroundColor: 'rgba(55, 65, 81, 0.5)',
              borderRadius: '12px',
              padding: '20px',
              marginBottom: '16px',
              border: '1px solid #4b5563'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                <span style={{ fontSize: '24px', marginRight: '12px' }}>🌐</span>
                <div>
                  <h4 style={{ color: 'white', fontSize: '16px', fontWeight: '600', margin: '0 0 4px 0' }}>
                    Web Registration Link
                  </h4>
                  <p style={{ color: '#9ca3af', fontSize: '12px', margin: 0 }}>
                    Direct link to website registration with your referral code
                  </p>
                </div>
              </div>

              <div style={{
                backgroundColor: 'rgba(31, 41, 55, 0.8)',
                borderRadius: '8px',
                padding: '12px',
                marginBottom: '12px',
                fontSize: '13px',
                color: '#d1d5db',
                wordBreak: 'break-all',
                fontFamily: 'monospace'
              }}>
                {webReferralLink}
              </div>

              <button
                onClick={() => copyToClipboard(webReferralLink, 'Web')}
                style={{
                  width: '100%',
                  padding: '10px 16px',
                  backgroundColor: copiedLink === 'Web' ? '#10b981' : '#3b82f6',
                  border: 'none',
                  borderRadius: '8px',
                  color: 'white',
                  fontSize: '14px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
              >
                {copiedLink === 'Web' ? '✅ Copied!' : '📋 Copy Web Link'}
              </button>
            </div>

            {/* Telegram Bot Link */}
            <div style={{
              backgroundColor: 'rgba(55, 65, 81, 0.5)',
              borderRadius: '12px',
              padding: '20px',
              marginBottom: '16px',
              border: '1px solid #4b5563'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                <span style={{ fontSize: '24px', marginRight: '12px' }}>✈️</span>
                <div>
                  <h4 style={{ color: 'white', fontSize: '16px', fontWeight: '600', margin: '0 0 4px 0' }}>
                    Telegram Bot Link
                  </h4>
                  <p style={{ color: '#9ca3af', fontSize: '12px', margin: 0 }}>
                    Direct link to Telegram bot with your referral code
                  </p>
                </div>
              </div>

              <div style={{
                backgroundColor: 'rgba(31, 41, 55, 0.8)',
                borderRadius: '8px',
                padding: '12px',
                marginBottom: '12px',
                fontSize: '13px',
                color: '#d1d5db',
                wordBreak: 'break-all',
                fontFamily: 'monospace'
              }}>
                {telegramBotLink}
              </div>

              <button
                onClick={() => copyToClipboard(telegramBotLink, 'Telegram')}
                style={{
                  width: '100%',
                  padding: '10px 16px',
                  backgroundColor: copiedLink === 'Telegram' ? '#10b981' : '#0088CC',
                  border: 'none',
                  borderRadius: '8px',
                  color: 'white',
                  fontSize: '14px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
              >
                {copiedLink === 'Telegram' ? '✅ Copied!' : '📋 Copy Telegram Link'}
              </button>
            </div>
          </div>

          {/* Social Media Sharing Options */}
          <div style={{ marginBottom: '32px' }}>
            <h3 style={{ color: 'white', fontSize: '20px', marginBottom: '16px' }}>
              📱 Share on Social Media
            </h3>
            <p style={{ color: '#9ca3af', fontSize: '14px', marginBottom: '16px' }}>
              Click to share your referral link directly on social media platforms
            </p>

            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
              gap: '16px'
            }}>
              {SOCIAL_PLATFORMS.map(platform => (
                <div
                  key={platform.id}
                  style={{
                    backgroundColor: 'rgba(55, 65, 81, 0.5)',
                    borderRadius: '12px',
                    padding: '20px',
                    border: '1px solid #4b5563'
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
                    <span style={{ fontSize: '24px', marginRight: '12px' }}>{platform.icon}</span>
                    <div>
                      <h4 style={{
                        color: 'white',
                        fontSize: '16px',
                        fontWeight: '600',
                        margin: '0 0 4px 0'
                      }}>
                        {platform.name}
                      </h4>
                      <p style={{
                        color: '#9ca3af',
                        fontSize: '12px',
                        margin: 0
                      }}>
                        {platform.description}
                      </p>
                    </div>
                  </div>

                  <div style={{ display: 'flex', gap: '8px' }}>
                    <button
                      onClick={() => copyToClipboard(webReferralLink, platform.name)}
                      style={{
                        flex: 1,
                        padding: '10px 12px',
                        backgroundColor: copiedLink === platform.name ? '#10b981' : 'rgba(55, 65, 81, 0.8)',
                        border: '1px solid #4b5563',
                        borderRadius: '8px',
                        color: copiedLink === platform.name ? 'white' : '#d1d5db',
                        fontSize: '12px',
                        fontWeight: '500',
                        cursor: 'pointer',
                        transition: 'all 0.2s'
                      }}
                    >
                      {copiedLink === platform.name ? '✅ Copied!' : '📋 Copy Link'}
                    </button>

                    <button
                      onClick={() => window.open(generateSocialShareUrl(platform.id, webReferralLink), '_blank', 'width=600,height=400')}
                      style={{
                        flex: 1,
                        padding: '10px 12px',
                        backgroundColor: platform.color,
                        border: 'none',
                        borderRadius: '8px',
                        color: 'white',
                        fontSize: '12px',
                        fontWeight: '500',
                        cursor: 'pointer',
                        transition: 'all 0.2s'
                      }}
                    >
                      📤 Share Now
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Multi-Platform Message Templates */}
          <div style={{ marginBottom: '32px' }}>
            <h3 style={{ color: 'white', fontSize: '20px', marginBottom: '16px' }}>
              📱 Professional Message Templates
            </h3>
            <p style={{ color: '#9ca3af', fontSize: '14px', marginBottom: '16px' }}>
              Ready-to-use message templates optimized for each platform. Click to copy the complete message.
            </p>

            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
              gap: '16px'
            }}>
              {[
                {
                  id: 'whatsapp',
                  name: '💬 WhatsApp/Messenger',
                  description: 'Optimized for mobile messaging',
                  icon: '💬',
                  color: '#25D366'
                },
                {
                  id: 'email',
                  name: '📧 Email Template',
                  description: 'Professional email format',
                  icon: '📧',
                  color: '#34495E'
                },
                {
                  id: 'social',
                  name: '📱 Social Media',
                  description: 'Twitter, Facebook, LinkedIn',
                  icon: '📱',
                  color: '#1DA1F2'
                },
                {
                  id: 'sms',
                  name: '📱 SMS/Text',
                  description: 'Ultra-concise text message',
                  icon: '📱',
                  color: '#FF6B35'
                }
              ].map(template => (
                <div
                  key={template.id}
                  style={{
                    backgroundColor: 'rgba(55, 65, 81, 0.5)',
                    borderRadius: '12px',
                    padding: '20px',
                    border: '1px solid #4b5563'
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
                    <span style={{ fontSize: '24px', marginRight: '12px' }}>{template.icon}</span>
                    <div>
                      <h4 style={{
                        color: 'white',
                        fontSize: '16px',
                        fontWeight: '600',
                        margin: '0 0 4px 0'
                      }}>
                        {template.name}
                      </h4>
                      <p style={{
                        color: '#9ca3af',
                        fontSize: '12px',
                        margin: 0
                      }}>
                        {template.description}
                      </p>
                    </div>
                  </div>

                  <div style={{
                    backgroundColor: 'rgba(31, 41, 55, 0.8)',
                    borderRadius: '8px',
                    padding: '12px',
                    marginBottom: '12px',
                    fontSize: '12px',
                    color: '#d1d5db',
                    fontFamily: 'monospace',
                    maxHeight: '120px',
                    overflowY: 'auto',
                    whiteSpace: 'pre-wrap'
                  }}>
                    {generatePlatformMessage(template.id, webReferralLink).substring(0, 200)}...
                  </div>

                  <div style={{ display: 'flex', gap: '8px' }}>
                    <button
                      onClick={() => copyToClipboard(generatePlatformMessage(template.id, webReferralLink), `${template.name}_message`)}
                      style={{
                        flex: 1,
                        padding: '10px 12px',
                        backgroundColor: copiedLink === `${template.name}_message` ? '#10b981' : 'rgba(55, 65, 81, 0.8)',
                        border: '1px solid #4b5563',
                        borderRadius: '8px',
                        color: copiedLink === `${template.name}_message` ? 'white' : '#d1d5db',
                        fontSize: '12px',
                        fontWeight: '500',
                        cursor: 'pointer',
                        transition: 'all 0.2s'
                      }}
                    >
                      {copiedLink === `${template.name}_message` ? '✅ Copied!' : '📋 Copy Message'}
                    </button>

                    {template.id === 'whatsapp' && (
                      <button
                        onClick={() => window.open(generateSocialShareUrl('whatsapp', webReferralLink), '_blank')}
                        style={{
                          flex: 1,
                          padding: '10px 12px',
                          backgroundColor: template.color,
                          border: 'none',
                          borderRadius: '8px',
                          color: 'white',
                          fontSize: '12px',
                          fontWeight: '500',
                          cursor: 'pointer',
                          transition: 'all 0.2s'
                        }}
                      >
                        📤 Open WhatsApp
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

        </div>
      )}

      {activeTab === 'analytics' && (
        <div>
          {analyticsLoading ? (
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>📊</div>
              <h3 style={{ color: 'white', fontSize: '24px', marginBottom: '12px' }}>
                Loading Analytics...
              </h3>
              <div style={{
                width: '40px',
                height: '40px',
                border: '4px solid #374151',
                borderTop: '4px solid #f59e0b',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
                margin: '0 auto'
              }}></div>
            </div>
          ) : (
            <div>
              {/* Analytics Header */}
              <div style={{ marginBottom: '32px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                  <h3 style={{ color: 'white', fontSize: '24px', margin: 0 }}>
                    📊 Referral Analytics Dashboard
                  </h3>
                </div>
                <p style={{ color: '#9ca3af', fontSize: '14px', textAlign: 'center', margin: 0 }}>
                  Track your referral performance and optimize your campaigns
                </p>
              </div>

              {/* Summary Cards */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                gap: '16px',
                marginBottom: '32px'
              }}>
                {[
                  {
                    title: 'Total Clicks',
                    value: 0, // Will be calculated from actual data
                    icon: '👆',
                    color: '#3b82f6'
                  },
                  {
                    title: 'Registrations',
                    value: referralAnalytics.length,
                    icon: '👥',
                    color: '#10b981'
                  },
                  {
                    title: 'Conversions',
                    value: referralAnalytics.filter(ref => ref.purchases && ref.purchases.length > 0).length,
                    icon: '💰',
                    color: '#f59e0b'
                  },
                  {
                    title: 'Total Revenue',
                    value: `$${referralAnalytics.reduce((sum, item) => sum + (item.total_commission || 0), 0).toFixed(2)}`,
                    icon: '📈',
                    color: '#8b5cf6'
                  }
                ].map((stat, index) => (
                  <div
                    key={index}
                    style={{
                      backgroundColor: 'rgba(55, 65, 81, 0.5)',
                      borderRadius: '12px',
                      padding: '20px',
                      border: '1px solid #4b5563',
                      textAlign: 'center'
                    }}
                  >
                    <div style={{ fontSize: '32px', marginBottom: '8px' }}>{stat.icon}</div>
                    <h4 style={{
                      color: 'white',
                      fontSize: '24px',
                      fontWeight: 'bold',
                      margin: '0 0 4px 0'
                    }}>
                      {stat.value}
                    </h4>
                    <p style={{ color: '#9ca3af', fontSize: '12px', margin: 0 }}>
                      {stat.title}
                    </p>
                  </div>
                ))}
              </div>

              {/* Referral Performance Table */}
              {referralAnalytics.length > 0 ? (
                <div style={{
                  backgroundColor: 'rgba(55, 65, 81, 0.5)',
                  borderRadius: '12px',
                  padding: '24px',
                  border: '1px solid #4b5563'
                }}>
                  <h4 style={{ color: 'white', fontSize: '18px', marginBottom: '16px' }}>
                    👥 Your Referrals
                  </h4>
                  <div style={{ overflowX: 'auto' }}>
                    <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                      <thead>
                        <tr style={{ borderBottom: '1px solid #4b5563' }}>
                          <th style={{ color: '#d1d5db', fontSize: '12px', fontWeight: '500', padding: '12px 8px', textAlign: 'left' }}>Referred User</th>
                          <th style={{ color: '#d1d5db', fontSize: '12px', fontWeight: '500', padding: '12px 8px', textAlign: 'center' }}>Join Date</th>
                          <th style={{ color: '#d1d5db', fontSize: '12px', fontWeight: '500', padding: '12px 8px', textAlign: 'center' }}>Purchases</th>
                          <th style={{ color: '#d1d5db', fontSize: '12px', fontWeight: '500', padding: '12px 8px', textAlign: 'right' }}>Commission</th>
                        </tr>
                      </thead>
                      <tbody>
                        {referralAnalytics.map((referral, index) => (
                          <tr key={index} style={{ borderBottom: '1px solid #374151' }}>
                            <td style={{ color: 'white', fontSize: '14px', padding: '12px 8px' }}>
                              <div>
                                <div style={{ fontWeight: '500' }}>
                                  {referral.referred_user?.full_name || referral.referred_user?.username}
                                </div>
                                <div style={{ fontSize: '12px', color: '#9ca3af' }}>
                                  {referral.referred_user?.email}
                                </div>
                              </div>
                            </td>
                            <td style={{ color: '#d1d5db', fontSize: '14px', padding: '12px 8px', textAlign: 'center' }}>
                              {new Date(referral.created_at).toLocaleDateString()}
                            </td>
                            <td style={{ color: '#d1d5db', fontSize: '14px', padding: '12px 8px', textAlign: 'center' }}>
                              {referral.purchases?.length || 0}
                            </td>
                            <td style={{ color: '#10b981', fontSize: '14px', padding: '12px 8px', textAlign: 'right', fontWeight: '500' }}>
                              ${(referral.total_commission || 0).toFixed(2)}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              ) : (
                <div style={{
                  backgroundColor: 'rgba(55, 65, 81, 0.3)',
                  borderRadius: '12px',
                  padding: '40px',
                  textAlign: 'center',
                  border: '1px solid #4b5563'
                }}>
                  <div style={{ fontSize: '48px', marginBottom: '16px' }}>📊</div>
                  <h4 style={{ color: 'white', fontSize: '18px', marginBottom: '8px' }}>
                    No Analytics Data Yet
                  </h4>
                  <p style={{ color: '#9ca3af', fontSize: '14px' }}>
                    Start sharing your referral links to see performance data here!
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {activeTab === 'content' && (
        <SocialMediaContentGenerator
          user={user}
          getReferralUsername={getReferralUsername}
          currentPhase={null}
        />
      )}

      {activeTab === 'training' && (
        <TrainingCenter userId={user?.database_user?.id || 0} />
      )}

      {/* CSS for animations */}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  )
}
