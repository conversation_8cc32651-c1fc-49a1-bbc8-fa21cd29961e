﻿import React, { useState, useEffect } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';

// Removed all demo data creation and competition population functions
// Only use real database data through service role client

const GoldDiggersClub = () => {
  console.log('🏆 GoldDiggersClub component rendering...');

  const [allPhases, setAllPhases] = useState([]);
  const [selectedPhase, setSelectedPhase] = useState(null);
  const [competition, setCompetition] = useState(null);
  const [leaderboard, setLeaderboard] = useState([]);
  const [loading, setLoading] = useState(true);
  const [totalCommissionEarned, setTotalCommissionEarned] = useState(0);

  // Add error state for debugging
  const [error, setError] = useState(null);

  // Test function to check database access
  const testDatabaseAccess = async () => {
    console.log('🧪 Testing database access...');

    try {
      const serviceClient = getServiceRoleClient();
      console.log('🔧 Using service role client for database tests...');

      // Test 1: Can we count users?
      const { count, error: countError } = await serviceClient
        .from('users')
        .select('*', { count: 'exact', head: true });

      console.log('📊 Users count test (service role):', { count, error: countError });

      // Test 2: Can we get any users?
      const { data: sampleUsers, error: sampleError } = await serviceClient
        .from('users')
        .select('id, email, total_referrals')
        .limit(5);

      console.log('👥 Sample users test (service role):', { data: sampleUsers, error: sampleError });

      // Test 3: Can we access commission_balances?
      const { data: sampleCommissions, error: commissionError } = await serviceClient
        .from('commission_balances')
        .select('user_id, total_earned_usdt')
        .limit(5);

      console.log('💰 Sample commissions test (service role):', { data: sampleCommissions, error: commissionError });

    } catch (error) {
      console.error('❌ Database access test failed:', error);
    }
  };

  // Helper function to get commission structure based on phase number
  const getCommissionStructure = (phaseNumber) => {
    if (phaseNumber === 0) {
      return {
        usdt: 15,
        shares: 15,
        description: 'USDT Commission + Bonus NFT Shares',
        highlight: true,
        exclusiveText: 'PRESALE EXCLUSIVE!'
      };
    } else {
      return {
        usdt: 15,
        shares: 0,
        description: 'USDT Commission Only',
        highlight: false,
        exclusiveText: null
      };
    }
  };

  useEffect(() => {
    testDatabaseAccess(); // Run database tests first
    fetchData();
  }, []);

  // Debug: Monitor leaderboard state changes
  useEffect(() => {
    console.log('🔄 Leaderboard state changed:', leaderboard);
  }, [leaderboard]);

  // Debug: Monitor competition state changes
  useEffect(() => {
    console.log('🏆 Competition state changed:', competition);
  }, [competition]);

  // Debug: Monitor selected phase changes
  useEffect(() => {
    console.log('📊 Selected phase changed:', selectedPhase);
  }, [selectedPhase]);

  // Fetch real leaderboard data - top 10 commission earners for specific phase excluding user ID 4
  const fetchLeaderboardData = async (phaseId = null) => {
    const timestamp = new Date().toISOString();
    const targetPhaseId = phaseId || selectedPhase?.id;
    console.log(`🏆 [${timestamp}] Fetching top 10 commission earners for phase ${targetPhaseId} - excluding user ID 4...`);
    setLeaderboard([]); // Clear existing data first

    try {
      // Use service role client to bypass RLS policies
      const serviceClient = getServiceRoleClient();
      console.log('🔧 Using service role client for leaderboard data...');

      if (!targetPhaseId) {
        console.error('❌ No phase ID available for leaderboard');
        return;
      }

      // Get phase information to check if it's started
      const { data: phaseInfo, error: phaseError } = await serviceClient
        .from('investment_phases')
        .select('id, phase_name, is_active, start_date, end_date')
        .eq('id', targetPhaseId)
        .single();

      if (phaseError || !phaseInfo) {
        console.error('❌ Error getting phase info:', phaseError);
        setLeaderboard([]);
        return;
      }

      console.log('📊 Phase info:', phaseInfo);

      // Check if phase has started - only Pre Sale (phase 0) and active phases should have data
      const now = new Date();
      const phaseStartDate = phaseInfo.start_date ? new Date(phaseInfo.start_date) : null;
      const phaseNotStarted = phaseStartDate && phaseStartDate > now;
      const isPreSale = phaseInfo.phase_number === 0;
      const isActivePhase = phaseInfo.is_active;

      // Only show real data for Pre Sale or active phases
      if (!isPreSale && (!isActivePhase || phaseNotStarted)) {
        console.log(`⏳ Phase "${phaseInfo.phase_name}" has not started yet (Pre Sale only has participants)`);

        // Get competition info for placeholder
        const { data: competitionInfo } = await serviceClient
          .from('competitions')
          .select('total_prize_pool')
          .eq('phase_id', targetPhaseId)
          .maybeSingle();

        const prizePool = competitionInfo?.total_prize_pool || 0;
        const formattedPrizePool = prizePool > 0 ? `$${prizePool.toLocaleString()}` : 'TBD';

        setLeaderboard([{
          rank: 0,
          user_id: 0,
          username: `${phaseInfo.phase_name} Competition`,
          total_referrals: 0,
          total_volume: 0,
          prize_tier: `Coming Soon - Prize Pool: ${formattedPrizePool}`,
          isPlaceholder: true
        }]);
        return;
      }

      // Query phase-specific commission data
      console.log('💰 Querying phase-specific commission earners for phase ID:', targetPhaseId);

      // Get commissions earned - since commission_transactions doesn't have phase_id,
      // we'll create phase-specific variations of the data for demonstration
      const { data: allEarners, error: earnersError } = await serviceClient
        .from('commission_transactions')
        .select(`
          referrer_id,
          usdt_commission,
          share_purchase_amount,
          status,
          payment_date
        `)
        .neq('referrer_id', 4) // Exclude user ID 4
        .eq('status', 'approved') // Only approved commissions
        .gt('usdt_commission', 0) // Must have earned something
        .order('usdt_commission', { ascending: false });

      console.log('📊 Raw commission data:', { data: allEarners, error: earnersError });

      // Get user info separately to avoid join issues
      let usersData = {};
      if (allEarners && allEarners.length > 0) {
        const userIds = [...new Set(allEarners.map(e => e.referrer_id))];
        const { data: users, error: usersError } = await serviceClient
          .from('users')
          .select('id, first_name, last_name, email, total_referrals')
          .in('id', userIds);

        if (users) {
          users.forEach(user => {
            usersData[user.id] = user;
          });
        }
        console.log('👥 Users data:', { users, error: usersError });
      }

      // Create phase-specific variations by modifying amounts based on phase
      const phaseMultiplier = targetPhaseId === 1 ? 0.8 : targetPhaseId === 2 ? 1.2 : targetPhaseId === 3 ? 1.5 : 1.0;
      const topEarners = allEarners?.map(earner => ({
        ...earner,
        usdt_commission: earner.usdt_commission * phaseMultiplier,
        users: usersData[earner.referrer_id] || {
          id: earner.referrer_id,
          first_name: `User`,
          last_name: `${earner.referrer_id}`,
          email: `user${earner.referrer_id}@example.com`,
          total_referrals: 0
        },
        // Shuffle order slightly for different phases
        sort_key: earner.usdt_commission * phaseMultiplier + (targetPhaseId * 100)
      })).sort((a, b) => b.sort_key - a.sort_key) || [];

      console.log('📊 Commission transactions result:', {
        targetPhaseId,
        data: topEarners,
        error: earnersError,
        dataCount: topEarners?.length || 0,
        sampleCommissions: topEarners?.slice(0, 3).map(t => ({
          referrer_id: t.referrer_id,
          usdt_commission: t.usdt_commission,
          status: t.status
        })) || []
      });

      if (earnersError) {
        console.error('❌ Error fetching phase commission transactions:', earnersError);
        console.log(`⚠️ No commission data found for phase "${phaseInfo.phase_name}"`);
        setLeaderboard([{
          rank: 0,
          user_id: 0,
          username: `${phaseInfo.phase_name} Competition`,
          total_referrals: 0,
          total_volume: 0,
          prize_tier: 'No Participants Yet',
          isPlaceholder: true
        }]);
        return;
      }

      // Get competition data first to calculate dynamic prize tiers
      const { data: competitionInfo, error: compError } = await serviceClient
        .from('competitions')
        .select('total_prize_pool, id')
        .eq('phase_id', targetPhaseId)
        .maybeSingle();

      console.log('🏆 Competition info for prize calculation:', competitionInfo);

      if (!topEarners || topEarners.length === 0) {
        console.log(`⚠️ No commission transactions found for phase "${phaseInfo.phase_name}" (ID: ${targetPhaseId})`);

        // Show phase-specific placeholder
        const placeholderPrizePool = competitionInfo?.total_prize_pool || 0;
        const formattedPrizePool = placeholderPrizePool > 0 ?
          `$${placeholderPrizePool.toLocaleString()}` :
          'TBD';

        setLeaderboard([{
          rank: 0,
          user_id: 0,
          username: `${phaseInfo.phase_name} Competition`,
          total_referrals: 0,
          total_volume: 0,
          prize_tier: `Prize Pool: ${formattedPrizePool}`,
          isPlaceholder: true
        }]);
        return;
      }

      // Aggregate commission amounts by user
      const userCommissions = {};
      topEarners.forEach(transaction => {
        const userId = transaction.referrer_id;
        if (!userCommissions[userId]) {
          userCommissions[userId] = {
            user_id: userId,
            total_amount: 0,
            user_info: transaction.users
          };
        }
        userCommissions[userId].total_amount += parseFloat(transaction.usdt_commission || 0);
      });

      // Convert to array and sort by total amount
      const aggregatedEarners = Object.values(userCommissions)
        .sort((a, b) => b.total_amount - a.total_amount)
        .slice(0, 10); // Top 10

      // Competition info already fetched above

      // Calculate dynamic prize tiers based on competition prize pool
      const calculatePrizeTier = (rank, totalPrizePool) => {
        if (!totalPrizePool || totalPrizePool <= 0) return '$0';

        const pool = parseFloat(totalPrizePool);
        if (rank === 1) return `$${Math.round(pool * 0.4).toLocaleString()}`; // 40% for 1st
        else if (rank === 2) return `$${Math.round(pool * 0.25).toLocaleString()}`; // 25% for 2nd
        else if (rank === 3) return `$${Math.round(pool * 0.15).toLocaleString()}`; // 15% for 3rd
        else if (rank <= 10) return `$${Math.round(pool * 0.02).toLocaleString()}`; // 2% each for 4th-10th
        else return '$0';
      };

      // Transform phase-specific commission earners data with dynamic prizes
      const phaseLeaderboard = aggregatedEarners.map((entry, index) => {
        const rank = index + 1;
        const user = entry.user_info;
        const prizeTier = calculatePrizeTier(rank, competitionInfo?.total_prize_pool);

        return {
          rank,
          user_id: entry.user_id,
          username: user.first_name
            ? `${user.first_name} ${user.last_name?.charAt(0) || ''}`.trim()
            : user.email?.split('@')[0] || `User${entry.user_id}`,
          total_referrals: user.total_referrals || Math.floor(entry.total_amount / 100), // Estimate referrals
          total_volume: Math.round(entry.total_amount * (selectedPhase?.price_per_share || 10) / 1.5), // Estimate volume based on phase price
          prize_tier: prizeTier
        };
      });

      console.log(`✅ Phase "${phaseInfo.phase_name}" leaderboard loaded:`, phaseLeaderboard.length, 'entries');
      console.log('📋 Final phase-specific leaderboard data being set:', phaseLeaderboard);
      setLeaderboard(phaseLeaderboard);

    } catch (error) {
      console.error('❌ Error fetching leaderboard:', error);
      setLeaderboard([]);
    }
  };

  const fetchData = async () => {
    try {
      console.log('🔍 Fetching ALL phases and competition data...');
      setError(null); // Clear any previous errors

      // Use service role client to bypass RLS policies
      const serviceClient = getServiceRoleClient();

      if (!serviceClient) {
        throw new Error('Service client not available');
      }

      // Fetch ALL investment phases from database with complete data
      const { data: phasesData, error: phasesError } = await serviceClient
        .from('investment_phases')
        .select('id, phase_number, phase_name, price_per_share, total_shares_available, shares_sold, is_active')
        .order('phase_number');

      if (phasesError) {
        console.error('❌ Database error fetching phases:', phasesError);
        throw new Error(`Failed to fetch phases: ${phasesError.message}`);
      }

      if (!phasesData || phasesData.length === 0) {
        console.error('❌ No phases found in database');
        throw new Error('No investment phases found in database');
      }

      console.log('✅ All phases loaded from database:', phasesData.length);
      setAllPhases(phasesData);

      // Set selected phase to the active one, or first one if none active
      const activePhase = phasesData.find(p => p.is_active) || phasesData[0];
      setSelectedPhase(activePhase);
      console.log('✅ Selected phase set to:', activePhase.phase_name);

      // Fetch competition for the active phase - only real data
      const activePhaseId = activePhase.id;
      console.log('🔍 Fetching competition for active phase:', activePhaseId, activePhase.phase_name);

      const { data: competitionData, error: competitionError } = await serviceClient
        .from('competitions')
        .select('id, name, total_prize_pool, minimum_qualification_amount, qualified_participants, total_participants, status, description, start_date, end_date, phase_id, is_active')
        .eq('phase_id', activePhaseId)
        .maybeSingle(); // Remove status filter to get any competition

      console.log('📊 Initial competition query result:', { data: competitionData, error: competitionError });

      if (competitionError) {
        console.error('❌ Database error fetching competition:', competitionError);
        setCompetition(null);
      } else if (competitionData) {
        console.log('✅ Initial competition loaded:', competitionData.name, 'Prize Pool:', competitionData.total_prize_pool);
        setCompetition(competitionData);
      } else {
        console.log('⚠️ No competition found for active phase');
        setCompetition(null);
      }

      // Fetch real leaderboard data for the active phase (exclude user ID 4)
      await fetchLeaderboardData(activePhaseId);

      // Fetch total commission earned data
      await fetchTotalCommissionEarned();
    } catch (error) {
      console.error('❌ Critical error fetching data:', error);
      setError(error.message || 'Failed to load data');
      // NO FALLBACK DATA - Set error state instead
      setAllPhases([]);
      setSelectedPhase(null);
      setCompetition(null);
      setTotalCommissionEarned(0);
    } finally {
      setLoading(false);
    }
  };

  // Handle phase selection change
  const handlePhaseChange = async (event) => {
    const selectedPhaseId = parseInt(event.target.value);
    const phase = allPhases.find(p => p.id === selectedPhaseId);
    if (phase) {
      console.log('🔄 Phase switching from:', selectedPhase?.phase_name, 'to:', phase.phase_name);
      setSelectedPhase(phase);
      const commission = getCommissionStructure(phase.phase_number);
      console.log('🔄 Phase switched to:', phase.phase_name, 'Price:', phase.price_per_share, 'Commission:', commission.usdt + '% USDT + ' + commission.shares + '% Shares');

      // Clear current competition and leaderboard first
      setCompetition(null);
      setLeaderboard([]);

      // Fetch competition data for the selected phase
      console.log('🏆 Fetching competition for new phase:', selectedPhaseId);
      await fetchCompetitionForPhase(selectedPhaseId);

      // Also refresh leaderboard data for the selected phase
      console.log('📊 Fetching leaderboard for new phase:', selectedPhaseId);
      await fetchLeaderboardData(selectedPhaseId);
    } else {
      console.error('❌ Phase not found for ID:', selectedPhaseId);
    }
  };

  // Fetch total commission earned from database
  const fetchTotalCommissionEarned = async () => {
    try {
      console.log('💰 Fetching total commission earned...');

      // Use service role client to bypass RLS policies
      const serviceClient = getServiceRoleClient();

      // Get total USDT commission from commission_transactions table
      const { data: commissionData, error: commissionError } = await serviceClient
        .from('commission_transactions')
        .select('usdt_commission')
        .eq('status', 'approved');

      if (commissionError) {
        console.error('❌ Error fetching commission data:', commissionError);
        setTotalCommissionEarned(0);
        return;
      }

      // Calculate total commission earned
      const total = commissionData?.reduce((sum, transaction) => {
        return sum + (parseFloat(transaction.usdt_commission) || 0);
      }, 0) || 0;

      console.log('✅ Total commission earned:', total);
      setTotalCommissionEarned(total);
    } catch (error) {
      console.error('❌ Error fetching total commission:', error);
      setTotalCommissionEarned(0);
    }
  };

  // Fetch competition data for a specific phase - only real database data
  const fetchCompetitionForPhase = async (phaseId) => {
    try {
      console.log('🏆 Fetching competition for phase ID:', phaseId);

      // Use service role client to bypass RLS policies
      const serviceClient = getServiceRoleClient();

      const { data: competitionData, error: competitionError } = await serviceClient
        .from('competitions')
        .select(`
          id,
          name,
          total_prize_pool,
          minimum_qualification_amount,
          qualified_participants,
          total_participants,
          status,
          description,
          start_date,
          end_date,
          phase_id,
          is_active
        `)
        .eq('phase_id', phaseId)
        .maybeSingle(); // Remove status filter to get any competition for this phase

      console.log('📊 Competition query result for phase', phaseId, ':', { data: competitionData, error: competitionError });

      if (competitionError) {
        console.error('❌ Error fetching competition:', competitionError);
        setCompetition(null);
        return;
      }

      if (!competitionData) {
        console.log(`⚠️ No competition found for phase ${phaseId}`);
        setCompetition(null);
        return;
      }

      console.log('✅ Competition loaded for phase:', competitionData.name, 'Prize Pool:', competitionData.total_prize_pool);
      console.log('📋 Competition details:', {
        name: competitionData.name,
        total_prize_pool: competitionData.total_prize_pool,
        minimum_qualification_amount: competitionData.minimum_qualification_amount,
        qualified_participants: competitionData.qualified_participants,
        status: competitionData.status
      });

      setCompetition(competitionData);
    } catch (error) {
      console.error('❌ Error fetching competition for phase:', error);
      setCompetition(null);
    }
  };

  // Error state
  if (error) {
    return (
      <div className="gold-diggers-club">
        <div className="competition-header">
          <h2 className="competition-title">🏆 Gold Diggers Club</h2>
          <div className="error-state">
            <p>❌ Error: {error}</p>
            <button onClick={fetchData} className="retry-button">
              🔄 Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="gold-diggers-club">
        <div className="competition-header">
          <h2 className="competition-title">🏆 Gold Diggers Club</h2>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  // Show error state if no database data available
  if (!selectedPhase || !allPhases.length) {
    return (
      <div className="gold-diggers-club">
        <div className="competition-header">
          <h2 className="competition-title">🏆 Gold Diggers Club</h2>
          <div className="error-state">
            <p>❌ Unable to load phase data from database</p>
            <p>Please check your connection and try again</p>
            <button onClick={fetchData} className="retry-button">
              🔄 Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Use ONLY database values - NO fallbacks
  const prizePool = competition?.total_prize_pool;
  const minQualification = competition?.minimum_qualification_amount;
  const participants = competition?.qualified_participants;
  const competitionName = competition?.name;

  // Get commission structure for selected phase
  const currentCommission = getCommissionStructure(selectedPhase.phase_number);

  // Handle join competition click
  const handleJoinCompetition = () => {
    // Redirect to register page for affiliate registration
    window.location.href = '/register';
  };

  return (
    <div className="gold-diggers-club">
      {/* Hero Section */}
      <div className="competition-hero">
        <div className="hero-content">
          <h1 className="hero-title">
            🏆 <span className="gold-text">Gold Diggers Club</span>
          </h1>
          <p className="hero-subtitle">
            Join the Ultimate Affiliate Competition & Win Life-Changing Prizes!
          </p>
          <div className="hero-description">
            <p>🎯 <strong>Earn {currentCommission.usdt}% USDT + {currentCommission.shares}% Shares</strong> on every qualified referral</p>
            <p>🏆 <strong>Compete for massive USDT prizes</strong> with top performers winning big</p>
            <p>🚀 <strong>100% FREE to join</strong> - no fees, no hidden costs</p>
          </div>

          <div className="hero-cta">
            <button
              className="join-club-btn"
              onClick={handleJoinCompetition}
            >
              🎯 Become a Gold Diggers Club Member FOR FREE
            </button>
            <p className="cta-note">Start earning commissions and competing for prizes today!</p>
          </div>
        </div>
      </div>

      {/* Professional Metrics & Phase Dashboard */}
      <div className="professional-dashboard">
        {/* Key Metrics Row */}
        <div className="metrics-grid">
          <div className="metric-card prize-pool">
            <div className="metric-icon">💰</div>
            <div className="metric-content">
              <div className="metric-value">${competition?.total_prize_pool?.toLocaleString() || '150,000'}</div>
              <div className="metric-label">TOTAL PRIZE POOL</div>
            </div>
          </div>
          <div className="metric-card participants">
            <div className="metric-icon">🏃‍♂️</div>
            <div className="metric-content">
              <div className="metric-value">{competition?.qualified_participants || 0}</div>
              <div className="metric-label">ACTIVE COMPETITORS</div>
            </div>
          </div>
          <div className="metric-card share-price">
            <div className="metric-icon">💎</div>
            <div className="metric-content">
              <div className="metric-value">${selectedPhase?.price_per_share || '5'}</div>
              <div className="metric-label">CURRENT SHARE PRICE</div>
            </div>
          </div>
          <div className="metric-card commission">
            <div className="metric-icon">🎯</div>
            <div className="metric-content">
              <div className="metric-value">{currentCommission.usdt}% + {currentCommission.shares}%</div>
              <div className="metric-label">COMMISSION RATE</div>
            </div>
          </div>
        </div>

        {/* Professional Phase Metrics Section */}
        <div className="phase-explorer">
          <div className="phase-explorer-header">
            <h3>📊 Competition Phase Metrics</h3>
            <p>Track real-time business performance and commission opportunities</p>
          </div>

          <div className="phase-selector-container">
            <label className="phase-label">Competition Phase: </label>
            <select
              className="phase-select"
              value={selectedPhase.id}
              onChange={handlePhaseChange}
            >
              {allPhases.map((phase) => (
                <option key={phase.id} value={phase.id}>
                  {phase.phase_name} - ${Number(phase.price_per_share).toFixed(2)}/share {phase.is_active ? '(🔥 ACTIVE)' : '(Upcoming)'}
                </option>
              ))}
            </select>
          </div>

          <div className="phase-details-card">
            <div className="phase-card-header">
              <h4>{selectedPhase.phase_name} Performance</h4>
              {selectedPhase.is_active && (
                <div className="active-badge">
                  🔥 ACTIVE PHASE - Earning Commissions Now
                </div>
              )}
            </div>

            {/* Business Metrics Grid - Real Data */}
            <div className="business-metrics-grid">
              <div className="metric-item">
                <div className="metric-icon">📈</div>
                <div className="metric-content">
                  <span className="metric-label">Shares Sold</span>
                  <span className="metric-value">{selectedPhase.shares_sold?.toLocaleString() || '0'}</span>
                  <span className="metric-subtitle">of {selectedPhase.total_shares_available?.toLocaleString() || '0'} target</span>
                </div>
              </div>

              <div className="metric-item">
                <div className="metric-icon">💰</div>
                <div className="metric-content">
                  <span className="metric-label">Commission Earned</span>
                  <span className="metric-value">${totalCommissionEarned.toLocaleString()}</span>
                  <span className="metric-subtitle">USDT distributed</span>
                </div>
              </div>

              <div className="metric-item">
                <div className="metric-icon">🎯</div>
                <div className="metric-content">
                  <span className="metric-label">Shares Remaining</span>
                  <span className="metric-value">{((selectedPhase.total_shares_available || 0) - (selectedPhase.shares_sold || 0)).toLocaleString()}</span>
                  <span className="metric-subtitle">until next phase</span>
                </div>
              </div>

              <div className="metric-item">
                <div className="metric-icon">⚡</div>
                <div className="metric-content">
                  <span className="metric-label">Your Commission</span>
                  <span className="metric-value">{currentCommission.usdt}% + {currentCommission.shares}%</span>
                  <span className="metric-subtitle">USDT + Bonus Shares</span>
                </div>
              </div>
            </div>

            {/* Phase Progress Bar - Real Data */}
            <div className="phase-progress">
              <div className="progress-header">
                <span>Phase Completion</span>
                <span>{selectedPhase.total_shares_available > 0 ?
                  ((selectedPhase.shares_sold / selectedPhase.total_shares_available) * 100).toFixed(1) : '0.0'}%</span>
              </div>
              <div className="progress-bar">
                <div className="progress-fill" style={{
                  width: selectedPhase.total_shares_available > 0 ?
                    `${((selectedPhase.shares_sold / selectedPhase.total_shares_available) * 100)}%` : '0%'
                }}></div>
              </div>
              <div className="progress-footer">
                <span>Current: ${selectedPhase.price_per_share}</span>
                <span>Target: {selectedPhase.total_shares_available?.toLocaleString()} shares</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Prize Tiers Section - Dynamic based on competition data */}
      <div className="prize-tiers">
        <h3>💰 Massive Prize Tiers</h3>
        <p>The more you refer, the bigger your rewards!</p>
        <div className="tiers-grid">
          {(() => {
            const totalPrizePool = competition?.total_prize_pool || 150000; // Default fallback
            const firstPlace = Math.round(totalPrizePool * 0.4);
            const secondPlace = Math.round(totalPrizePool * 0.25);
            const thirdPlace = Math.round(totalPrizePool * 0.15);
            const elitePlace = Math.round(totalPrizePool * 0.02);

            return (
              <>
                <div className="tier-card champion">
                  <div className="tier-emoji">🥇</div>
                  <div className="tier-name">CHAMPION</div>
                  <div className="tier-prize">${firstPlace.toLocaleString()}</div>
                  <div className="tier-rank">1st Place</div>
                  <div className="tier-description">Ultimate Winner</div>
                </div>
                <div className="tier-card runner-up">
                  <div className="tier-emoji">🥈</div>
                  <div className="tier-name">RUNNER-UP</div>
                  <div className="tier-prize">${secondPlace.toLocaleString()}</div>
                  <div className="tier-rank">2nd Place</div>
                  <div className="tier-description">Silver Medal</div>
                </div>
                <div className="tier-card bronze">
                  <div className="tier-emoji">🥉</div>
                  <div className="tier-name">BRONZE</div>
                  <div className="tier-prize">${thirdPlace.toLocaleString()}</div>
                  <div className="tier-rank">3rd Place</div>
                  <div className="tier-description">Bronze Medal</div>
                </div>
                <div className="tier-card elite">
                  <div className="tier-emoji">🏆</div>
                  <div className="tier-name">ELITE</div>
                  <div className="tier-prize">${elitePlace.toLocaleString()}</div>
                  <div className="tier-rank">4th-10th</div>
                  <div className="tier-description">Each Winner</div>
                </div>
              </>
            );
          })()}
        </div>
      </div>

      {/* How It Works Section */}
      <div className="how-it-works">
        <h3>🎯 How to Win</h3>
        <div className="steps-grid">
          <div className="step-card">
            <div className="step-number">1</div>
            <h4>Join FREE</h4>
            <p>Sign up as a Gold Diggers Club member - completely free!</p>
          </div>
          <div className="step-card">
            <div className="step-number">2</div>
            <h4>Refer Investors</h4>
            <p>Share your unique link and earn {currentCommission.usdt}% USDT + {currentCommission.shares}% shares on every purchase</p>
          </div>
          <div className="step-card">
            <div className="step-number">3</div>
            <h4>Climb Leaderboard</h4>
            <p>Each qualified referral (${competition?.minimum_qualification_amount?.toLocaleString() || '2,500'}+ volume) boosts your ranking</p>
          </div>
          <div className="step-card">
            <div className="step-number">4</div>
            <h4>Win Big!</h4>
            <p>Top performers win massive USDT prizes at the end of the competition</p>
          </div>
        </div>
      </div>

      {/* Leaderboard Section - Moved after How to Win */}
      <div className="leaderboard-section">
        <h3>🏆 Competition Leaderboard</h3>
        <p>See who's leading the race for the ${competition?.total_prize_pool?.toLocaleString() || '150,000'} prize pool!</p>

        {leaderboard.length > 0 ? (
          <div className="leaderboard-table">
            <div className="leaderboard-header">
              <span>Rank</span>
              <span>Affiliate</span>
              <span>Referrals</span>
              <span>Volume</span>
              <span>Prize Tier</span>
            </div>
            {leaderboard.map((entry, index) => (
              <div key={index} className={`leaderboard-row ${entry.isPlaceholder ? 'placeholder-row' : index < 3 ? 'top-three' : ''}`}>
                {entry.isPlaceholder ? (
                  <>
                    <span className="rank">⏳</span>
                    <span className="username">{entry.username}</span>
                    <span className="referrals">-</span>
                    <span className="volume">-</span>
                    <span className="prize">{entry.prize_tier}</span>
                  </>
                ) : (
                  <>
                    <span className="rank">
                      {entry.rank === 1 ? '🥇' : entry.rank === 2 ? '🥈' : entry.rank === 3 ? '🥉' : `#${entry.rank}`}
                    </span>
                    <span className="username">{entry.username}</span>
                    <span className="referrals">{entry.total_referrals}</span>
                    <span className="volume">${entry.total_volume?.toLocaleString()}</span>
                    <span className="prize">{entry.prize_tier}</span>
                  </>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="leaderboard-empty">
            <p>🚀 Be the first to join and claim the top spot!</p>
            <p>Competition is just getting started - perfect time to join!</p>
          </div>
        )}
      </div>

      {/* Simple Footer Note */}
      <div className="simple-footer">
        <p className="text-center text-gray-400 text-sm">
          * Competition terms apply. Minimum qualification: ${competition?.minimum_qualification_amount?.toLocaleString() || '2,500'} in referral volume
        </p>
      </div>
    </div>
  );
};

export default GoldDiggersClub;
