﻿import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';

const GoldDiggersClub = () => {
  const [allPhases, setAllPhases] = useState([]);
  const [selectedPhase, setSelectedPhase] = useState(null);
  const [competition, setCompetition] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      console.log('🔍 Fetching ALL phases and competition data...');

      // Fetch ALL investment phases from database - NO LIMITS, NO FALLBACKS
      const { data: phasesData, error: phasesError } = await supabase
        .from('investment_phases')
        .select('id, phase_number, phase_name, price_per_share, is_active, commission_usdt, commission_shares')
        .order('phase_number');

      if (phasesError) {
        console.error('❌ Database error fetching phases:', phasesError);
        throw new Error(`Failed to fetch phases: ${phasesError.message}`);
      }

      if (!phasesData || phasesData.length === 0) {
        console.error('❌ No phases found in database');
        throw new Error('No investment phases found in database');
      }

      console.log('✅ All phases loaded from database:', phasesData.length);
      setAllPhases(phasesData);

      // Set selected phase to the active one, or first one if none active
      const activePhase = phasesData.find(p => p.is_active) || phasesData[0];
      setSelectedPhase(activePhase);
      console.log('✅ Selected phase set to:', activePhase.phase_name);

      // Fetch active competition
      const { data: competitionData, error: competitionError } = await supabase
        .from('competitions')
        .select('name, total_prize_pool, minimum_qualification_amount, qualified_participants, status')
        .eq('status', 'active')
        .maybeSingle();

      if (competitionError) {
        console.error('❌ Database error fetching competition:', competitionError);
        // Don't set fallback data - let it be null and handle in UI
        setCompetition(null);
      } else {
        console.log('✅ Competition loaded:', competitionData?.name || 'None');
        setCompetition(competitionData);
      }
    } catch (error) {
      console.error('❌ Critical error fetching data:', error);
      // NO FALLBACK DATA - Set error state instead
      setAllPhases([]);
      setSelectedPhase(null);
      setCompetition(null);
    } finally {
      setLoading(false);
    }
  };

  // Handle phase selection change
  const handlePhaseChange = (event) => {
    const selectedPhaseId = parseInt(event.target.value);
    const phase = allPhases.find(p => p.id === selectedPhaseId);
    if (phase) {
      setSelectedPhase(phase);
      console.log('🔄 Phase switched to:', phase.phase_name, 'Price:', phase.price_per_share, 'Commission:', phase.commission_usdt + '%');
    } else {
      console.error('❌ Phase not found for ID:', selectedPhaseId);
    }
  };

  if (loading) {
    return (
      <div className="gold-diggers-club">
        <div className="competition-header">
          <h2 className="competition-title">🏆 Gold Diggers Club</h2>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  // Show error state if no database data available
  if (!selectedPhase || !allPhases.length) {
    return (
      <div className="gold-diggers-club">
        <div className="competition-header">
          <h2 className="competition-title">🏆 Gold Diggers Club</h2>
          <div className="error-state">
            <p>❌ Unable to load phase data from database</p>
            <p>Please check your connection and try again</p>
            <button onClick={fetchData} className="retry-button">
              🔄 Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Use ONLY database values - NO fallbacks
  const prizePool = competition?.total_prize_pool;
  const minQualification = competition?.minimum_qualification_amount;
  const participants = competition?.qualified_participants;
  const competitionName = competition?.name;

  return (
    <div className="gold-diggers-club">
      {/* Commission Structure Section */}
      <div className="commission-structure">
        <div className="commission-cards">
          {/* Current Phase Card */}
          <div className="commission-card current-phase">
            <div className="phase-badge">
              {selectedPhase.is_active ? 'PRESALE EXCLUSIVE' : `PHASE ${selectedPhase.phase_number}`}
            </div>
            <h3>Current Phase</h3>
            <div className="commission-details">
              <div className="commission-item">
                <span className="label">USDT Commission</span>
                <span className="value">{selectedPhase.commission_usdt}%</span>
              </div>
              <div className="commission-item">
                <span className="label">Bonus NFT Shares</span>
                <span className="value">{selectedPhase.commission_shares}%</span>
              </div>
              {selectedPhase.is_active && (
                <div className="special-offer">
                  🔥 Limited time: Double rewards!
                </div>
              )}
            </div>
          </div>

          {/* Future Phases Card */}
          <div className="commission-card future-phases">
            <div className="phase-badge">PHASES 1-19</div>
            <h3>Future Phases</h3>
            <div className="commission-details">
              <div className="commission-item">
                <span className="label">USDT Commission</span>
                <span className="value">15%</span>
              </div>
              <div className="commission-item">
                <span className="label">Bonus NFT Shares</span>
                <span className="value">0%</span>
              </div>
              <div className="future-note">
                USDT rewards continue
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Competition Section */}
      <div className="competition-header">
        <h2 className="competition-title">🏆 Gold Diggers Club</h2>
        <p className="competition-subtitle">
          Compete for your share of ${prizePool.toLocaleString()} in prizes!
        </p>

        {/* Phase Selector with ALL 20 phases */}
        <div className="phase-selector-simple">
          <label className="phase-label">Phase: </label>
          <select
            className="phase-select"
            value={selectedPhase.id}
            onChange={handlePhaseChange}
          >
            {allPhases.map((phase) => (
              <option key={phase.id} value={phase.id}>
                {phase.phase_name} - ${Number(phase.price_per_share).toFixed(0)} {phase.is_active ? '(Active)' : '(Coming Soon)'}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="competition-overview">
        <div className="competition-card">
          <h3>{competitionName} - {selectedPhase.phase_name}</h3>
          <p>Build your network by referring new investors. Each qualified referral counts toward your ranking.</p>

          <div className="competition-stats">
            {prizePool && (
              <div className="stat-item">
                <span className="stat-label">Total Prize Pool</span>
                <span className="stat-value">${prizePool.toLocaleString()}</span>
              </div>
            )}
            {minQualification && (
              <div className="stat-item">
                <span className="stat-label">Minimum Qualification</span>
                <span className="stat-value">${minQualification.toLocaleString()}</span>
              </div>
            )}
            {participants !== undefined && (
              <div className="stat-item">
                <span className="stat-label">Participants</span>
                <span className="stat-value">{participants}</span>
              </div>
            )}
            <div className="stat-item">
              <span className="stat-label">Share Price</span>
              <span className="stat-value">${selectedPhase.price_per_share}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Commission Rate</span>
              <span className="stat-value">{selectedPhase.commission_usdt}% USDT + {selectedPhase.commission_shares}% Shares</span>
            </div>
          </div>
        </div>
      </div>

      <div className="prize-tiers">
        <h3>Prize Tiers</h3>
        <div className="tiers-grid">
          <div className="tier-card">
            <div className="tier-emoji">🥇</div>
            <div className="tier-name">1st Place</div>
            <div className="tier-prize">$50,000</div>
            <div className="tier-rank">Rank 1</div>
          </div>
          <div className="tier-card">
            <div className="tier-emoji">🥈</div>
            <div className="tier-name">2nd Place</div>
            <div className="tier-prize">$30,000</div>
            <div className="tier-rank">Rank 2</div>
          </div>
          <div className="tier-card">
            <div className="tier-emoji">🥉</div>
            <div className="tier-name">3rd Place</div>
            <div className="tier-prize">$20,000</div>
            <div className="tier-rank">Rank 3</div>
          </div>
          <div className="tier-card">
            <div className="tier-emoji">🏆</div>
            <div className="tier-name">4th-10th Place</div>
            <div className="tier-prize">$5,000</div>
            <div className="tier-rank">Ranks 4-10</div>
          </div>
        </div>
      </div>

      <div className="competition-actions">
        <a
          href="https://t.me/AureusAllianceBot"
          target="_blank"
          rel="noopener noreferrer"
          className="join-competition-btn"
        >
           Join Competition
        </a>
        <p className="competition-note">
          Start referring investors to climb the leaderboard and win prizes!
        </p>
      </div>
    </div>
  );
};

export default GoldDiggersClub;
