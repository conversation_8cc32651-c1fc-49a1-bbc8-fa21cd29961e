import { createClient } from '@supabase/supabase-js'
import { hashPassword, verifyPassword, validatePasswordStrength, isOldHashFormat } from './passwordSecurity'

// Debug environment variables
console.log('🔍 Raw Environment Variables:');
console.log('import.meta.env.VITE_SUPABASE_URL:', import.meta.env.VITE_SUPABASE_URL);
console.log('import.meta.env.VITE_SUPABASE_ANON_KEY exists:', !!import.meta.env.VITE_SUPABASE_ANON_KEY);
console.log('All VITE_ vars:', Object.keys(import.meta.env).filter(k => k.startsWith('VITE_')));

// FORCE the correct URL - environment variables aren't loading properly
const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDkyMTAsImV4cCI6MjA2Njg4NTIxMH0.ZdCtKWveoWqxufQ59OXGf2EXoCBjUhWe8spDvYASySI'

// Validate environment variables
console.log('🔍 Supabase Config Check (FORCED):');
console.log('URL:', supabaseUrl);
console.log('Anon Key exists:', !!supabaseAnonKey);
console.log('Anon Key length:', supabaseAnonKey?.length || 0);

// Test database connection immediately
const testDatabaseConnection = async () => {
  try {
    console.log('🔍 Testing database connection...');
    const testClient = createClient(supabaseUrl, supabaseAnonKey || '');
    const { data, error } = await testClient
      .from('users')
      .select('id')
      .limit(1);

    if (error) {
      console.error('❌ Database connection test failed:', error);
    } else {
      console.log('✅ Database connection test successful, found', data?.length || 0, 'users');
    }
  } catch (err) {
    console.error('❌ Database connection test error:', err);
  }
};

// Run test after a short delay
setTimeout(testDatabaseConnection, 1000);

if (!supabaseAnonKey || supabaseAnonKey.includes('PASTE_YOUR_REAL_ANON_KEY_HERE')) {
  console.error('❌ SUPABASE CONFIGURATION ERROR:');
  console.error('Please set VITE_SUPABASE_ANON_KEY in your .env file');
  console.error('Get your anon key from: https://supabase.com/dashboard → Settings → API');
} else {
  console.log('✅ Supabase configuration looks good');
}

// Create single Supabase client instance to avoid multiple client warnings
let supabase: any;
try {
  if (!supabaseAnonKey) {
    throw new Error('Missing VITE_SUPABASE_ANON_KEY');
  }

  // Create client with proper configuration to avoid multiple instances
  supabase = createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      autoRefreshToken: false, // Disable auto refresh to prevent token errors
      persistSession: false,   // Disable session persistence to avoid corruption
      detectSessionInUrl: false // Disable URL detection to prevent conflicts
    },
    global: {
      headers: {
        'X-Client-Info': 'aureus-web-dashboard'
      }
    }
  });

  console.log('✅ Supabase client created successfully');
} catch (error) {
  console.error('❌ Supabase connection failed:', error);
  // Create a mock client for development
  supabase = {
    auth: {
      signInWithPassword: () => Promise.resolve({ data: { user: null }, error: { message: 'Supabase not configured' } }),
      signOut: () => Promise.resolve({ error: null }),
      getUser: () => Promise.resolve({ data: { user: null }, error: null })
    },
    from: () => ({
      select: () => ({ eq: () => ({ single: () => Promise.resolve({ data: null, error: { message: 'Supabase not configured' } }) }) })
    })
  };
}

// Create singleton service role client to avoid multiple instances
let serviceRoleClient: any = null;

export const getServiceRoleClient = () => {
  if (!serviceRoleClient) {
    // FORCE the correct service role key - environment variables aren't loading properly
    const serviceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';
    console.log('🔧 Service role key available (FORCED):', !!serviceRoleKey);
    console.log('🔧 Service role key length:', serviceRoleKey?.length || 0);
    console.log('🔧 Service role key starts with:', serviceRoleKey?.substring(0, 20) + '...');

    console.log('🔧 Creating service role client with URL (FORCED):', supabaseUrl);
    serviceRoleClient = createClient(supabaseUrl, serviceRoleKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });
    console.log('🔧 Service role client created successfully');

    // Test the service role client
    console.log('🔧 Testing service role client...');
    serviceRoleClient.from('users').select('count', { count: 'exact', head: true })
      .then(({ count, error }) => {
        if (error) {
          console.error('🔧 Service role client test failed:', error);
        } else {
          console.log('🔧 Service role client test successful, user count:', count);
        }
      });
  }
  return serviceRoleClient;
};

export { supabase, createClient }

// Database types
export interface SiteContent {
  id: string
  section: string
  key: string
  value: any
  updated_at: string
  updated_by: string
}

export interface AdminUser {
  id: string
  email: string
  role: 'admin' | 'super_admin'
  created_at: string
}

// Content management functions (with fallback)
export const getSiteContent = async (section?: string) => {
  try {
    let query = supabase.from('site_content').select('*')

    if (section) {
      query = query.eq('section', section)
    }

    const { data, error } = await query.order('section', { ascending: true })

    if (error) {
      console.error('Error fetching site content:', error)
      // Return empty array as fallback
      return []
    }

    return data || []
  } catch (error) {
    console.error('Site content table not available:', error)
    return []
  }
}

export const updateSiteContent = async (section: string, key: string, value: any, userEmail: string) => {
  try {
    const { data, error } = await supabase
      .from('site_content')
      .upsert({
        section,
        key,
        value,
        updated_by: userEmail,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'section,key'
      })
      .select()

    if (error) {
      console.error('Error updating site content:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Site content table not available:', error)
    return null
  }
}

// Verify password hash function - REPLACED with passwordSecurity.ts
// Old function removed - now using verifyPassword from passwordSecurity.ts

// Enhanced login function that works with users table (accepts email or username)
export const signInWithEmailEnhanced = async (emailOrUsername: string, password: string) => {
  try {
    console.log('🔐 Attempting enhanced login for:', emailOrUsername)

    if (!emailOrUsername || emailOrUsername.length === 0) {
      return { user: null, error: { message: 'Email address or username is required' } }
    }

    if (!password || password.length === 0) {
      return { user: null, error: { message: 'Password is required' } }
    }

    // Determine if input is email or username
    const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailOrUsername)
    console.log('🔍 Input type:', isEmail ? 'email' : 'username')

    // Check if user exists in database and verify password - check both tables
    let dbUser = null
    let accountType = 'web'
    let actualEmail = emailOrUsername

    // Use service role client for database queries to bypass RLS
    const serviceClient = getServiceRoleClient()

    // First check users table by email or username
    let webUser = null
    if (isEmail) {
      const { data, error } = await serviceClient
        .from('users')
        .select('*')
        .eq('email', emailOrUsername.toLowerCase().trim())
        .single()
      webUser = data
    } else {
      const { data, error } = await serviceClient
        .from('users')
        .select('*')
        .eq('username', emailOrUsername.toLowerCase().trim())
        .single()
      webUser = data
      if (webUser) {
        actualEmail = webUser.email // Get the actual email for Supabase auth
      }
    }

    if (webUser) {
      dbUser = webUser
      accountType = 'web'
    } else {
      // Check telegram_users table for accounts with temp_email (only if input was email)
      if (isEmail) {
        const { data: telegramUser, error: telegramError } = await serviceClient
          .from('telegram_users')
          .select('*')
          .eq('temp_email', emailOrUsername.toLowerCase().trim())
          .single()

        if (telegramUser) {
          // Map telegram_users fields to match users table structure
          dbUser = {
            id: telegramUser.telegram_id,
            username: telegramUser.username,
            email: telegramUser.email,
            password_hash: telegramUser.password_hash,
            full_name: telegramUser.full_name,
            phone: telegramUser.phone,
            country_of_residence: telegramUser.country_of_residence,
            telegram_id: telegramUser.telegram_id,
            is_active: true, // Assume telegram users are active
            created_at: telegramUser.created_at,
            updated_at: telegramUser.updated_at
          }
          accountType = 'telegram_linked'
          actualEmail = telegramUser.email
        }
      }
    }

    if (!dbUser) {
      console.log('❌ User not found in either table:', emailOrUsername)
      if (isEmail) {
        return { user: null, error: { message: 'No account found with this email address. Please check your email or create a new account.' } }
      } else {
        return { user: null, error: { message: 'No account found with this username. Please check your username or create a new account.' } }
      }
    }

    // Verify password
    const passwordValid = await verifyPassword(password, dbUser.password_hash)
    if (!passwordValid) {
      console.log('❌ Invalid password for user:', emailOrUsername)
      return { user: null, error: { message: 'Incorrect password. Please check your password and try again.' } }
    }

    // Check if user is active (for web users)
    if (accountType === 'web' && !dbUser.is_active) {
      return { user: null, error: { message: 'Account is deactivated. Please contact support.' } }
    }

    // Try to sign in with Supabase auth using the actual email
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: actualEmail,
      password
    })

    if (authError) {
      console.log('⚠️ Supabase auth failed:', authError)
      console.log('✅ Database user exists with valid password, proceeding without Supabase Auth...')

      // Since database password is valid, we can proceed without Supabase Auth
      // This handles cases where users were created via Telegram bot
      console.log('🔧 Bypassing Supabase Auth for database-only user')

      // Check if user profile is complete
      const needsProfileCompletion = !dbUser.email || !dbUser.password_hash || !dbUser.full_name || !dbUser.phone || !dbUser.country_of_residence

      return {
        user: {
          id: dbUser.auth_user_id || `db_${dbUser.id}`, // Use auth_user_id if available, otherwise create a temp ID
          email: dbUser.email,
          database_user: dbUser, // CRITICAL: Include the full database user record
          account_type: accountType,
          user_metadata: {
            full_name: dbUser.full_name,
            username: dbUser.username,
            phone: dbUser.phone,
            country_of_residence: dbUser.country_of_residence,
            telegram_id: dbUser.telegram_id,
            user_id: dbUser.id, // CRITICAL: This is the numeric database ID we need
            is_email_user: accountType === 'web',
            is_telegram_user: accountType === 'telegram',
            needsProfileCompletion
          },
          app_metadata: {
            provider: 'database',
            account_type: accountType
          }
        },
        error: null
      }
    }

    console.log('✅ Enhanced login successful for:', actualEmail)

    // Update the database user with the auth user ID for RLS policies (if not already set)
    if (authData?.user?.id && dbUser.id) {
      console.log('🔗 Ensuring auth user ID is linked to database user...')
      const { error: updateError } = await supabase
        .from('users')
        .update({ auth_user_id: authData.user.id })
        .eq('id', dbUser.id)
        .eq('email', actualEmail) // Extra safety check
        .is('auth_user_id', null) // Only update if not already set

      if (updateError) {
        console.warn('⚠️ Failed to update auth_user_id:', updateError)
      } else {
        console.log('✅ Auth user ID link verified')
      }
    }

    // Check if user profile is complete
    const needsProfileCompletion = !dbUser.email || !dbUser.password_hash || !dbUser.full_name || !dbUser.phone || !dbUser.country_of_residence

    return {
      user: {
        ...authData.user,
        database_user: dbUser,
        account_type: accountType,
        needsProfileCompletion,
        user_metadata: {
          ...authData.user.user_metadata,
          user_id: dbUser.id, // CRITICAL: Include the numeric database ID
          telegram_id: dbUser.telegram_id,
          full_name: dbUser.full_name,
          username: dbUser.username,
          phone: dbUser.phone,
          country_of_residence: dbUser.country_of_residence,
          is_email_user: accountType === 'web',
          is_telegram_user: accountType === 'telegram',
          telegram_connected: !!dbUser.telegram_id,
          profile_completion_required: needsProfileCompletion
        }
      },
      error: null
    }

  } catch (error) {
    console.error('❌ Enhanced login exception:', error)

    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('fetch')) {
        return { user: null, error: { message: 'Network error. Please check your connection and try again.' } }
      } else if (error.message.includes('timeout')) {
        return { user: null, error: { message: 'Request timed out. Please try again.' } }
      } else {
        return { user: null, error: { message: `Login error: ${error.message}` } }
      }
    }

    return { user: null, error: { message: 'An unexpected error occurred. Please try again.' } }
  }
}

// Original authentication function (kept for backward compatibility)
export const signInWithEmail = async (email: string, password: string) => {
  try {
    console.log('🔐 Attempting login for:', email)

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    if (error) {
      console.error('❌ Auth error:', error.message)

      // For development, allow test credentials
      if (email === '<EMAIL>' && password === 'admin123') {
        console.log('🧪 Using test credentials - bypassing Supabase auth')
        const testUser = {
          id: 'test-admin-id',
          email: '<EMAIL>',
          user_metadata: { role: 'super_admin' },
          created_at: new Date().toISOString(),
          aud: 'authenticated',
          role: 'authenticated'
        }

        // Store test user in localStorage for persistence
        localStorage.setItem('aureus_test_user', JSON.stringify(testUser))
        console.log('✅ Test user stored in localStorage')

        return {
          user: testUser,
          error: null
        }
      }

      return { user: null, error }
    }

    console.log('✅ Login successful for:', data.user?.email)
    return { user: data.user, error: null }
  } catch (err) {
    console.error('❌ Login exception:', err)
    return { user: null, error: { message: 'Login failed' } }
  }
}

export const signUpWithEmail = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password
  })

  if (error) {
    console.error('Error signing up:', error)
    return { user: null, error }
  }

  return { user: data.user, error: null }
}

// Direct Telegram ID login - connects to existing database users (no Supabase Auth)
export const signInWithTelegramId = async (telegramId: string) => {
  try {
    console.log('🔐 Attempting Telegram ID login for:', telegramId)

    const telegramIdNum = parseInt(telegramId)
    if (isNaN(telegramIdNum)) {
      return { success: false, error: 'Invalid Telegram ID format' }
    }

    // Use singleton service role client to avoid multiple instances
    const serviceRoleClient = getServiceRoleClient()

    // CRITICAL FIX: First check if user exists in users table with telegram_id (profile completed)
    console.log('🔍 Checking if profile is completed by looking for telegram_id in users table')

    const { data: userWithTelegramId, error: userLookupError } = await serviceRoleClient
      .from('users')
      .select('*')
      .eq('telegram_id', telegramIdNum)
      .maybeSingle()

    if (userLookupError) {
      console.error('❌ Error looking up user by telegram_id:', userLookupError)
      return { success: false, error: 'Database error. Please try again.' }
    }

    if (userWithTelegramId) {
      // Profile is completed - user exists in users table with telegram_id
      console.log('✅ Profile completed - found user in users table:', userWithTelegramId.email)

      const user = userWithTelegramId

      // Check if user profile is complete
      const needsProfileCompletion = !user.email || !user.password_hash || !user.full_name || !user.phone || !user.country_of_residence

      if (needsProfileCompletion) {
        console.log('⚠️ User found but profile incomplete, needs profile completion')
        return {
          success: true,
          user: {
            id: `telegram_${telegramIdNum}`,
            email: user.email,
            database_user: user,
            account_type: 'telegram_direct',
            needsProfileCompletion: true,
            user_metadata: {
              telegram_id: telegramIdNum,
              full_name: user.full_name,
              username: user.username,
              profile_completion_required: true
            }
          }
        }
      }

      console.log('✅ Profile complete - user can login directly')

      // Create authenticated user object for completed profile
      const authenticatedUser = {
        id: `telegram_${telegramIdNum}`,
        email: user.email,
        database_user: user,
        account_type: 'telegram_direct',
        needsProfileCompletion: false,
        user_metadata: {
          telegram_id: telegramIdNum,
          full_name: user.full_name,
          username: user.username,
          profile_completion_required: false
        }
      }

      console.log('💾 Storing completed profile user in localStorage')
      localStorage.setItem('aureus_telegram_user', JSON.stringify(authenticatedUser))

      return { success: true, user: authenticatedUser }
    }

    // Profile not completed - check telegram_users table for initial registration
    console.log('🔍 Profile not completed - checking telegram_users table')

    const { data: telegramUser, error: telegramError } = await serviceRoleClient
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramIdNum)
      .single()

    console.log('🔍 [DEBUG] Telegram user query result:', { telegramUser, telegramError })

    if (telegramError || !telegramUser) {
      console.log('❌ Telegram user not found:', telegramId, telegramError?.message)
      return { success: false, error: 'Telegram ID not found. Please contact support.' }
    }

    // User needs profile completion
    console.log('⚠️ User found in telegram_users but not in users table - needs profile completion')

    const user = {
      id: telegramUser.telegram_id,
      username: telegramUser.username || `telegram_${telegramIdNum}`,
      email: telegramUser.temp_email || `telegram_${telegramIdNum}@temp.local`,
      full_name: `${telegramUser.first_name || ''} ${telegramUser.last_name || ''}`.trim(),
      phone: null,
      country_of_residence: null,
      telegram_id: telegramUser.telegram_id,
      is_active: true,
      created_at: telegramUser.created_at,
      updated_at: telegramUser.updated_at
    }

    if (!user.is_active) {
      return { success: false, error: 'Account is deactivated. Please contact support.' }
    }

    console.log('✅ Found user for Telegram ID (needs profile completion):', user.username)

    // Create authenticated user object that needs profile completion
    const authenticatedUser = {
      id: `telegram_${telegramIdNum}`,
      email: user.email,
      database_user: user,
      account_type: 'telegram_direct',
      needsProfileCompletion: true,
      user_metadata: {
        telegram_id: telegramIdNum,
        full_name: user.full_name,
        username: user.username,
        profile_completion_required: true
      }
    }

    console.log('🔍 [DEBUG] Created authenticatedUser object (needs profile completion):', authenticatedUser)

    // Store user in localStorage so getCurrentUser can find it
    console.log('💾 Storing Telegram user in localStorage for session persistence')
    localStorage.setItem('aureus_telegram_user', JSON.stringify(authenticatedUser))

    return { success: true, user: authenticatedUser }

  } catch (error) {
    console.error('❌ Telegram ID login exception:', error)
    return { success: false, error: 'Login failed. Please try again.' }
  }
}

// Telegram login for completed profiles (legacy)
export const signInWithTelegramProfile = async (telegramUser: any) => {
  try {
    // Use the linked user record if available
    const linkedUser = telegramUser.linkedUser
    if (!linkedUser) {
      console.error('❌ No linked user record found for Telegram user')
      return { user: null, error: { message: 'Profile not completed. Please complete your profile first.' } }
    }

    console.log('🔐 Attempting Telegram profile login for:', linkedUser.email)

    const authPassword = `telegram_${telegramUser.telegram_id}_auth`

    // First, try to sign in with the email and a generated password
    let { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: linkedUser.email,
      password: authPassword
    })

    // If auth fails, try to create the missing Supabase auth user
    if (authError && authError.message.includes('Invalid login credentials')) {
      console.log('🔧 Auth user missing, attempting to create...')

      const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
        email: linkedUser.email,
        password: authPassword,
        options: {
          data: {
            full_name: linkedUser.full_name,
            telegram_id: telegramUser.telegram_id,
            telegram_username: telegramUser.username,
            is_telegram_linked: true
          }
        }
      })

      if (signUpError) {
        console.error('❌ Failed to create auth user:', signUpError)
        return { user: null, error: { message: 'Failed to repair Telegram authentication. Please contact support.' } }
      }

      // Try login again after creating auth user
      const { data: retryAuthData, error: retryAuthError } = await supabase.auth.signInWithPassword({
        email: linkedUser.email,
        password: authPassword
      })

      if (retryAuthError) {
        console.error('❌ Retry auth error:', retryAuthError)
        return { user: null, error: { message: 'Telegram authentication failed after repair attempt' } }
      }

      authData = retryAuthData
      console.log('✅ Auth user created and login successful')
    } else if (authError) {
      console.error('❌ Telegram auth error:', authError)
      return { user: null, error: { message: 'Telegram authentication failed' } }
    }

    console.log('✅ Telegram login successful for:', linkedUser.email)

    return {
      user: {
        ...authData.user,
        database_user: linkedUser,
        account_type: 'telegram_linked',
        telegram_user: telegramUser
      },
      error: null
    }

  } catch (error) {
    console.error('❌ Telegram login exception:', error)
    return { user: null, error: { message: 'Telegram login failed. Please try again.' } }
  }
}

// Password validation function - REPLACED with passwordSecurity.ts
// Old function removed - now using validatePasswordStrength from passwordSecurity.ts

// Helper function to get country name from code
const getCountryName = (countryCode: string): string => {
  const countryMap: { [key: string]: string } = {
    'ZAF': 'South Africa',
    'USA': 'United States',
    'GBR': 'United Kingdom',
    'CAN': 'Canada',
    'AUS': 'Australia',
    'DEU': 'Germany',
    'FRA': 'France',
    'NLD': 'Netherlands',
    'CHE': 'Switzerland',
    'OTHER': 'Other'
  }
  return countryMap[countryCode] || 'Unknown'
}

// Email validation function
export const validateEmail = (email: string): { valid: boolean; error?: string } => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

  if (!email || email.trim().length === 0) {
    return { valid: false, error: 'Email is required' }
  }

  if (email.length > 255) {
    return { valid: false, error: 'Email is too long' }
  }

  if (!emailRegex.test(email)) {
    return { valid: false, error: 'Invalid email format' }
  }

  if (email.includes('<script>') || email.includes('<') || email.includes('>')) {
    return { valid: false, error: 'Invalid email format' }
  }

  return { valid: true }
}

// Check if email already exists in users table
export const checkEmailExists = async (email: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('id')
      .eq('email', email.toLowerCase().trim())
      .single()

    if (error && error.code !== 'PGRST116') {
      console.error('Error checking email:', error)
      return false
    }

    return !!data
  } catch (error) {
    console.error('Error checking email:', error)
    return false
  }
}

// Check if username already exists in users table
export const checkUsernameExists = async (username: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('id')
      .ilike('username', username.trim())
      .single()

    if (error && error.code !== 'PGRST116') {
      console.error('Error checking username:', error)
      return false
    }

    return !!data
  } catch (error) {
    console.error('Error checking username:', error)
    return false
  }
}

// Get user by username for sponsor lookup - uses service role for registration
export const getUserByUsername = async (username: string) => {
  try {
    console.log('🔍 Looking up sponsor username:', username)

    // Try service role client first (for registration when user isn't authenticated)
    try {
      const serviceClient = getServiceRoleClient()
      if (serviceClient) {
        console.log('🔧 Using service role client for sponsor lookup')
        const { data, error } = await serviceClient
          .from('users')
          .select('id, username, full_name, email, is_active')
          .ilike('username', username.trim())
          .eq('is_active', true)
          .single()

        if (!error && data) {
          console.log('✅ Sponsor found via service role:', data.username)
          return data
        }

        if (error && error.code !== 'PGRST116') {
          console.log('⚠️ Service role lookup failed:', error.message)
        }
      }
    } catch (serviceError) {
      console.log('⚠️ Service role client not available:', serviceError.message)
    }

    // Fallback: try regular client (might work if RLS allows it)
    console.log('🔧 Falling back to regular client for sponsor lookup')
    const { data, error } = await supabase
      .from('users')
      .select('id, username, full_name, email, is_active')
      .ilike('username', username.trim())
      .eq('is_active', true)
      .single()

    if (error && error.code !== 'PGRST116') {
      console.error('❌ Regular client lookup also failed:', error)
      return null
    }

    if (data) {
      console.log('✅ Sponsor found via regular client:', data.username)
    } else {
      console.log('❌ Sponsor not found:', username)
    }

    return data
  } catch (error) {
    console.error('❌ Error getting user by username:', error)
    return null
  }
}

// Hash password function - REPLACED with passwordSecurity.ts
// Old vulnerable SHA-256 with static salt function removed
// Now using secure bcrypt implementation from passwordSecurity.ts

// Create referral relationship
const createReferralRelationship = async (referrerId: number, referredId: number, sponsorUsername: string, campaignSource?: string) => {
  try {
    const referralCode = `${sponsorUsername}_${referredId}_${Date.now()}`

    // Use service role client to bypass RLS
    const serviceClient = getServiceRoleClient()

    const { data, error } = await serviceClient
      .from('referrals')
      .insert({
        referrer_id: referrerId,
        referred_id: referredId,
        referral_code: referralCode,
        campaign_source: campaignSource,
        commission_rate: 15.00,
        status: 'active',
        created_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating referral relationship:', error)
      return false
    }

    console.log('✅ Referral relationship created:', data)
    return true
  } catch (error) {
    console.error('Error creating referral relationship:', error)
    return false
  }
}

// Simplified registration for progressive form (email, username, password only)
export const registerUserProgressive = async (userData: {
  email: string
  password: string
  confirmPassword: string
  username: string
  sponsorUsername: string
  campaignSource?: string
}) => {
  try {
    console.log('🔐 Starting progressive user registration for:', userData.email)

    // Validate input data
    const emailValidation = validateEmail(userData.email)
    if (!emailValidation.valid) {
      return { user: null, error: { message: emailValidation.error } }
    }

    // Validate passwords match
    if (userData.password !== userData.confirmPassword) {
      return { user: null, error: { message: 'Passwords do not match' } }
    }

    // Validate password strength
    const passwordValidation = validatePasswordStrength(userData.password)
    if (!passwordValidation.valid) {
      return { user: null, error: { message: passwordValidation.errors[0] } }
    }

    // Validate username
    if (!userData.username || userData.username.trim().length < 3) {
      return { user: null, error: { message: 'Username must be at least 3 characters' } }
    }

    if (!/^[a-zA-Z0-9_]+$/.test(userData.username)) {
      return { user: null, error: { message: 'Username can only contain letters, numbers, and underscores' } }
    }

    const finalUsername = userData.username.trim()

    // Use service role client for all database operations
    const serviceClient = getServiceRoleClient()

    // Skip duplicate checking for progressive registration since email was already verified
    // and username validation happens in real-time on the frontend
    console.log('🔍 Progressive registration - skipping duplicate checks (already verified)')
    console.log('📧 Email:', userData.email.toLowerCase().trim())
    console.log('👤 Username:', finalUsername.toLowerCase())

    // Validate sponsor exists
    let sponsor = await getUserByUsername(userData.sponsorUsername)
    if (!sponsor) {
      console.log(`❌ Sponsor ${userData.sponsorUsername} not found, trying TTTFOUNDER`)
      sponsor = await getUserByUsername('TTTFOUNDER')
      if (!sponsor) {
        console.log('❌ TTTFOUNDER also not found, proceeding without sponsor validation')
        // For now, allow registration to proceed even if sponsor lookup fails
        // This prevents registration from being completely blocked by RLS issues
        console.log('⚠️ Proceeding with registration despite sponsor lookup failure')
        sponsor = { id: 1, username: 'TTTFOUNDER' } // Fallback sponsor object
      }
    }

    // Hash password
    const passwordHash = await hashPassword(userData.password)

    // Create Supabase auth user using regular client (not admin)
    // This will handle duplicates gracefully by returning existing user
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: userData.email.toLowerCase().trim(),
      password: userData.password,
      options: {
        data: {
          full_name: '', // Will be updated later
          username: finalUsername,
          is_email_user: true,
          registration_type: 'progressive_web'
        }
      }
    })

    if (authError) {
      console.error('❌ Supabase auth error:', authError)
      // If user already exists, that's okay for progressive registration
      if (authError.message.includes('already registered')) {
        console.log('🔍 User already exists in auth, continuing with registration...')
      } else {
        return { user: null, error: { message: authError.message } }
      }
    }

    if (!authData.user) {
      return { user: null, error: { message: 'Failed to create authentication account' } }
    }

    console.log('✅ Supabase auth user created/found:', authData.user.id)

    // Check if user already exists in database first
    const { data: existingUser } = await serviceClient
      .from('users')
      .select('*')
      .eq('email', userData.email.toLowerCase().trim())
      .single()

    let newUser: any

    if (existingUser) {
      console.log('🔍 User already exists in database, using existing:', existingUser.id)
      newUser = existingUser
    } else {
      // Create user record in database - using service role client to bypass RLS
      const { data: insertedUser, error: dbError } = await serviceClient
        .from('users')
        .insert({
          email: userData.email.toLowerCase().trim(),
          password_hash: passwordHash,
          username: finalUsername,
          full_name: '', // Progressive form doesn't collect this
          phone: '', // Progressive form doesn't collect this
          country_of_residence: 'ZA', // Default to South Africa
          is_admin: false,
          is_active: true,
          auth_user_id: authData.user.id, // Link to Supabase auth user
          sponsor_user_id: sponsor?.id || 1, // Link to sponsor
          registration_source: 'web'
        })
        .select()
        .single()

      if (dbError) {
        console.error('❌ Database error:', dbError)
        // Note: Cannot clean up auth user without admin privileges
        // This is acceptable for progressive registration as auth user can be reused
        console.log('⚠️ Database insert failed, but auth user remains (can be reused)')

        return { user: null, error: { message: 'Failed to create user account' } }
      }

      newUser = insertedUser
      console.log('✅ User record created in database:', newUser.id)
    }

    console.log('✅ User record created in database:', newUser.id)

    // Create commission balance record for new user
    try {
      console.log('💰 Creating commission balance record for new user...')
      const { error: commissionError } = await serviceClient
        .from('commission_balances')
        .insert({
          user_id: newUser.id,
          usdt_balance: 0.00,
          share_balance: 0.00,
          total_earned_usdt: 0.00,
          total_earned_shares: 0.00,
          escrowed_amount: 0.00,
          total_withdrawn: 0.00
        })

      if (commissionError) {
        console.warn('⚠️ Failed to create commission balance record:', commissionError.message)
      } else {
        console.log('✅ Commission balance record created')
      }
    } catch (error) {
      console.warn('⚠️ Commission balance creation failed:', error)
    }

    // Create referral relationship
    const referralSuccess = await createReferralRelationship(sponsor.id, newUser.id, sponsor.username, userData.campaignSource)
    if (!referralSuccess) {
      console.warn('⚠️ Failed to create referral relationship, but user was created successfully')
    }

    // Sign in the user to establish a session
    console.log('🔐 Signing in user to establish session...')
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: userData.email.toLowerCase().trim(),
      password: userData.password
    })

    if (signInError) {
      console.error('❌ Sign-in error after registration:', signInError)
      // Still return success since user was created, just warn about session
      console.warn('⚠️ User created but session not established')
    } else {
      console.log('✅ User signed in successfully, session established')
    }

    // Store user in localStorage for getCurrentUser function
    console.log('💾 Storing user in localStorage...')
    localStorage.setItem('aureus_user', JSON.stringify(newUser))

    return {
      success: true,
      user: newUser,
      error: null
    }

  } catch (error) {
    console.error('❌ Registration error:', error)
    return {
      user: null,
      error: { message: error instanceof Error ? error.message : 'Registration failed' }
    }
  }
}

// Register new user with email/password and sponsor assignment
export const registerUserWithEmail = async (userData: {
  email: string
  password: string
  confirmPassword: string
  fullName: string
  username: string
  phone: string
  countryOfResidence: string
  hasTelegram: boolean
  telegramUsername?: string
  sponsorUsername: string
  campaignSource?: string
}) => {
  try {
    console.log('🔐 Starting user registration for:', userData.email)

    // Validate input data
    const emailValidation = validateEmail(userData.email)
    if (!emailValidation.valid) {
      return { user: null, error: { message: emailValidation.error } }
    }

    const passwordValidation = validatePasswordStrength(userData.password)
    if (!passwordValidation.valid) {
      return { user: null, error: { message: passwordValidation.errors.join(', ') } }
    }

    if (userData.password !== userData.confirmPassword) {
      return { user: null, error: { message: 'Passwords do not match' } }
    }

    if (!userData.fullName || userData.fullName.trim().length < 2) {
      return { user: null, error: { message: 'Full name is required' } }
    }

    if (!userData.username || userData.username.trim().length < 3) {
      return { user: null, error: { message: 'Username must be at least 3 characters' } }
    }

    if (!/^[a-zA-Z0-9_]+$/.test(userData.username)) {
      return { user: null, error: { message: 'Username can only contain letters, numbers, and underscores' } }
    }

    if (!userData.phone || userData.phone.trim().length < 10) {
      return { user: null, error: { message: 'Phone number is required' } }
    }

    if (!userData.countryOfResidence) {
      return { user: null, error: { message: 'Country of residence is required' } }
    }

    // Telegram username validation (only if user has Telegram)
    if (userData.hasTelegram && (!userData.telegramUsername || userData.telegramUsername.trim().length < 3)) {
      return { user: null, error: { message: 'Telegram username is required and must be at least 3 characters' } }
    }

    if (userData.hasTelegram && userData.telegramUsername && !/^[a-zA-Z0-9_]+$/.test(userData.telegramUsername)) {
      return { user: null, error: { message: 'Telegram username can only contain letters, numbers, and underscores' } }
    }

    if (!userData.sponsorUsername || userData.sponsorUsername.trim().length < 2) {
      return { user: null, error: { message: 'Sponsor username is required' } }
    }

    // Check if email already exists
    const emailExists = await checkEmailExists(userData.email)
    if (emailExists) {
      return { user: null, error: { message: 'Email address is already registered' } }
    }

    // Check if provided username already exists
    const usernameExists = await checkUsernameExists(userData.username.trim())
    if (usernameExists) {
      return { user: null, error: { message: 'Username is already taken' } }
    }

    const finalUsername = userData.username.trim()

    // Validate sponsor exists
    let sponsor = await getUserByUsername(userData.sponsorUsername)
    if (!sponsor) {
      console.log(`❌ Sponsor ${userData.sponsorUsername} not found, using TTTFOUNDER`)
      sponsor = await getUserByUsername('TTTFOUNDER')
      if (!sponsor) {
        return { user: null, error: { message: 'Default sponsor not found. Please contact support.' } }
      }
    }

    // Hash password
    const passwordHash = await hashPassword(userData.password)

    // Create Supabase auth user first
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: userData.email,
      password: userData.password,
      options: {
        data: {
          full_name: userData.fullName,
          username: finalUsername,
          is_email_user: true
        }
      }
    })

    if (authError) {
      console.error('❌ Error creating auth user:', authError)
      return { user: null, error: { message: authError.message } }
    }

    // Check if user was automatically created in users table
    let { data: existingDbUser } = await supabase
      .from('users')
      .select('*')
      .eq('email', userData.email.toLowerCase().trim())
      .single()

    let newUser
    if (existingDbUser) {
      // Update existing user with our data
      console.log('✅ User already exists in database, updating...')
      const { data: updatedUser, error: updateError } = await supabase
        .from('users')
        .update({
          username: finalUsername,
          password_hash: passwordHash,
          full_name: userData.fullName.trim(),
          phone: userData.phone.trim(),
          country_of_residence: userData.countryOfResidence,
          is_active: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingDbUser.id)
        .select()
        .single()

      if (updateError) {
        console.error('❌ Error updating user:', updateError)
        return { user: null, error: { message: 'Failed to update user account' } }
      }
      newUser = updatedUser
    } else {
      // Create new user in users table
      const { data: createdUser, error: userError } = await supabase
        .from('users')
        .insert({
          username: finalUsername,
          email: userData.email.toLowerCase().trim(),
          password_hash: passwordHash,
          full_name: userData.fullName.trim(),
          phone: userData.phone.trim(),
          country_of_residence: userData.countryOfResidence,
          is_active: true,
          is_verified: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single()

      if (userError) {
        console.error('❌ Error creating user:', userError)
        return { user: null, error: { message: 'Failed to create user account' } }
      }
      newUser = createdUser
    }

    console.log('✅ User created successfully:', newUser)

    // Create referral relationship
    const referralSuccess = await createReferralRelationship(
      sponsor.id,
      newUser.id,
      sponsor.username,
      userData.campaignSource
    )

    if (!referralSuccess) {
      console.warn('⚠️ Failed to create referral relationship, but user was created')
    }

    // Track referral conversion for analytics
    if (userData.campaignSource && userData.sponsorUsername) {
      try {
        const { trackReferralConversion } = await import('./referralTracking')
        await trackReferralConversion(userData.sponsorUsername, userData.campaignSource)
      } catch (error) {
        console.warn('⚠️ Failed to track referral conversion:', error)
      }
    }

    console.log('✅ Registration completed successfully for:', userData.email)

    return {
      user: {
        ...authData.user,
        database_user: newUser,
        sponsor: sponsor.username
      },
      error: null
    }

  } catch (error) {
    console.error('❌ Registration exception:', error)
    return { user: null, error: { message: 'Registration failed. Please try again.' } }
  }
}

export const signOut = async () => {
  try {
    const { error } = await supabase.auth.signOut()

    // Always clear all user data from localStorage
    localStorage.removeItem('aureus_test_user')
    localStorage.removeItem('aureus_telegram_user')
    localStorage.removeItem('aureus-remember-me') // Clear saved login credentials
    console.log('🧪 Test user, Telegram user, and saved credentials cleared from localStorage')

    if (error) {
      console.error('Error signing out:', error)
      return false
    }

    return true
  } catch (error) {
    console.error('Error signing out:', error)
    // Still clear users even if Supabase signOut fails
    localStorage.removeItem('aureus_test_user')
    localStorage.removeItem('aureus_telegram_user')
    localStorage.removeItem('aureus-remember-me') // Clear saved login credentials
    return false
  }
}

export const getCurrentUser = async () => {
  try {
    // First check for test user in localStorage
    const testUser = localStorage.getItem('aureus_test_user')
    if (testUser) {
      console.log('🧪 Using test user from localStorage')
      return JSON.parse(testUser)
    }

    // Check for email login user in localStorage
    const emailUser = localStorage.getItem('aureus_user')
    if (emailUser) {
      console.log('📧 Using email user from localStorage')
      const parsedEmailUser = JSON.parse(emailUser)

      // Create user object in expected format
      const userObject = {
        id: parsedEmailUser.auth_user_id || `db_${parsedEmailUser.id}`,
        email: parsedEmailUser.email,
        database_user: parsedEmailUser,
        account_type: 'email',
        user_metadata: {
          full_name: parsedEmailUser.full_name,
          username: parsedEmailUser.username,
          phone: parsedEmailUser.phone,
          country_of_residence: parsedEmailUser.country_of_residence,
          telegram_id: parsedEmailUser.telegram_id,
          user_id: parsedEmailUser.id,
          is_email_user: true,
          telegram_connected: !!parsedEmailUser.telegram_id
        }
      }

      console.log('✅ Email user loaded from localStorage:', userObject)
      return userObject
    }

    // Then check for Telegram user in localStorage
    const telegramUser = localStorage.getItem('aureus_telegram_user')
    if (telegramUser) {
      console.log('📱 Using Telegram user from localStorage')
      const parsedTelegramUser = JSON.parse(telegramUser)
      console.log('🔍 [DEBUG] Parsed Telegram user from localStorage:', parsedTelegramUser)
      console.log('🔍 [DEBUG] database_user:', parsedTelegramUser.database_user)
      console.log('🔍 [DEBUG] database_user.telegram_id:', parsedTelegramUser.database_user?.telegram_id)
      return parsedTelegramUser
    }

    // Then try Supabase auth (but don't warn about missing session for Telegram users)
    const { data: { user }, error } = await supabase.auth.getUser()

    if (error) {
      // Only log as info, not warning, since Telegram users don't use Supabase auth
      console.log('ℹ️ No Supabase auth session found (normal for Telegram users):', error.message)
      return null
    }

    if (!user) {
      console.log('ℹ️ No authenticated user found')
      return null
    }

    console.log('✅ Supabase auth user found:', user.email, user.id)

    // Fetch database user information - check both users and telegram_users tables
    try {
      // First check users table using service role client to bypass RLS
      console.log('🔍 Looking up database user for email:', user.email)
      const serviceClient = getServiceRoleClient()
      const { data: dbUsers, error: dbError } = await serviceClient
        .from('users')
        .select('*')
        .eq('email', user.email)
        .limit(1)

      if (dbError && dbError.code !== 'PGRST116') {
        console.log('⚠️ Users table query error:', dbError.code, dbError.message)
      }

      if (!dbError && dbUsers && dbUsers.length > 0) {
        const dbUser = dbUsers[0]
        // Found in users table
        console.log('✅ Database user found in users table:', dbUser.id, dbUser.email)
        return {
          ...user,
          database_user: dbUser,
          account_type: 'web'
        }
      }

      // If not found in users table, check telegram_users table for accounts with temp_email
      console.log('🔍 Looking up telegram user for temp_email:', user.email)
      const { data: telegramUsers, error: telegramError } = await serviceClient
        .from('telegram_users')
        .select('*')
        .eq('temp_email', user.email)
        .limit(1)

      if (telegramError && telegramError.code !== 'PGRST116') {
        console.log('⚠️ Telegram users table query error:', telegramError.code, telegramError.message)
      }

      if (!telegramError && telegramUsers && telegramUsers.length > 0) {
        const telegramUser = telegramUsers[0]
        // Found in telegram_users table with web access
        console.log('✅ Database user found in telegram_users table:', telegramUser.telegram_id, telegramUser.username)
        return {
          ...user,
          database_user: {
            id: telegramUser.telegram_id, // Use telegram_id as the primary key
            username: telegramUser.username,
            email: telegramUser.email,
            full_name: telegramUser.full_name,
            phone: telegramUser.phone,
            country_of_residence: telegramUser.country_of_residence,
            telegram_id: telegramUser.telegram_id,
            is_active: true,
            created_at: telegramUser.created_at,
            updated_at: telegramUser.updated_at
          },
          account_type: 'telegram_linked'
        }
      }

      console.warn('❌ Database user not found in either table for:', user.email)
      return user // Return auth user without database info

    } catch (dbError) {
      console.warn('Error fetching database user:', dbError)
      return user // Return auth user without database info
    }

  } catch (error) {
    console.warn('Error getting current user (expected in test mode):', error)
    return null
  }
}

// Check if user is admin using existing admin_users table
export const isUserAdmin = async (email: string) => {
  try {
    console.log('🔍 Checking admin status for email:', email)

    const { data, error } = await supabase
      .from('admin_users')
      .select('*')
      .eq('email', email)
      .single()

    if (error) {
      console.log('❌ Admin check failed:', error.code, error.message)
      return false
    }

    if (data) {
      console.log('✅ Admin user found:', data.email, data.role)
      return true
    }

    console.log('❌ User is not an admin:', email)
    return false

    return !!data && (data.role === 'admin' || data.role === 'super_admin')
  } catch (error) {
    console.error('Error checking admin_users table:', error)
    return false
  }
}

// Get admin user details
export const getAdminUser = async (email: string) => {
  try {
    const { data, error } = await supabase
      .from('admin_users')
      .select('*')
      .eq('email', email)
      .single()

    if (error) {
      console.log('Admin user fetch error:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Error fetching admin user:', error)
    return null
  }
}

// Telegram bot authentication functions
export const authenticateWithTelegramBot = async () => {
  try {
    console.log('🔐 Generating Telegram bot authentication PIN...')

    // Generate a proper 6-digit PIN for bot authentication
    const token = Math.floor(100000 + Math.random() * 900000).toString()

    // Store the token in the database with expiration
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000) // 10 minutes

    const { error } = await supabase
      .from('auth_tokens')
      .insert({
        token,
        expires_at: expiresAt.toISOString(),
        confirmed: false,
        cancelled: false,
        created_at: new Date().toISOString()
      })

    if (error) {
      console.error('Error storing auth PIN:', error)
      return { success: false, error: 'Failed to generate authentication PIN' }
    }

    console.log(`✅ 6-digit authentication PIN generated successfully: ${token}`)
    return { success: true, token }
  } catch (error) {
    console.error('Exception generating auth PIN:', error)
    return { success: false, error: 'Authentication PIN generation failed' }
  }
}

export const pollForAuthConfirmation = async (token: string) => {
  try {
    console.log('🔄 Polling for authentication confirmation...')

    const maxAttempts = 60 // 5 minutes with 5-second intervals
    let attempts = 0

    return new Promise((resolve) => {
      const pollInterval = setInterval(async () => {
        attempts++

        try {
          // Check if token has been confirmed
          const { data: tokenData, error } = await supabase
            .from('auth_tokens')
            .select('*')
            .eq('token', token)
            .single()

          if (error) {
            console.error('Error checking token:', error)
            clearInterval(pollInterval)
            resolve({ success: false, error: 'Token verification failed' })
            return
          }

          // Check if token expired
          const now = new Date()
          const expiresAt = new Date(tokenData.expires_at)

          if (now > expiresAt) {
            clearInterval(pollInterval)
            resolve({ success: false, error: 'Authentication token expired' })
            return
          }

          // Check if token was cancelled
          if (tokenData.cancelled) {
            clearInterval(pollInterval)
            resolve({ success: false, error: 'Authentication was cancelled' })
            return
          }

          // Check if token was confirmed
          if (tokenData.confirmed) {
            clearInterval(pollInterval)

            // Get user data if available
            let userData = null
            if (tokenData.telegram_id) {
              console.log('🔍 Looking up Telegram user:', tokenData.telegram_id)
              const { data: telegramUser, error: telegramError } = await supabase
                .from('telegram_users')
                .select('*')
                .eq('telegram_id', tokenData.telegram_id)
                .single()

              if (telegramError) {
                console.log('⚠️ Telegram user not found in database:', telegramError.message)
                // User authenticated via bot but not in telegram_users table
                // This is okay - they may be a new user
                userData = null
              } else {
                userData = telegramUser
                console.log('✅ Found Telegram user:', telegramUser)
              }
            }

            // Include token data for additional context
            const authResult = {
              success: true,
              userData: userData || {
                telegram_id: tokenData.telegram_id,
                user_status: tokenData.user_status || 'new_user',
                // Add basic info from token if no user record exists
                ...(tokenData.user_data ? JSON.parse(tokenData.user_data) : {})
              }
            }

            console.log('🔍 Resolving with auth result:', authResult)
            resolve(authResult)
            return
          }

          // Check if max attempts reached
          if (attempts >= maxAttempts) {
            clearInterval(pollInterval)
            resolve({ success: false, error: 'Authentication timeout' })
            return
          }

        } catch (pollError) {
          console.error('Polling error:', pollError)
          clearInterval(pollInterval)
          resolve({ success: false, error: 'Polling failed' })
        }
      }, 5000) // Poll every 5 seconds
    })
  } catch (error) {
    console.error('Exception during polling:', error)
    return { success: false, error: 'Polling setup failed' }
  }
}
