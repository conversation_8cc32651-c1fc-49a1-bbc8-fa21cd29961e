/* Import Unified Design System */
@import './aureus-design-system.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Hot Reload Trigger - Fixed */

/* Custom CSS for Aureus Alliance Holdings */
:root {
  --gold-primary: #fbbf24;
  --gold-secondary: #f59e0b;
  --cyber-blue: #06b6d4;
  --cyber-purple: #8b5cf6;

  /* Single Design System - Light Theme Only */
  --theme-bg: #ffffff;
  --theme-bg-secondary: #f8fafc;
  --theme-bg-tertiary: #f1f5f9;
  --theme-text: #1e293b;
  --theme-text-secondary: #64748b;
  --theme-text-muted: #94a3b8;
  --theme-border: #e2e8f0;
  --theme-border-light: #f1f5f9;
  --theme-card-bg: #ffffff;
  --theme-card-hover: #f8fafc;
  --theme-shadow: rgba(0, 0, 0, 0.1);
  --theme-shadow-lg: rgba(0, 0, 0, 0.15);
  --theme-nav-bg: rgba(255, 255, 255, 0.95);
  --theme-overlay: rgba(0, 0, 0, 0.5);
  --theme-input-bg: #ffffff;
  --theme-input-border: #d1d5db;
  --theme-button-secondary: #f1f5f9;
  --theme-button-secondary-hover: #e2e8f0;
  --theme-success: #10b981;
  --theme-warning: #f59e0b;
  --theme-error: #ef4444;
  --theme-info: #3b82f6;
}

/* Theme toggle removed - using single design system */

/* Enhanced Global Typography Styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding-top: 80px; /* Account for fixed header */
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #ffffff;
  color: #1e293b;
  overflow-x: hidden;
  letter-spacing: 2px;
  font-size: clamp(1rem, 1.5vw, 1.125rem);
}

/* Enhanced Typography for All Elements */
h1, h2, h3, h4, h5, h6 {
  letter-spacing: 2px;
  font-weight: 600;
  line-height: 1.2;
}

p, span, div, a, button, input, textarea, label {
  letter-spacing: 2px;
}

/* Commission Structure Styles */
.commission-structure {
  margin-bottom: 2rem;
}

.commission-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
}

.commission-card {
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  border: 1px solid #333;
  border-radius: 12px;
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
}

.commission-card.current-phase {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: #000;
  border-color: #fbbf24;
}

.commission-card.future-phases {
  background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);
  color: #fff;
  border-color: #3730a3;
}

.phase-badge {
  background: rgba(0, 0, 0, 0.2);
  color: #fff;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 1rem;
  display: inline-block;
}

.commission-card.current-phase .phase-badge {
  background: rgba(0, 0, 0, 0.3);
  color: #000;
}

.commission-details {
  margin-top: 1rem;
}

.commission-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.commission-item .label {
  font-size: 0.9rem;
  opacity: 0.8;
}

.commission-item .value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #fbbf24;
}

.commission-card.current-phase .commission-item .value {
  color: #000;
}

.special-offer {
  background: rgba(255, 0, 0, 0.1);
  border: 1px solid #ff4444;
  border-radius: 8px;
  padding: 0.5rem;
  text-align: center;
  font-size: 0.85rem;
  font-weight: 600;
  color: #ff4444;
  margin-top: 1rem;
}

.future-note {
  text-align: center;
  font-size: 0.85rem;
  opacity: 0.7;
  margin-top: 1rem;
}

/* Error State Styles */
.error-state {
  text-align: center;
  padding: 2rem;
  background: rgba(255, 0, 0, 0.1);
  border: 1px solid #ff4444;
  border-radius: 12px;
  margin: 1rem 0;
}

.error-state p {
  color: #ff6666;
  margin: 0.5rem 0;
}

.retry-button {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: #000;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 1rem;
  transition: all 0.3s ease;
}

.retry-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(251, 191, 36, 0.3);
}

/* Responsive Design for Commission Cards */
@media (max-width: 768px) {
  .commission-cards {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .commission-card {
    padding: 1rem;
  }

  .commission-item .value {
    font-size: 1.1rem;
  }
}

/* Professional Typography Enhancements */
.section-title {
  font-size: clamp(2rem, 6vw, 3rem);
  font-weight: 600;
  letter-spacing: 2px;
  color: #fbbf24;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.section-subtitle {
  font-size: clamp(1.25rem, 2.5vw, 1.5rem);
  letter-spacing: 2px;
  color: #e5e7eb;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.card-title {
  font-size: clamp(1.5rem, 3vw, 1.75rem);
  font-weight: 600;
  letter-spacing: 2px;
  color: #fbbf24;
  margin-bottom: 1rem;
}

.card-description {
  font-size: clamp(1.125rem, 2vw, 1.25rem);
  letter-spacing: 2px;
  color: #e5e7eb;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.feature-text {
  font-size: clamp(1rem, 1.75vw, 1.125rem);
  letter-spacing: 2px;
  color: #e5e7eb;
  line-height: 1.5;
}

.label-text {
  font-size: clamp(1rem, 1.5vw, 1.125rem);
  letter-spacing: 2px;
  color: #9ca3af;
  font-weight: 500;
  text-transform: uppercase;
}

/* Enhanced Mobile Typography Responsiveness */
@media (max-width: 480px) {
  body {
    font-size: clamp(1rem, 3vw, 1.125rem);
    letter-spacing: 1.5px;
  }

  .section-title {
    font-size: clamp(1.75rem, 8vw, 2.5rem);
    letter-spacing: 1.5px;
  }

  .section-subtitle {
    font-size: clamp(1.125rem, 4vw, 1.25rem);
    letter-spacing: 1.5px;
  }

  .card-title {
    font-size: clamp(1.25rem, 5vw, 1.5rem);
    letter-spacing: 1.5px;
  }

  .card-description {
    font-size: clamp(1rem, 3.5vw, 1.125rem);
    letter-spacing: 1.5px;
  }

  .feature-text {
    font-size: clamp(0.875rem, 3vw, 1rem);
    letter-spacing: 1.5px;
  }

  .label-text {
    font-size: clamp(0.875rem, 2.5vw, 1rem);
    letter-spacing: 1.5px;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  body {
    font-size: clamp(1.125rem, 2.5vw, 1.25rem);
    letter-spacing: 2px;
  }

  .section-title {
    font-size: clamp(2rem, 7vw, 2.75rem);
    letter-spacing: 2px;
  }

  .section-subtitle {
    font-size: clamp(1.25rem, 3.5vw, 1.375rem);
    letter-spacing: 2px;
  }
}

@media (min-width: 769px) {
  body {
    font-size: clamp(1.125rem, 2vw, 1.25rem);
    letter-spacing: 2px;
  }
}

/* Universal Typography Enhancement for All Text Elements */
.text-xs, .text-sm, .text-base, .text-lg, .text-xl, .text-2xl, .text-3xl, .text-4xl, .text-5xl, .text-6xl {
  letter-spacing: 2px !important;
}

/* Ensure all small text elements are properly sized */
.text-xs {
  font-size: clamp(1rem, 1.25vw, 1.125rem) !important;
}

.text-sm {
  font-size: clamp(1.125rem, 1.5vw, 1.25rem) !important;
}

.text-base {
  font-size: clamp(1.125rem, 2vw, 1.25rem) !important;
}

.text-lg {
  font-size: clamp(1.25rem, 2.5vw, 1.5rem) !important;
}

.text-xl {
  font-size: clamp(1.5rem, 3vw, 1.75rem) !important;
}

.text-2xl {
    font-size: clamp(1.75rem, 3.5vw, 1rem) !important;
    font-weight: bold !important;
    letter-spacing: 3px !important;
}

.text-3xl {
  font-size: clamp(2rem, 4vw, 2.5rem) !important;
}

.text-4xl {
    font-size: clamp(2.25rem, 5vw, 3rem) !important;
    margin-top: 5px !important;
}

.text-5xl {
  font-size: clamp(2.5rem, 6vw, 3.5rem) !important;
}

.text-6xl {
  font-size: clamp(3rem, 8vw, 4rem) !important;
}

/* Enhanced Typography for Specific Components */
.gold-gradient-text, .text-gradient-gold, .text-gradient-cyber {
  letter-spacing: 2px !important;
}

/* Button Typography Enhancement */
button, .btn, .btn-primary, .btn-secondary {
  letter-spacing: 2px !important;
  font-size: clamp(1rem, 1.5vw, 1.125rem) !important;
}

/* Input and Form Typography */
input, textarea, select, label {
  letter-spacing: 2px !important;
  font-size: clamp(1rem, 1.5vw, 1.125rem) !important;
}

/* Table Typography */
table, th, td {
  letter-spacing: 2px !important;
}

/* Navigation Typography */
nav a, .nav-link {
  letter-spacing: 2px !important;
  font-size: clamp(1.125rem, 2vw, 1.25rem) !important;
}

/* Conflicting overrides removed - now handled by unified design system */

/* Reduce excessive padding */
.py-24 {
  padding-top: 3rem !important;
  padding-bottom: 3rem !important;
}

.py-32 {
  padding-top: 4rem !important;
  padding-bottom: 4rem !important;
}

.py-20 {
  padding-top: 2.5rem !important;
  padding-bottom: 2.5rem !important;
}

.mb-20 {
  margin-bottom: 2rem !important;
}

.mb-16 {
  margin-bottom: 1.5rem !important;
}

/* Ensure full width sections */
section {
  width: 100%;
  max-width: none;
  min-height: 100px; /* Ensure sections have minimum height */
}

/* Phase section specific styling */
#phases {
  padding: var(--spacing-xl) 0;
  background: rgba(15, 23, 42, 0.3);
  min-height: 600px; /* Ensure phase section is visible */
}

/* Grid system fixes */
.grid {
  display: grid;
  width: 100%;
}

/* Gap utilities */
.gap-xl {
  gap: var(--spacing-xl);
}

.gap-lg {
  gap: var(--spacing-lg);
}

/* Spacing utilities */
.space-y-lg > * + * {
  margin-top: var(--spacing-lg);
}

.mt-lg {
  margin-top: var(--spacing-lg);
}

.mt-md {
  margin-top: var(--spacing-md);
}

.grid-cols-1 {
  grid-template-columns: 1fr;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

.col-span-2 {
  grid-column: span 2 / span 2;
}

@media (min-width: 768px) {
  .md\\:grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  .md\\:grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1024px) {
  .lg\\:grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  .lg\\:grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  .lg\\:grid-cols-12 {
    grid-template-columns: repeat(12, 1fr);
  }
  .lg\\:col-span-2 {
    grid-column: span 2 / span 2;
  }
}

/* Gradient text effects */
.text-gradient-gold {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-cyber {
  background: linear-gradient(135deg, #06b6d4, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Text Color Utilities */
.text-gold {
  color: var(--gold-primary) !important;
}

.text-white {
  color: #ffffff !important;
}

.animated-gradient {
  background: linear-gradient(45deg, #fbbf24, #f59e0b, #fbbf24);
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Legacy glass effects - deprecated, use .card instead */

/* Multi-Colored Border Containers */
.aureus-container {
  position: relative;
  background: linear-gradient(135deg, #FFD700, #00D4FF, #8B5CF6, #FF006E, #00FF88);
  border-radius: 24px;
  padding: 3px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  transition: all 0.3s ease;
}

.aureus-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5);
}

.aureus-container-inner {
  background: rgba(17, 17, 24, 0.95);
  border-radius: 21px;
  padding: 32px;
  position: relative;
  z-index: 1;
  height: 100%;
}

.aureus-table-container {
  position: relative;
  background: linear-gradient(90deg, #FFD700, #00D4FF, #8B5CF6, #FF006E);
  border-radius: 16px;
  padding: 2px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.aureus-table-container:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4);
}

.aureus-table-inner {
  background: rgba(17, 17, 24, 0.95);
  border-radius: 14px;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

/* Glow effects */
.glow-gold {
  box-shadow: 0 0 20px rgba(251, 191, 36, 0.3);
}

.glow-cyber {
  box-shadow: 0 0 20px rgba(6, 182, 212, 0.3);
}

/* Button styles */
.btn-primary {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: #ffffff;
  font-weight: 600;
  padding: 12px 24px;
  border-radius: 12px;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(251, 191, 36, 0.4);
}

/* Animations */
.float-animation {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
  from { box-shadow: 0 0 20px rgba(251, 191, 36, 0.3); }
  to { box-shadow: 0 0 30px rgba(251, 191, 36, 0.6); }
}

/* Slide animations */
.slide-in-right {
  animation: slideInRight 1s ease-out;
}

.slide-in-left {
  animation: slideInLeft 1s ease-out;
}

.fade-in-section {
  animation: fadeIn 1s ease-out;
}

@keyframes slideInRight {
  from { transform: translateX(100px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInLeft {
  from { transform: translateX(-100px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Gold text utility */
.gold-text {
  color: #fbbf24;
}

.gold-bg {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
}

/* Admin Dashboard Light Theme Overrides */
.admin-dashboard {
  background: #ffffff;
  color: #1f2937;
}

.admin-dashboard input,
.admin-dashboard textarea,
.admin-dashboard select {
  background-color: #ffffff !important;
  border-color: #d1d5db !important;
  color: #1f2937 !important;
}

.admin-dashboard input::placeholder,
.admin-dashboard textarea::placeholder {
  color: #6b7280 !important;
}

.admin-dashboard label {
  color: #374151 !important;
}

.admin-dashboard .bg-white {
  background-color: #ffffff !important;
}

.admin-dashboard .text-gray-900 {
  color: #1f2937 !important;
}

.admin-dashboard .text-gray-700 {
  color: #374151 !important;
}

.admin-dashboard .text-gray-500 {
  color: #6b7280 !important;
}

.admin-dashboard .border-gray-300 {
  border-color: #d1d5db !important;
}

.admin-dashboard .border-gray-200 {
  border-color: #e5e7eb !important;
}

/* ===== CALCULATOR RESPONSIVE DESIGN ===== */

/* Calculator Container Constraints */
.calculator-container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
  overflow: hidden;
}

.calculator-layout {
  display: flex;
  flex-direction: row;
  gap: 1rem;
  width: 100%;
}

.calculator-inputs {
  flex: 1;
  max-width: 400px;
  min-width: 300px;
}

.calculator-results {
  flex: 1;
  min-width: 300px;
}

/* Calculator Responsive Breakpoints - Mobile First */
@media (min-width: 768px) and (max-width: 1023px) {
  .calculator-layout {
    gap: 0.75rem;
  }

  .calculator-inputs {
    max-width: 350px;
    min-width: 280px;
  }

  .calculator-results {
    min-width: 280px;
  }
}

@media (min-width: 768px) {
  .calculator-layout {
    flex-direction: row;
    gap: 1rem;
  }

  .calculator-inputs {
    max-width: 400px;
    min-width: 300px;
  }

  .calculator-results {
    min-width: 300px;
  }
}

/* Mobile styles (default) */
.calculator-layout {
  flex-direction: column;
  gap: 1.5rem;
}

.calculator-inputs,
.calculator-results {
  max-width: 100%;
  min-width: unset;
}

@media (max-width: 479px) {
  .calculator-container {
    padding: 0 0.5rem;
  }

  .calculator-layout {
    gap: 1rem;
  }
}

/* Calculator Input Field Responsiveness */
.calculator-input-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 0.375rem;
  margin-bottom: 0.5rem;
}

.calculator-input-label {
  font-size: clamp(1.125rem, 2vw, 1.25rem);
  font-weight: 500;
  color: #e5e7eb;
  letter-spacing: 2px;
  flex: 1;
  margin-right: 0.5rem;
}

.calculator-input-field {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-left: 0.5rem;
  min-width: 0;
  flex-shrink: 0;
}

.calculator-input-field input,
.calculator-input-field select {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.25rem;
  color: white;
  padding: 0.375rem 0.5rem;
  font-size: clamp(1.125rem, 2vw, 1.25rem);
  letter-spacing: 2px;
  width: 80px;
  min-width: 60px;
  max-width: 120px;
}

.calculator-input-field input:focus,
.calculator-input-field select:focus {
  outline: none;
  border-color: #fbbf24;
  box-shadow: 0 0 0 1px #fbbf24;
}

.calculator-input-unit {
  font-size: clamp(1rem, 1.5vw, 1.125rem);
  color: #9ca3af;
  letter-spacing: 2px;
  white-space: nowrap;
  margin-left: 0.25rem;
}

@media (max-width: 768px) {
  .calculator-input-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    padding: 0.75rem;
  }

  .calculator-input-label {
    margin-right: 0;
    margin-bottom: 0.25rem;
  }

  .calculator-input-field {
    margin-left: 0;
    width: 100%;
    justify-content: space-between;
  }

  .calculator-input-field input,
  .calculator-input-field select {
    width: 120px;
    max-width: 150px;
  }
}

@media (max-width: 480px) {
  .calculator-input-field input,
  .calculator-input-field select {
    width: 100px;
    max-width: 120px;
    font-size: 0.8rem;
  }

  .calculator-input-label {
    font-size: clamp(1rem, 2vw, 1.125rem);
    letter-spacing: 2px;
  }

  .calculator-input-unit {
    font-size: clamp(0.875rem, 1.5vw, 1rem);
    letter-spacing: 2px;
  }
}

/* Calculator Button Layout Responsiveness */
.calculator-header {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.calculator-header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
}

.calculator-title-section {
  flex: 1;
}

.calculator-reset-button {
  flex-shrink: 0;
}

@media (min-width: 640px) {
  .calculator-header {
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 1rem;
  }

  .calculator-header-content {
    width: 100%;
  }
}

@media (max-width: 639px) {
  .calculator-header-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .calculator-reset-button {
    align-self: flex-end;
  }
}

/* Calculator Results Grid Responsiveness */
.calculator-results-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

@media (min-width: 640px) {
  .calculator-results-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .calculator-results-grid {
    gap: 0.5rem;
  }
}

/* Calculator Mobile Touch Optimization */
@media (max-width: 768px) {
  .calculator-container .btn {
    min-height: 44px;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }

  .calculator-container .card {
    padding: 1rem;
  }

  .calculator-container h3 {
    font-size: 1.125rem;
  }

  .calculator-container p {
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .calculator-container {
    margin: 0 -0.5rem;
  }

  .calculator-container .btn {
    min-height: 48px;
    padding: 0.875rem 1.25rem;
    font-size: 0.9rem;
    border-radius: 0.5rem;
  }

  .calculator-container .card {
    padding: 0.875rem;
    border-radius: 0.75rem;
  }

  .calculator-container h3 {
    font-size: 1rem;
    line-height: 1.4;
  }

  .calculator-container p {
    font-size: 0.8rem;
    line-height: 1.5;
  }

  /* Touch-friendly spacing */
  .calculator-layout {
    gap: 1.25rem;
  }

  .calculator-inputs .space-y-4 > * + * {
    margin-top: 1.25rem;
  }

  .calculator-results .space-y-4 > * + * {
    margin-top: 1.25rem;
  }
}

/* Calculator Projection Table Responsiveness */
.calculator-projection-table {
  overflow-x: auto;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 0.375rem;
  -webkit-overflow-scrolling: touch;
}

.calculator-projection-table table {
  width: 100%;
  font-size: clamp(1.125rem, 2vw, 1.25rem);
  letter-spacing: 2px;
  min-width: 600px;
}

.calculator-projection-table th,
.calculator-projection-table td {
  padding: 0.75rem;
  white-space: nowrap;
}

@media (max-width: 768px) {
  .calculator-projection-table {
    margin: 0 -1rem;
    border-radius: 0;
  }

  .calculator-projection-table table {
    font-size: 0.8rem;
    min-width: 500px;
  }

  .calculator-projection-table th,
  .calculator-projection-table td {
    padding: 0.5rem 0.375rem;
  }
}

@media (max-width: 480px) {
  .calculator-projection-table {
    margin: 0 -0.875rem;
  }

  .calculator-projection-table table {
    font-size: 0.75rem;
    min-width: 450px;
  }

  .calculator-projection-table th,
  .calculator-projection-table td {
    padding: 0.375rem 0.25rem;
  }
}

/* ===== RESPONSIVE TABLE SYSTEM ===== */

/* Base responsive table container */
.responsive-table-container {
  overflow-x: auto;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 0.5rem;
  -webkit-overflow-scrolling: touch;
  margin: var(--spacing-md) 0;
}

.responsive-table {
  width: 100%;
  border-collapse: collapse;
  font-size: clamp(1.125rem, 2vw, 1.25rem);
  letter-spacing: 2px;
  min-width: 600px;
}

.responsive-table th,
.responsive-table td {
  padding: var(--spacing-sm) var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  white-space: nowrap;
}

.responsive-table th {
  background: rgba(0, 0, 0, 0.3);
  font-weight: 600;
  color: #fbbf24;
  position: sticky;
  top: 0;
  z-index: 1;
}

.responsive-table td {
  color: #ffffff;
}

.responsive-table tr:hover {
  background: rgba(255, 255, 255, 0.05);
}

/* Financial table specific styling */
.financial-table {
  min-width: 700px;
}

.financial-table .currency {
  text-align: right;
  font-family: 'Courier New', monospace;
}

.financial-table .total-row {
  background: rgba(251, 191, 36, 0.1);
  font-weight: 600;
}

.financial-table .total-row td {
  border-top: 2px solid #fbbf24;
  color: #fbbf24;
}

/* Mobile responsive table adjustments */
@media (max-width: 768px) {
  .responsive-table-container {
    margin: var(--spacing-sm) -var(--spacing-md);
    border-radius: 0;
  }

  .responsive-table {
    font-size: 0.8rem;
    min-width: 500px;
  }

  .responsive-table th,
  .responsive-table td {
    padding: var(--spacing-xs) var(--spacing-sm);
  }

  .financial-table {
    min-width: 600px;
  }
}

@media (max-width: 480px) {
  .responsive-table-container {
    margin: var(--spacing-sm) -var(--spacing-sm);
  }

  .responsive-table {
    font-size: 0.75rem;
    min-width: 450px;
  }

  .responsive-table th,
  .responsive-table td {
    padding: var(--spacing-xs);
  }

  .financial-table {
    min-width: 500px;
  }
}

/* ===== HERO SECTION RESPONSIVE LAYOUT ===== */

.hero-section {
  min-height: 60vh;
  display: flex;
  align-items: center;
  position: relative;
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  padding: var(--spacing-md) 0;
}

/* Hero Section Background Variants */
.hero-section.hero-home {
  background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4)), url('https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/hero-background.jpg');
}

.hero-section.hero-calculator {
  background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4)), url('https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/calculator-hero.jpg');
}

.hero-section.hero-affiliate {
  background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4)), url('https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/affiliate-hero.jpg');
}

.hero-section.hero-phases {
  background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4)), url('https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/phases-hero.jpg');
}

.hero-section.hero-default {
  background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4)), url('https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/mining-hero.jpg');
}

.hero-content-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  width: 100%;
}

.hero-left-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: var(--spacing-md);
}

.hero-right-content {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: var(--spacing-lg);
}

.hero-stats-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-sm);
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.hero-logo {
  height: 3rem;
  width: auto;
  margin-bottom: var(--spacing-sm);
}

.hero-main-logo {
  width: 200px;
  height: auto;
  max-height: 150px;
  filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.4));
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.hero-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  width: 100%;
  max-width: 300px;
}

.hero-features {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
}

/* Mobile Large (480px+) */
@media (min-width: 480px) {
  .hero-stats-grid {
    grid-template-columns: repeat(3, 1fr);
    max-width: 500px;
  }

  .hero-buttons {
    flex-direction: row;
    max-width: 400px;
    gap: var(--spacing-md) !important;
    justify-content: center;
  }

  /* Force spacing between buttons with multiple approaches */
  .hero-buttons .btn {
    margin: 0 calc(var(--spacing-md) / 2) !important;
    flex: 0 0 auto;
  }

  .hero-buttons .btn:first-child {
    margin-left: 0 !important;
  }

  .hero-buttons .btn:last-child {
    margin-right: 0 !important;
  }

  /* Additional fallback for button spacing */
  .hero-buttons .btn + .btn {
    margin-left: var(--spacing-md) !important;
  }

  .hero-logo {
    height: 4rem;
  }
}

/* Additional fix for screens between 480px and 800px */
@media (min-width: 480px) and (max-width: 800px) {
  .hero-buttons {
    gap: 1rem !important;
    display: flex !important;
    flex-direction: row !important;
    justify-content: center !important;
    align-items: center !important;
  }

  .hero-buttons .btn {
    margin: 0 0.5rem !important;
    min-width: 140px;
    flex-shrink: 0 !important;
  }

  .hero-buttons .btn:first-child {
    margin-left: 0 !important;
  }

  .hero-buttons .btn:last-child {
    margin-right: 0 !important;
  }
}

/* Fallback for very specific range where buttons touch */
@media (min-width: 481px) and (max-width: 799px) {
  .hero-buttons {
    column-gap: 1rem !important;
  }

  .hero-buttons > * + * {
    margin-left: 1rem !important;
  }
}

/* Tablet (768px+) */
@media (min-width: 768px) {
  .hero-content {
    flex-direction: row;
    align-items: center;
    gap: var(--spacing-2xl);
  }

  .hero-main-content {
    flex: 1;
    align-items: flex-start;
    text-align: left;
    max-width: none;
  }

  .hero-stats-grid {
    flex: 0 0 auto;
    width: 300px;
    margin: 0;
  }

  .hero-buttons {
    max-width: none;
    width: auto;
  }

  .hero-features {
    justify-content: flex-start;
  }

  .hero-logo {
    margin: 0 0 var(--spacing-sm) 0;
  }
}

/* Desktop (1024px+) */
@media (min-width: 1024px) {
  .hero-section {
    background-attachment: fixed;
  }

  .hero-content-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    align-items: center;
  }

  .hero-section {
    min-height: 60vh;
    padding: var(--spacing-sm) 0;
  }

  .hero-left-content {
    align-items: flex-start;
    text-align: left;
  }

  .hero-right-content {
    margin-top: 0;
  }

  .hero-stats-grid {
    width: 350px;
  }

  .hero-logo {
    height: 5rem;
  }

  .hero-main-logo {
    width: 250px;
    max-height: 180px;
  }
}

/* Large Desktop (1200px+) */
@media (min-width: 1200px) {
  .hero-stats-grid {
    width: 400px;
  }
}

/* Enhanced Responsive Design - Mobile First */

/* Base mobile styles */
.aureus-container-inner {
  padding: 16px;
}

.btn-primary {
  padding: 8px 16px;
  font-size: 12px;
}

.text-gradient-gold,
.text-gradient-cyber {
  font-size: clamp(1.5rem, 5vw, 2.5rem);
}

.aureus-container {
  border-radius: 16px;
  padding: 2px;
}

/* Mobile Large (480px+) */
@media (min-width: 480px) {
  .aureus-container-inner {
    padding: 20px;
  }

  .btn-primary {
    padding: 10px 20px;
    font-size: 14px;
  }

  .text-gradient-gold,
  .text-gradient-cyber {
    font-size: clamp(2rem, 6vw, 3rem);
  }
}

/* Tablet (768px+) */
@media (min-width: 768px) {
  .aureus-container-inner {
    padding: 24px;
  }

  .text-gradient-gold,
  .text-gradient-cyber {
    font-size: clamp(2.5rem, 8vw, 4rem);
  }
}

/* Desktop (1024px+) */
@media (min-width: 1024px) {
  .aureus-container-inner {
    padding: 32px;
  }
}

/* Section Integration Styles */
.section-overlap {
  margin-top: -2rem;
  position: relative;
  z-index: 10;
}

.section-overlap-large {
  margin-top: -4rem;
  position: relative;
  z-index: 10;
}

/* Enhanced Visual Hierarchy */
.visual-separator {
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.3), transparent);
  margin: 3rem 0;
}

.content-flow {
  position: relative;
}

.content-flow::before {
  content: '';
  position: absolute;
  top: -2rem;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #FFD700, transparent);
  border-radius: 1px;
}

/* Smooth Section Transitions */
.section-transition {
  position: relative;
  z-index: 5;
}

.section-transition::before {
  content: '';
  position: absolute;
  top: -50px;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.1), transparent);
  pointer-events: none;
}

/* Enhanced Grid Layouts */
.responsive-grid {
  display: grid;
  gap: 2rem;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

@media (min-width: 768px) {
  .responsive-grid {
    gap: 2.5rem;
  }
}

@media (min-width: 1024px) {
  .responsive-grid {
    gap: 3rem;
  }
}

/* Professional Card Hover Effects */
.professional-card {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: center;
}

.professional-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Improved Typography Scaling */
.heading-responsive {
  font-size: clamp(2rem, 5vw, 4rem);
  line-height: 1.1;
  letter-spacing: -0.02em;
}

.subheading-responsive {
  font-size: clamp(1.25rem, 3vw, 1.5rem);
  line-height: 1.4;
}

.body-responsive {
  font-size: clamp(1rem, 2vw, 1.125rem);
  line-height: 1.6;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #f59e0b, #fbbf24);
}

/* Line clamp utilities for gallery */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* ===== GALLERY RESPONSIVE GRID SYSTEM ===== */

/* Mobile-first gallery grid */
.gallery-grid-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-md);
  width: 100%;
}

.gallery-image-item {
  width: 100%;
  max-width: 100%;
}

/* Mobile Large (480px+) */
@media (min-width: 480px) {
  .gallery-grid-container {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
  }
}

/* Tablet (768px+) */
@media (min-width: 768px) {
  .gallery-grid-container {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-xl);
  }
}

/* Desktop (1024px+) */
@media (min-width: 1024px) {
  .gallery-grid-container {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-2xl);
  }
}

/* Large Desktop (1200px+) */
@media (min-width: 1200px) {
  .gallery-grid-container {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-2xl);
  }
}

/* Legacy flexbox fallback for existing components */
.gallery-flex-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-md);
}

.gallery-flex-container > div {
  width: 100%;
  max-width: 350px;
  flex: 0 0 auto;
}

@media (min-width: 480px) {
  .gallery-flex-container {
    gap: var(--spacing-lg);
  }

  .gallery-flex-container > div {
    width: calc(50% - var(--spacing-lg) / 2);
    max-width: 300px;
  }
}

@media (min-width: 768px) {
  .gallery-flex-container {
    gap: var(--spacing-xl);
  }

  .gallery-flex-container > div {
    width: calc(33.333% - var(--spacing-xl) * 2 / 3);
    max-width: 280px;
  }
}

@media (min-width: 1024px) {
  .gallery-flex-container > div {
    width: calc(25% - var(--spacing-xl) * 3 / 4);
    max-width: 260px;
  }
}

/* Gallery animations and effects */
.gallery-image-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.gallery-image-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4), 0 0 20px rgba(251, 191, 36, 0.2);
}

.gallery-image-fade-in {
  animation: galleryFadeIn 0.6s ease-out forwards;
}

@keyframes galleryFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.gallery-loading-shimmer {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* ===== RESPONSIVE FOOTER LAYOUT ===== */

.footer-container {
  background: #000000;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: var(--spacing-lg) 0;
}

.footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  text-align: center;
}

.footer-logo {
  font-size: clamp(1.75rem, 3vw, 2rem);
  font-weight: 800;
  color: #fbbf24;
  letter-spacing: 2px;
  margin-bottom: var(--spacing-sm);
}

.footer-nav {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.footer-nav a {
  color: #e5e7eb;
  text-decoration: none;
  font-size: clamp(1.125rem, 2vw, 1.25rem);
  letter-spacing: 2px;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: 0.25rem;
  transition: all 0.3s ease;
}

.footer-nav a:hover {
  color: #fbbf24;
  background: rgba(251, 191, 36, 0.1);
}

.footer-legal-nav {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-legal-nav button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  transition: color 0.3s ease;
}

.footer-legal-nav button:hover {
  color: var(--gold-primary);
}

.footer-cta {
  margin-bottom: var(--spacing-md);
}

.footer-legal {
  padding-top: var(--spacing-md);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  width: 100%;
}

.footer-legal p {
  font-size: 0.75rem;
  color: #9ca3af;
  margin-bottom: var(--spacing-xs);
  line-height: 1.5;
}

/* Tablet responsive footer */
@media (min-width: 768px) {
  .footer-container {
    padding: var(--spacing-xl) 0;
  }

  .footer-content {
    gap: var(--spacing-lg);
  }

  .footer-logo {
    font-size: 2rem;
  }

  .footer-nav {
    gap: var(--spacing-md);
  }

  .footer-nav a {
    font-size: 1rem;
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .footer-legal p {
    font-size: 0.875rem;
  }
}

/* Desktop responsive footer */
@media (min-width: 1024px) {
  .footer-container {
    padding: var(--spacing-2xl) 0;
  }

  .footer-content {
    gap: var(--spacing-xl);
  }
}

/* ===== RESPONSIVE NAVIGATION SYSTEM ===== */

/* Enhanced Floating Navigation */
.header-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  backdrop-filter: blur(20px);
  background: var(--theme-nav-bg);
  border-bottom: 1px solid rgba(251, 191, 36, 0.2);
  box-shadow: 0 4px 20px var(--theme-shadow);
  transition: all 0.3s ease;
}

.header-container.scrolled {
  backdrop-filter: blur(25px);
  box-shadow: 0 8px 32px var(--theme-shadow);
}

.header-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) 0;
  position: relative;
  z-index: 10;
}

.header-logo {
  font-size: clamp(1.75rem, 3vw, 2rem);
  font-weight: 800;
  color: #fbbf24;
  letter-spacing: 2px;
  text-decoration: none;
  display: flex;
  flex-direction: column;
  line-height: 1;
}

.header-logo-subtitle {
  font-size: clamp(1rem, 1.5vw, 1.125rem);
  font-weight: 400;
  color: #9ca3af;
  letter-spacing: 2px;
  margin-top: -0.25rem;
}

.header-logo-image {
  text-decoration: none;
  display: flex;
  align-items: center;
}

/* Enhanced Header Logo */
.header-logo-img {
  height: 2rem;
  width: auto;
  max-width: none;
  object-fit: contain;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  transition: all 0.3s ease;
}

.header-logo-img:hover {
  filter: drop-shadow(0 4px 8px rgba(251, 191, 36, 0.4));
  transform: scale(1.05);
}

/* Floating Logo Animation */
.float-animation {
  animation: headerFloat 4s ease-in-out infinite;
}

@keyframes headerFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-2px); }
}

.desktop-nav {
  display: none;
  align-items: center;
  gap: var(--spacing-lg);
}

.desktop-nav a {
  color: #e5e7eb;
  text-decoration: none;
  font-size: clamp(1.125rem, 2vw, 1.25rem);
  font-weight: 500;
  letter-spacing: 2px;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: 0.375rem;
  transition: all 0.3s ease;
}

.desktop-nav a:hover {
  color: #fbbf24;
  background: rgba(251, 191, 36, 0.1);
}

.desktop-actions {
  display: none;
  align-items: center;
  gap: var(--spacing-md);
}

/* Desktop single-row elements (hidden by default) */
.desktop-logo,
.desktop-nav-single,
.desktop-actions-single {
  display: none;
}





/* Mobile Menu Styles */
.mobile-menu-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: transparent;
  border: none;
  cursor: pointer;
  z-index: 60;
  position: relative;
}

.hamburger-icon {
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
}

.hamburger-line {
  width: 100%;
  height: 2px;
  background: #fbbf24;
  border-radius: 1px;
  transition: all 0.3s ease;
  transform-origin: center;
}

.hamburger-line.open:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.hamburger-line.open:nth-child(2) {
  opacity: 0;
}

.hamburger-line.open:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
  z-index: 40;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mobile-menu-overlay.open {
  opacity: 1;
  visibility: visible;
}

.mobile-menu-panel {
  position: fixed;
  top: 0;
  right: -100%;
  width: 280px;
  height: 100vh;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(20px);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 50;
  transition: right 0.3s ease;
  overflow-y: auto;
}

.mobile-menu-panel.open {
  right: 0;
}

.mobile-menu-content {
  padding: var(--spacing-xl) var(--spacing-lg);
  padding-top: 5rem;
}

.mobile-menu-nav {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.mobile-menu-nav a {
  color: #e5e7eb;
  text-decoration: none;
  font-size: 1.125rem;
  font-weight: 500;
  letter-spacing: 2px;
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.mobile-menu-nav a:hover {
  color: #fbbf24;
  padding-left: var(--spacing-sm);
}

.mobile-menu-actions {
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-lg);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Base tablet and desktop navigation */
@media (min-width: 768px) {
  .mobile-menu-button {
    display: none;
  }

  .header-logo {
    font-size: 1.875rem;
  }

  .header-logo-subtitle {
    font-size: 0.875rem;
  }

  .header-logo-img {
    height: 2.25rem;
    width: auto;
    max-width: none;
    object-fit: contain;
  }

  .desktop-nav {
    display: flex;
  }

  .desktop-actions {
    display: flex;
  }

  .header-nav-top,
  .header-nav-bottom {
    display: none;
  }
}

/* Tablet specific adjustments - Two Layer Navigation */
@media (min-width: 768px) and (max-width: 1023px) {
  .header-nav {
    flex-direction: column;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) 0;
  }

  .header-nav-top {
    display: flex !important;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .header-nav-bottom {
    display: flex !important;
    justify-content: center;
    width: 100%;
  }

  /* Hide the original single-row layout elements */
  .header-nav > .header-logo-image,
  .header-nav > .desktop-nav,
  .header-nav > .desktop-actions {
    display: none;
  }

  .desktop-nav {
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--spacing-md) var(--spacing-lg);
    max-width: 100%;
  }

  .desktop-nav a {
    font-size: 0.875rem;
    padding: var(--spacing-xs) var(--spacing-sm);
    letter-spacing: 1px;
    white-space: nowrap;
  }

  .desktop-actions {
    flex-shrink: 0;
  }

  .desktop-actions .btn {
    font-size: 0.8rem;
    padding: 0.5rem 0.875rem;
    white-space: nowrap;
  }
}

/* Large tablet and small desktop */
@media (min-width: 1024px) {
  .header-nav {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0;
  }

  /* Hide tablet layout on desktop */
  .tablet-layout {
    display: none !important;
  }

  /* Show desktop single-row elements */
  .desktop-logo,
  .desktop-nav-single,
  .desktop-actions-single {
    display: flex !important;
  }

  .desktop-nav-single {
    gap: var(--spacing-lg);
    flex-wrap: nowrap;
    justify-content: center;
  }

  .desktop-nav-single a {
    font-size: 1rem;
    padding: var(--spacing-sm) var(--spacing-md);
    letter-spacing: 2px;
  }
}

@media (max-width: 480px) {
  .header-logo {
    font-size: 1.25rem;
  }

  .header-logo-subtitle {
    font-size: 0.7rem;
  }

  .header-logo-img {
    height: 1.75rem;
    width: auto;
    max-width: none;
    object-fit: contain;
  }
}

/* ===== FINAL RESPONSIVE OPTIMIZATIONS ===== */

/* Prevent horizontal scrolling on all devices */
html, body {
  overflow-x: hidden;
  max-width: 100vw;
}

/* Ensure all containers respect viewport width */
* {
  box-sizing: border-box;
}

/* Responsive image handling */
img {
  max-width: 100%;
  height: auto;
}

/* Responsive video handling */
video {
  max-width: 100%;
  height: auto;
}

/* Responsive iframe handling */
iframe {
  max-width: 100%;
}

/* Responsive table fallback */
table {
  max-width: 100%;
}

/* Ensure text doesn't break layout */
p, h1, h2, h3, h4, h5, h6 {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Mobile-specific optimizations */
@media (max-width: 767px) {
  /* Reduce motion for better mobile performance */
  * {
    animation-duration: 0.3s !important;
    transition-duration: 0.3s !important;
  }

  /* Optimize touch interactions */
  button, a, input, select, textarea {
    -webkit-tap-highlight-color: rgba(251, 191, 36, 0.3);
  }

  /* Improve text readability on mobile */
  body {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* Sharper borders and shadows on high DPI displays */
  .card, .btn, .glass-card {
    border-width: 0.5px;
  }
}

/* Print styles */
@media print {
  .header-container {
    position: static !important;
  }

  body {
    background: white !important;
    color: black !important;
  }
}

/* Custom CSS overrides as requested */
.rounded-full {
    margin-right: 15px;
}

.border {
    border-width: 0px;
}

.text-amber-500 {
    margin-right: 5px;
}

.text-green-400 {
    margin-right: 5px;
}

.max-w-2xl {
    max-width: 100rem;
}

.bg-white\/5 {
    background-color: rgb(255 255 255 / 0%) !important;
}

/* ===== GOLD DIGGERS CLUB STYLES ===== */
.gold-diggers-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(229, 231, 235, 0.5);
  border-radius: 0.75rem;
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  transition: all 0.3s ease;
  min-height: 400px;
  display: flex;
  flex-direction: column;
}

.gold-diggers-card:hover {
  border-color: rgba(251, 191, 36, 0.3);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(251, 191, 36, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid rgba(107, 114, 128, 0.3);
  flex-shrink: 0;
}

.card-icon {
  font-size: 1.5rem;
  margin-right: var(--spacing-sm);
}

.card-title {
  font-size: clamp(1.5rem, 3vw, 1.75rem);
  font-weight: 700;
  color: #fbbf24;
  letter-spacing: 2px;
  flex: 1;
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 600;
}

.live-dot {
  width: 0.5rem;
  height: 0.5rem;
  background: #22c55e;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.step-item {
  display: flex;
  gap: var(--spacing-md);
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
}

.step-number {
  width: 2rem;
  height: 2rem;
  background: #fbbf24;
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.875rem;
  flex-shrink: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.step-content {
  flex: 1;
}

.step-title {
  font-weight: 600;
  color: #ffffff;
  margin-bottom: var(--spacing-xs);
}

.step-description {
  color: #d1d5db;
  font-size: 0.875rem;
  line-height: 1.6;
}

.competition-status {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.status-icon {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-sm);
}

.status-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #fbbf24;
  margin-bottom: var(--spacing-sm);
}

.status-description {
  color: #d1d5db;
  margin-bottom: var(--spacing-sm);
}

.status-subtitle {
  color: #fbbf24;
  font-weight: 600;
}

.qualification-badge {
  background: rgba(251, 191, 36, 0.1);
  border: 1px solid rgba(251, 191, 36, 0.3);
  border-radius: 0.5rem;
  padding: var(--spacing-md);
  color: #fbbf24;
  font-weight: 600;
  margin-top: var(--spacing-lg);
}

.prize-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.prize-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  border-radius: 0.5rem;
  border: 1px solid;
}

.prize-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-leader {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding-top: var(--spacing-xs);
  border-top: 1px solid rgba(107, 114, 128, 0.3);
}

.leader-label {
  font-size: 0.75rem;
  color: #9ca3af;
  font-weight: 500;
}

.leader-name {
  font-size: 0.875rem;
  color: #ffffff;
  font-weight: 600;
}

/* Competition Tabs */
.competition-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing-xl);
}

.competition-tabs .tabs-container {
  display: flex;
  gap: var(--spacing-sm);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(229, 231, 235, 0.5);
  border-radius: 0.75rem;
  padding: var(--spacing-xs);
  overflow-x: auto;
  max-width: 100%;
  flex-wrap: nowrap;
  scrollbar-width: thin;
}

.competition-tabs .tab-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: 0.5rem;
  background: transparent;
  color: #9ca3af;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: fit-content;
  flex-shrink: 0;
}

.competition-tabs .tab-button:hover {
  background: rgba(229, 231, 235, 0.5);
  color: #374151;
}

.competition-tabs .tab-button.active {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: #000000;
  font-weight: 600;
}

.competition-tabs .tab-button.loading {
  opacity: 0.6;
  cursor: not-allowed;
}

.competition-tabs .tab-button.loading .tab-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.tab-icon {
  font-size: 1rem;
}

.tab-text {
  font-size: 0.875rem;
}

.tab-status {
  font-size: 0.75rem;
}

/* Responsive tabs */
@media (max-width: 768px) {
  .competition-tabs .tabs-container {
    gap: var(--spacing-xs);
    padding: var(--spacing-xs);
  }

  .competition-tabs .tab-button {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.75rem;
  }

  .tab-text {
    font-size: 0.75rem;
  }

  .tab-icon {
    font-size: 0.875rem;
  }
}

/* Current Competition Badge */
.current-competition-badge {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  margin: var(--spacing-sm) 0;
  padding: var(--spacing-sm) var(--spacing-md);
  background: rgba(31, 41, 55, 0.6);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(107, 114, 128, 0.3);
  border-radius: 0.5rem;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.competition-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: #fbbf24;
}

.competition-status {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.competition-status.active {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.competition-status.ended {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.competition-status.upcoming {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.prize-first {
  background: linear-gradient(to right, rgba(234, 179, 8, 0.2), rgba(245, 158, 11, 0.2));
  border-color: rgba(234, 179, 8, 0.3);
}

.prize-second {
  background: linear-gradient(to right, rgba(156, 163, 175, 0.2), rgba(107, 114, 128, 0.2));
  border-color: rgba(156, 163, 175, 0.3);
}

.prize-third {
  background: linear-gradient(to right, rgba(217, 119, 6, 0.2), rgba(249, 115, 22, 0.2));
  border-color: rgba(217, 119, 6, 0.3);
}

.prize-other {
  background: rgba(55, 65, 81, 0.3);
  border-color: rgba(75, 85, 99, 0.3);
}

.prize-rank {
  font-weight: 600;
  color: #ffffff;
}

.prize-amount {
  font-weight: 700;
  color: #fbbf24;
  font-size: 1.125rem;
}

.prize-note {
  font-size: 0.875rem;
  color: #9ca3af;
  text-align: center;
  margin-top: var(--spacing-md);
  padding: var(--spacing-sm);
  background: rgba(31, 41, 55, 0.5);
  border-radius: 0.5rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(229, 231, 235, 0.5);
  border-radius: 0.75rem;
  padding: var(--spacing-xl);
  text-align: center;
  transition: all 0.3s ease;
  margin-bottom: var(--spacing-lg);
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.stat-card:hover {
  border-color: rgba(251, 191, 36, 0.3);
}

.stat-number {
  font-size: clamp(2.25rem, 4vw, 2.5rem);
  font-weight: 900;
  color: #fbbf24;
  letter-spacing: 2px;
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  color: var(--gold);
  font-size: clamp(1.125rem, 2vw, 1.25rem);
  text-transform: uppercase;
  letter-spacing: 2px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.btn-gold-diggers {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg) var(--spacing-xl);
  background: linear-gradient(to right, #fbbf24, #f59e0b);
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
  font-weight: 700;
  font-size: 1.125rem;
  border-radius: 0.75rem;
  text-decoration: none;
  transition: all 0.3s ease;
  transform-origin: center;
}

.btn-gold-diggers:hover {
  background: linear-gradient(to right, #f59e0b, #fbbf24);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(251, 191, 36, 0.3);
  transform: scale(1.05);
}

.btn-gold-diggers:active {
  transform: scale(0.95);
}

.btn-gold-diggers:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(251, 191, 36, 0.5), 0 0 0 4px rgba(0, 0, 0, 1);
}

/* Gold Diggers Club Grid Layout Fixes */
#gold-diggers-club .grid {
  gap: var(--spacing-xl) !important;
  margin-bottom: var(--spacing-xl);
}

#gold-diggers-club .grid-cols-2 {
  grid-template-columns: 1fr 1fr;
}

#gold-diggers-club .grid-cols-3 {
  grid-template-columns: 2fr 1fr;
}

#gold-diggers-club .col-span-2 {
  grid-column: span 1;
}

#gold-diggers-club .space-y-lg > * + * {
  margin-top: var(--spacing-lg);
}

/* Competition Status Spacing */
.competition-status {
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.qualification-badge {
  background: rgba(251, 191, 36, 0.1);
  border: 1px solid rgba(251, 191, 36, 0.3);
  border-radius: 0.5rem;
  padding: var(--spacing-md);
  margin-top: var(--spacing-lg);
  color: #fbbf24;
  font-weight: 600;
  text-align: center;
}

/* Phase Progression & Commission Structure Styles */
.phase-progression-container {
  width: 100%;
}

.phase-progression-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.commission-highlight {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(251, 191, 36, 0.05));
  border: 1px solid rgba(251, 191, 36, 0.3);
  border-radius: 12px;
  padding: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.commission-rate {
  font-size: 1.5rem;
  font-weight: 700;
  color: #fbbf24;
  text-shadow: 0 0 10px rgba(251, 191, 36, 0.3);
}

.commission-label {
  font-size: 0.9rem;
  color: #d1d5db;
  font-weight: 500;
}

.phases-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.phase-item {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(229, 231, 235, 0.5);
  border-radius: 12px;
  padding: var(--spacing-lg);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.phase-item:hover {
  border-color: rgba(251, 191, 36, 0.5);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}

.phase-item.active {
  border-color: #fbbf24;
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(251, 191, 36, 0.05));
  box-shadow: 0 0 20px rgba(251, 191, 36, 0.2);
}

.phase-item.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #fbbf24, #f59e0b);
}

.phase-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.phase-icon {
  font-size: 1.5rem;
}

.phase-label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.phase-status {
  background: #fbbf24;
  color: #000;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.phase-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.phase-price {
  font-size: 1.2rem;
  font-weight: 700;
  color: #fbbf24;
}

.phase-target {
  font-size: 0.9rem;
  color: #d1d5db;
  font-weight: 500;
}

.phase-commission {
  font-size: 0.85rem;
  color: #10b981;
  font-weight: 600;
  background: rgba(16, 185, 129, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  text-align: center;
}

.phase-progression-footer {
  text-align: center;
  padding-top: var(--spacing-lg);
  border-top: 1px solid rgba(75, 85, 99, 0.3);
}

.total-target {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
}

.total-label {
  font-size: 1.1rem;
  color: #d1d5db;
  font-weight: 500;
}

.total-amount {
  font-size: 1.5rem;
  font-weight: 700;
  color: #fbbf24;
  text-shadow: 0 0 10px rgba(251, 191, 36, 0.3);
}

.commission-note {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

/* Mobile Responsive Fixes */
@media (max-width: 768px) {
  #gold-diggers-club .grid-cols-2,
  #gold-diggers-club .grid-cols-3 {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg) !important;
  }

  #gold-diggers-club .col-span-2 {
    grid-column: span 1;
  }

  .gold-diggers-card {
    min-height: auto;
    margin-bottom: var(--spacing-md);
  }

  .stat-card {
    min-height: 100px;
    margin-bottom: var(--spacing-md);
  }

  .phases-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
  }

  .phase-item {
    padding: var(--spacing-md);
  }

  .commission-rate {
    font-size: 1.2rem;
  }

  .total-target {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .total-amount {
    font-size: 1.3rem;
  }
}

/* ===== PHASE-SPECIFIC INTERFACE STYLES ===== */

/* Phase Tabs Navigation */
.phase-tabs-container {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(229, 231, 235, 0.5);
  border-radius: 16px;
  padding: var(--spacing-lg);
}

.phase-tabs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.phase-tabs-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.breadcrumb {
  font-size: 0.9rem;
  color: #9ca3af;
}

.breadcrumb-phase {
  color: #fbbf24;
  font-weight: 600;
}

.phase-tabs-scroll {
  overflow-x: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(251, 191, 36, 0.3) transparent;
}

.phase-tabs {
  display: flex;
  gap: var(--spacing-md);
  min-width: max-content;
  padding-bottom: var(--spacing-sm);
}

.phase-tab {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
  border: 1px solid rgba(229, 231, 235, 0.5);
  border-radius: 12px;
  padding: var(--spacing-md);
  min-width: 120px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.phase-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(251, 191, 36, 0.1), transparent);
  transition: left 0.5s ease;
}

.phase-tab:hover {
  border-color: rgba(251, 191, 36, 0.5);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(251, 191, 36, 0.2);
}

.phase-tab:hover::before {
  left: 100%;
}

.phase-tab.active {
  border-color: #fbbf24;
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.2), rgba(251, 191, 36, 0.1));
  box-shadow: 0 5px 20px rgba(251, 191, 36, 0.3);
  box-shadow: 0 0 20px rgba(251, 191, 36, 0.2);
}

.phase-tab.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #fbbf24, #f59e0b);
  border-radius: 12px 12px 0 0;
}

.phase-tab-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  text-align: center;
}

.phase-tab-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.phase-tab-icon {
  font-size: 1.2rem;
}

.phase-tab-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.phase-tab-badge {
  background: #fbbf24;
  color: #000;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
}

.phase-tab-price {
  font-size: 0.8rem;
  color: #10b981;
  font-weight: 600;
}

/* Phase Overview Card */
.phase-overview-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
}

.phase-overview-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.phase-overview-icon {
  font-size: 2rem;
}

.phase-overview-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.phase-overview-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.phase-overview-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.phase-status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.phase-status-badge.active {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.phase-status-badge.upcoming {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
  border: 1px solid rgba(251, 191, 36, 0.3);
}

.phase-status-badge.completed {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.phase-number {
  font-size: 0.9rem;
  color: #9ca3af;
  font-weight: 500;
}

.phase-progress-summary {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  min-width: 200px;
}

.progress-percentage {
  font-size: 1.1rem;
  font-weight: 600;
  color: #fbbf24;
  text-align: right;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(229, 231, 235, 0.5);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #fbbf24, #f59e0b);
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* Earnings Structure Styles */
.earnings-structure {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.share-price-section {
  text-align: center;
  padding: var(--spacing-lg);
  background: rgba(17, 24, 39, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(75, 85, 99, 0.3);
}

.share-price-main {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.share-price-label {
  font-size: 0.9rem;
  color: #9ca3af;
  font-weight: 500;
}

.share-price-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: #fbbf24;
  text-shadow: 0 0 10px rgba(251, 191, 36, 0.3);
}

.commission-structure .commission-highlight {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05));
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 8px;
  padding: var(--spacing-md);
}

.commission-structure .commission-rate {
  font-size: 1.2rem;
  font-weight: 700;
  color: #10b981;
}

.commission-structure .commission-label {
  font-size: 0.8rem;
  color: #d1d5db;
  font-weight: 500;
}

.phase-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-md);
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  text-align: center;
  padding: var(--spacing-md);
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  border: 1px solid rgba(75, 85, 99, 0.2);
}

.metric-label {
  font-size: 0.8rem;
  color: #9ca3af;
  font-weight: 500;
}

.metric-value {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.progress-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-label {
  font-size: 0.9rem;
  color: #9ca3af;
  font-weight: 500;
}

.progress-bar-large {
  width: 100%;
  height: 12px;
  background: rgba(229, 231, 235, 0.5);
  border-radius: 6px;
  overflow: hidden;
}

.progress-bar-large .progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #fbbf24, #f59e0b);
  border-radius: 6px;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: #9ca3af;
}

/* Timeline Styles */
.timeline-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.timeline-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  background: rgba(31, 41, 55, 0.3);
  border-radius: 8px;
  border: 1px solid rgba(75, 85, 99, 0.2);
}

.timeline-item.highlight {
  background: rgba(251, 191, 36, 0.1);
  border-color: rgba(251, 191, 36, 0.3);
}

.timeline-label {
  font-size: 0.9rem;
  color: #9ca3af;
  font-weight: 500;
}

.timeline-value {
  font-size: 0.9rem;
  color: var(--text-primary);
  font-weight: 600;
  text-align: right;
}

/* Competition Content Styles */
.competition-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.competition-status-section {
  text-align: center;
  padding: var(--spacing-lg);
  background: rgba(17, 24, 39, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(75, 85, 99, 0.3);
}

.status-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.status-icon {
  font-size: 1.5rem;
}

.status-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.status-description {
  font-size: 0.9rem;
  color: #d1d5db;
  line-height: 1.5;
}

.competition-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-md);
}

.competition-metric {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  text-align: center;
  padding: var(--spacing-md);
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  border: 1px solid rgba(75, 85, 99, 0.2);
}

.competition-metric .metric-value.prize-pool {
  color: #fbbf24;
  font-size: 1.3rem;
  text-shadow: 0 0 10px rgba(251, 191, 36, 0.3);
}

.leaderboard-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.leaderboard-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  text-align: center;
  margin: 0;
}

.leaderboard-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.leaderboard-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  border: 1px solid rgba(229, 231, 235, 0.5);
  transition: all 0.2s ease;
}

.leaderboard-item:hover {
  border-color: rgba(251, 191, 36, 0.3);
  background: rgba(251, 191, 36, 0.05);
}

.rank-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.rank-number {
  font-weight: 700;
  color: #fbbf24;
  min-width: 24px;
}

.participant-name {
  font-size: 0.9rem;
  color: var(--text-primary);
  font-weight: 500;
}

.participant-volume {
  font-size: 0.9rem;
  color: #10b981;
  font-weight: 600;
}

.empty-leaderboard {
  text-align: center;
  padding: var(--spacing-xl);
  background: rgba(31, 41, 55, 0.3);
  border-radius: 12px;
  border: 1px solid rgba(75, 85, 99, 0.2);
}

.empty-icon {
  font-size: 2rem;
  margin-bottom: var(--spacing-md);
}

.empty-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.empty-description {
  font-size: 0.9rem;
  color: #9ca3af;
  line-height: 1.5;
}

.competition-action {
  text-align: center;
  padding: var(--spacing-lg);
  background: rgba(17, 24, 39, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(75, 85, 99, 0.3);
}

.btn-competition {
  display: inline-block;
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: #000;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  margin-bottom: var(--spacing-md);
}

.btn-competition:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(251, 191, 36, 0.3);
}

.btn-competition.secondary {
  background: rgba(75, 85, 99, 0.5);
  color: var(--text-primary);
  border: 1px solid rgba(75, 85, 99, 0.5);
}

.btn-competition.secondary:hover {
  background: rgba(75, 85, 99, 0.7);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.competition-deadline {
  font-size: 0.8rem;
  color: #9ca3af;
  margin: 0;
}

.no-competition {
  text-align: center;
  padding: var(--spacing-xl);
  background: rgba(31, 41, 55, 0.3);
  border-radius: 12px;
  border: 1px solid rgba(75, 85, 99, 0.2);
}

.no-competition-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-lg);
  opacity: 0.7;
}

.no-competition-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-md) 0;
}

.no-competition-description {
  font-size: 0.9rem;
  color: #9ca3af;
  line-height: 1.5;
  margin-bottom: var(--spacing-lg);
}

/* Phase CTA Section */
.phase-cta-section {
  margin-top: var(--spacing-xl);
}

.cta-content {
  padding: var(--spacing-xl);
}

.cta-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-md) 0;
}

.cta-description {
  font-size: 1rem;
  color: #d1d5db;
  line-height: 1.6;
  margin-bottom: var(--spacing-lg);
}

.cta-highlights {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  flex-wrap: wrap;
}

.cta-highlight {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: rgba(17, 24, 39, 0.5);
  border-radius: 8px;
  border: 1px solid rgba(75, 85, 99, 0.3);
}

.highlight-icon {
  font-size: 1.2rem;
}

.highlight-text {
  font-size: 0.9rem;
  color: var(--text-primary);
  font-weight: 600;
}

.cta-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.btn-primary-large {
  display: inline-block;
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: #000;
  padding: var(--spacing-lg) var(--spacing-xl);
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 700;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(251, 191, 36, 0.2);
}

.btn-primary-large:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(251, 191, 36, 0.4);
}

.cta-note {
  font-size: 0.8rem;
  color: #9ca3af;
  margin: 0;
}

/* Mobile Responsive for Phase Interface */
@media (max-width: 768px) {
  .phase-tabs-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .phase-tabs {
    gap: var(--spacing-sm);
  }

  .phase-tab {
    min-width: 100px;
    padding: var(--spacing-sm);
  }

  .phase-tab-name {
    font-size: 0.8rem;
  }

  .phase-tab-price {
    font-size: 0.7rem;
  }

  .phase-overview-card .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  .phase-progress-summary {
    min-width: auto;
    width: 100%;
  }

  .progress-percentage {
    text-align: left;
  }

  .share-price-value {
    font-size: 2rem;
  }

  .phase-metrics {
    grid-template-columns: 1fr;
  }

  .competition-details {
    grid-template-columns: 1fr;
  }

  .cta-highlights {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .btn-primary-large {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 1rem;
  }
}

/* ===== MODERN CALCULATOR STYLES ===== */
.calculator-layout-modern {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-xl);
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  overflow-x: hidden;
}

/* Force mobile constraints */
@media (max-width: 480px) {
  .calculator-layout-modern {
    max-width: 100vw !important;
    width: 100vw !important;
    margin: 0 !important;
    padding: 0 !important;
    gap: var(--spacing-md) !important;
  }
}

@media (max-width: 360px) {
  .calculator-layout-modern {
    gap: var(--spacing-sm) !important;
  }
}

/* Calculator Section Container */
#calculator {
  overflow-x: hidden;
  width: 100%;
}

#calculator .container {
  max-width: 100%;
  overflow-x: hidden;
  padding-left: var(--spacing-md);
  padding-right: var(--spacing-md);
}

@media (max-width: 480px) {
  #calculator .container {
    padding-left: var(--spacing-sm);
    padding-right: var(--spacing-sm);
  }
}

@media (max-width: 360px) {
  #calculator .container {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
}

.calculator-inputs-modern {
  background: rgba(17, 24, 39, 0.6);
  border: 1px solid rgba(107, 114, 128, 0.3);
  border-radius: 1rem;
  padding: var(--spacing-xl);
  backdrop-filter: blur(8px);
}

.inputs-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.modern-inputs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
}

/* Mobile Responsive Overrides for Modern Calculator */
@media (max-width: 768px) {
  .calculator-layout-modern {
    gap: var(--spacing-lg);
    padding: 0 var(--spacing-sm);
  }

  .calculator-inputs-modern {
    padding: var(--spacing-lg);
    margin: 0 -var(--spacing-xs);
  }

  .modern-inputs-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .input-group {
    padding: var(--spacing-sm);
  }

  .input-label {
    font-size: 0.8rem;
  }

  .modern-input {
    padding: var(--spacing-sm);
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .calculator-inputs-modern {
    padding: var(--spacing-sm) !important;
    margin: 0 !important;
    border-radius: 0.75rem;
  }

  .calculator-inputs-modern .inputs-header h3,
  .inputs-header h3.text-xl {
    font-size: 1rem !important;
    line-height: 1.3 !important;
  }

  .calculator-inputs-modern .inputs-header p,
  .inputs-header p.text-sm {
    font-size: 0.75rem !important;
    line-height: 1.4 !important;
  }

  .calculator-inputs-modern .input-group {
    padding: var(--spacing-xs) !important;
    margin-bottom: var(--spacing-xs) !important;
  }

  .calculator-inputs-modern .input-group.editable,
  .calculator-inputs-modern .input-group.readonly {
    padding: var(--spacing-sm) !important;
  }

  .calculator-inputs-modern .input-label {
    font-size: 0.7rem !important;
    margin-bottom: 0.25rem !important;
    line-height: 1.2 !important;
  }

  .calculator-inputs-modern .modern-input {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.8rem !important;
    min-height: 40px !important;
  }

  .calculator-inputs-modern .modern-input select {
    font-size: 0.8rem !important;
  }
}

/* Extra small screens - very tight constraints */
@media (max-width: 360px) {
  .calculator-inputs-modern {
    padding: 0.75rem !important;
    margin: 0 -0.25rem !important;
  }

  .calculator-inputs-modern .inputs-header h3,
  .inputs-header h3.text-xl {
    font-size: 0.9rem !important;
    margin-bottom: 0.5rem !important;
  }

  .calculator-inputs-modern .inputs-header p,
  .inputs-header p.text-sm {
    font-size: 0.7rem !important;
    margin-bottom: 1rem !important;
  }

  .calculator-inputs-modern .input-group {
    padding: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  .calculator-inputs-modern .input-group.editable,
  .calculator-inputs-modern .input-group.readonly {
    padding: 0.625rem !important;
  }

  .calculator-inputs-modern .input-label {
    font-size: 0.65rem !important;
    margin-bottom: 0.2rem !important;
    letter-spacing: 0.02em !important;
  }

  .calculator-inputs-modern .modern-input {
    padding: 0.4rem 0.6rem !important;
    font-size: 0.75rem !important;
    min-height: 36px !important;
  }

  .calculator-inputs-modern .modern-input select {
    font-size: 0.75rem !important;
  }

  .calculator-inputs-modern .modern-inputs-grid {
    gap: 0.75rem !important;
  }
}

/* Force mobile text sizing - override Tailwind */
@media (max-width: 480px) {
  #calculator .text-xl,
  #calculator h3.text-xl {
    font-size: 1rem !important;
  }

  #calculator .text-sm,
  #calculator p.text-sm {
    font-size: 0.75rem !important;
  }

  #calculator .mb-2 {
    margin-bottom: 0.5rem !important;
  }

  #calculator .mb-6 {
    margin-bottom: 1rem !important;
  }
}

@media (max-width: 360px) {
  #calculator .text-xl,
  #calculator h3.text-xl {
    font-size: 0.9rem !important;
  }

  #calculator .text-sm,
  #calculator p.text-sm {
    font-size: 0.7rem !important;
  }
}

/* COMPREHENSIVE MOBILE CALCULATOR FIX */
@media (max-width: 480px) {
  /* Force container constraints */
  #calculator {
    width: 100vw !important;
    max-width: 100vw !important;
    overflow-x: hidden !important;
  }

  #calculator .container {
    width: 100% !important;
    max-width: 100% !important;
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
    margin: 0 !important;
  }

  /* Calculator card constraints */
  .calculator-container {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0.75rem !important;
    box-sizing: border-box !important;
  }

  /* Modern layout constraints */
  .calculator-layout-modern {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    gap: 1rem !important;
  }

  /* Input section constraints */
  .calculator-inputs-modern {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0.75rem !important;
    box-sizing: border-box !important;
  }

  /* Grid constraints */
  .modern-inputs-grid {
    width: 100% !important;
    max-width: 100% !important;
    grid-template-columns: 1fr !important;
    gap: 0.75rem !important;
  }

  /* Input group constraints */
  .calculator-inputs-modern .input-group {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0.5rem !important;
    margin: 0 0 0.5rem 0 !important;
    box-sizing: border-box !important;
  }

  /* Input field constraints */
  .calculator-inputs-modern .modern-input {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0.5rem !important;
    font-size: 0.8rem !important;
    box-sizing: border-box !important;
    min-height: 40px !important;
  }

  /* Label constraints */
  .calculator-inputs-modern .input-label {
    font-size: 0.7rem !important;
    margin-bottom: 0.25rem !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
  }
}

@media (max-width: 360px) {
  /* Even tighter constraints for very small screens */
  .calculator-container {
    padding: 0.5rem !important;
  }

  .calculator-inputs-modern {
    padding: 0.5rem !important;
  }

  .calculator-inputs-modern .input-group {
    padding: 0.375rem !important;
  }

  .calculator-inputs-modern .modern-input {
    padding: 0.375rem !important;
    font-size: 0.75rem !important;
    min-height: 36px !important;
  }

  .calculator-inputs-modern .input-label {
    font-size: 0.65rem !important;
  }

  .modern-inputs-grid {
    gap: 0.5rem !important;
  }
}

/* ===== RESPONSIVE WIDTH ADJUSTMENTS FOR CALCULATOR SECTIONS ===== */







/* Tablet Portrait (600px - 710px) */
@media (min-width: 600px) and (max-width: 710px) {
  .calculator-inputs-modern,
  .calculator-results-modern,
  .projection-section-modern {
    max-width: 100% !important;
    margin: 0 auto !important;
    padding: 1rem !important;
    width: 98% !important;
    box-sizing: border-box !important;
    overflow-x: hidden !important;
  }

  /* Fix text overflow issues */
  .calculator-inputs-modern .inputs-header p,
  .calculator-results-modern .results-header p,
  .projection-section-modern .projection-header p {
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    white-space: normal !important;
    hyphens: auto !important;
    font-size: 0.9rem !important;
    line-height: 1.5 !important;
  }

  /* Ensure all text elements wrap properly */
  .calculator-inputs-modern *,
  .calculator-results-modern *,
  .projection-section-modern * {
    max-width: 100% !important;
    box-sizing: border-box !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
  }

  /* Input groups optimization */
  .calculator-inputs-modern .input-group {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0.875rem !important;
    margin: 0 0 0.875rem 0 !important;
  }

  /* Input fields optimization */
  .calculator-inputs-modern .modern-input,
  .calculator-inputs-modern select,
  .calculator-inputs-modern input {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0.75rem !important;
    font-size: 0.95rem !important;
    box-sizing: border-box !important;
  }

  /* Label optimization */
  .calculator-inputs-modern .input-label {
    font-size: 0.85rem !important;
    margin-bottom: 0.5rem !important;
    word-wrap: break-word !important;
    line-height: 1.4 !important;
  }

  /* Grid layout - single column for tablet portrait */
  .calculator-inputs-modern .modern-inputs-grid {
    grid-template-columns: 1fr !important;
    gap: 0.875rem !important;
    width: 100% !important;
  }
}

/* Tablet Portrait Large (711px - 767px) */
@media (min-width: 711px) and (max-width: 767px) {
  .calculator-inputs-modern,
  .calculator-results-modern,
  .projection-section-modern {
    max-width: 100% !important;
    margin: 0 auto !important;
    padding: 1.25rem !important;
    width: 96% !important;
    box-sizing: border-box !important;
    overflow-x: hidden !important;
  }

  /* Enhanced text handling for larger tablet portrait */
  .calculator-inputs-modern .inputs-header p,
  .calculator-results-modern .results-header p,
  .projection-section-modern .projection-header p {
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    white-space: normal !important;
    hyphens: auto !important;
    font-size: 0.95rem !important;
    line-height: 1.5 !important;
    max-width: 100% !important;
  }

  /* Ensure all elements wrap properly */
  .calculator-inputs-modern *,
  .calculator-results-modern *,
  .projection-section-modern * {
    max-width: 100% !important;
    box-sizing: border-box !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
  }

  /* Input optimization for larger tablet portrait */
  .calculator-inputs-modern .input-group {
    width: 100% !important;
    max-width: 100% !important;
    padding: 1rem !important;
    margin: 0 0 1rem 0 !important;
  }

  .calculator-inputs-modern .modern-input,
  .calculator-inputs-modern select,
  .calculator-inputs-modern input {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0.875rem !important;
    font-size: 1rem !important;
    box-sizing: border-box !important;
  }

  .calculator-inputs-modern .input-label {
    font-size: 0.875rem !important;
    margin-bottom: 0.5rem !important;
    word-wrap: break-word !important;
    line-height: 1.4 !important;
  }

  /* Grid layout - single column */
  .calculator-inputs-modern .modern-inputs-grid {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
    width: 100% !important;
  }
}

/* Tablet Landscape (768px - 991px) */
@media (min-width: 768px) and (max-width: 991px) {
  .calculator-inputs-modern,
  .calculator-results-modern,
  .projection-section-modern {
    max-width: 100% !important;
    margin: 0 auto !important;
    padding: 1.5rem !important;
    width: 98% !important;
    box-sizing: border-box !important;
    overflow-x: hidden !important;
  }

  /* Ensure text wrapping for landscape tablets */
  .calculator-inputs-modern .inputs-header p,
  .calculator-results-modern .results-header p,
  .projection-section-modern .projection-header p {
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    white-space: normal !important;
    font-size: 1rem !important;
    line-height: 1.5 !important;
    max-width: 100% !important;
  }

  /* Universal element constraints */
  .calculator-inputs-modern *,
  .calculator-results-modern *,
  .projection-section-modern * {
    max-width: 100% !important;
    box-sizing: border-box !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
  }

  /* Input group optimization for landscape */
  .calculator-inputs-modern .input-group {
    width: 100% !important;
    max-width: 100% !important;
    padding: 1rem !important;
    margin: 0 0 1rem 0 !important;
    box-sizing: border-box !important;
  }

  /* Input field optimization */
  .calculator-inputs-modern .modern-input,
  .calculator-inputs-modern select,
  .calculator-inputs-modern input {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0.875rem !important;
    font-size: 1rem !important;
    box-sizing: border-box !important;
    min-height: 44px !important;
  }

  /* Label optimization */
  .calculator-inputs-modern .input-label {
    font-size: 0.875rem !important;
    margin-bottom: 0.5rem !important;
    word-wrap: break-word !important;
    line-height: 1.4 !important;
    max-width: 100% !important;
  }

  /* Grid layout - maintain multi-column but ensure fit */
  .calculator-inputs-modern .modern-inputs-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
    gap: 1rem !important;
    width: 100% !important;
  }

  /* Results grid optimization */
  .calculator-results-modern .results-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
    gap: 1rem !important;
    width: 100% !important;
  }

  /* Result card optimization */
  .calculator-results-modern .result-card {
    padding: 1rem !important;
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
  }

  /* Header sizing for landscape tablets */
  .calculator-inputs-modern .inputs-header h3,
  .calculator-results-modern .results-header h3,
  .projection-section-modern .projection-header h3 {
    font-size: 1.5rem !important;
    line-height: 1.3 !important;
    margin-bottom: 0.75rem !important;
  }
}

/* Large Mobile (481px - 599px) */
@media (min-width: 481px) and (max-width: 599px) {
  .calculator-inputs-modern,
  .calculator-results-modern,
  .projection-section-modern {
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0.75rem !important;
    width: 100% !important;
    box-sizing: border-box !important;
    overflow-x: hidden !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
  }

  /* Fix internal content overflow for large mobile */
  .calculator-inputs-modern *,
  .calculator-results-modern *,
  .projection-section-modern * {
    max-width: 100% !important;
    box-sizing: border-box !important;
  }

  .calculator-inputs-modern .input-group,
  .calculator-results-modern .result-card,
  .projection-section-modern .projection-card {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0.625rem !important;
    margin: 0 0 0.625rem 0 !important;
  }

  .calculator-inputs-modern .modern-input,
  .calculator-inputs-modern select,
  .calculator-inputs-modern input {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0.5rem !important;
    font-size: 0.8rem !important;
    box-sizing: border-box !important;
    min-height: 42px !important;
  }

  .calculator-inputs-modern .input-label {
    font-size: 0.7rem !important;
    margin-bottom: 0.3rem !important;
    word-wrap: break-word !important;
    line-height: 1.3 !important;
  }

  /* Force all text elements to wrap */
  .calculator-inputs-modern .input-label,
  .calculator-inputs-modern .input-info,
  .calculator-inputs-modern .info-badge,
  .calculator-inputs-modern .info-text {
    white-space: normal !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    hyphens: auto !important;
  }

  /* Ensure buttons fit properly */
  .calculator-inputs-modern button,
  .calculator-inputs-modern .btn {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    font-size: 0.8rem !important;
    padding: 0.6rem !important;
  }

  /* Fix grid layouts */
  .calculator-inputs-modern .modern-inputs-grid {
    grid-template-columns: 1fr !important;
    gap: 0.625rem !important;
    width: 100% !important;
  }

  /* Header text sizing */
  .calculator-inputs-modern .inputs-header h3,
  .calculator-results-modern .results-header h3,
  .projection-section-modern .projection-header h3 {
    font-size: 1.1rem !important;
    line-height: 1.3 !important;
  }

  .calculator-inputs-modern .inputs-header p,
  .calculator-results-modern .results-header p,
  .projection-section-modern .projection-header p {
    font-size: 0.8rem !important;
    line-height: 1.4 !important;
  }
}

/* Standard Mobile (361px - 480px) */
@media (min-width: 361px) and (max-width: 480px) {
  .calculator-inputs-modern,
  .calculator-results-modern,
  .projection-section-modern {
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0.5rem !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  /* Fix internal content overflow */
  .calculator-inputs-modern *,
  .calculator-results-modern *,
  .projection-section-modern * {
    max-width: 100% !important;
    box-sizing: border-box !important;
  }

  .calculator-inputs-modern .input-group,
  .calculator-results-modern .result-card,
  .projection-section-modern .projection-card {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0.5rem !important;
    margin: 0 0 0.5rem 0 !important;
  }

  .calculator-inputs-modern .modern-input,
  .calculator-inputs-modern select,
  .calculator-inputs-modern input {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0.4rem !important;
    font-size: 0.75rem !important;
    box-sizing: border-box !important;
  }

  .calculator-inputs-modern .input-label {
    font-size: 0.65rem !important;
    margin-bottom: 0.25rem !important;
    word-wrap: break-word !important;
  }
}

/* Small Mobile (320px - 360px) */
@media (min-width: 320px) and (max-width: 360px) {
  .calculator-inputs-modern,
  .calculator-results-modern,
  .projection-section-modern {
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0.4rem !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  /* Fix internal content overflow for very small screens */
  .calculator-inputs-modern *,
  .calculator-results-modern *,
  .projection-section-modern * {
    max-width: 100% !important;
    box-sizing: border-box !important;
  }

  .calculator-inputs-modern .input-group,
  .calculator-results-modern .result-card,
  .projection-section-modern .projection-card {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0.375rem !important;
    margin: 0 0 0.375rem 0 !important;
  }

  .calculator-inputs-modern .modern-input,
  .calculator-inputs-modern select,
  .calculator-inputs-modern input {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0.3rem !important;
    font-size: 0.7rem !important;
    box-sizing: border-box !important;
    min-height: 32px !important;
  }

  .calculator-inputs-modern .input-label {
    font-size: 0.6rem !important;
    margin-bottom: 0.2rem !important;
    word-wrap: break-word !important;
    line-height: 1.2 !important;
  }
}

/* UNIVERSAL MOBILE OVERFLOW FIX */
@media (max-width: 480px) {
  /* Ensure no horizontal overflow on any mobile device */
  .calculator-inputs-modern,
  .calculator-results-modern,
  .projection-section-modern {
    overflow-x: hidden !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
  }

  /* Force all text elements to wrap */
  .calculator-inputs-modern .input-label,
  .calculator-inputs-modern .input-info,
  .calculator-inputs-modern .info-badge,
  .calculator-inputs-modern .info-text {
    white-space: normal !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    hyphens: auto !important;
  }

  /* Ensure buttons and interactive elements fit */
  .calculator-inputs-modern button,
  .calculator-inputs-modern .btn {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    font-size: 0.75rem !important;
    padding: 0.5rem !important;
  }

  /* Fix grid layouts */
  .calculator-inputs-modern .modern-inputs-grid {
    grid-template-columns: 1fr !important;
    gap: 0.5rem !important;
    width: 100% !important;
  }
}

/* Extra Small Mobile (280px - 319px) */
@media (max-width: 319px) {
  .calculator-inputs-modern,
  .calculator-results-modern,
  .projection-section-modern {
    max-width: 100%;
    margin: 0;
    padding: 0.5rem;
    width: 100%;
    box-sizing: border-box;
  }
}

/* ===== ADDITIONAL RESPONSIVE ADJUSTMENTS ===== */

/* Medium to Large Screens - Optimize spacing */
@media (min-width: 768px) {
  .calculator-layout-modern {
    gap: var(--spacing-xl);
  }

  .calculator-inputs-modern,
  .calculator-results-modern,
  .projection-section-modern {
    border-radius: 1rem;
  }
}

/* Small Screens - Reduce spacing and borders */
@media (max-width: 767px) {
  .calculator-layout-modern {
    gap: var(--spacing-md);
  }

  .calculator-inputs-modern,
  .calculator-results-modern,
  .projection-section-modern {
    border-radius: 0.75rem;
    margin-bottom: var(--spacing-md);
  }
}

/* Mobile Screens - Minimal spacing */
@media (max-width: 480px) {
  .calculator-layout-modern {
    gap: var(--spacing-sm);
  }

  .calculator-inputs-modern,
  .calculator-results-modern,
  .projection-section-modern {
    border-radius: 0.5rem;
    margin-bottom: var(--spacing-sm);
  }
}

/* Very Small Screens - Ultra minimal */
@media (max-width: 360px) {
  .calculator-layout-modern {
    gap: 0.75rem;
  }

  .calculator-inputs-modern,
  .calculator-results-modern,
  .projection-section-modern {
    border-radius: 0.375rem;
    margin-bottom: 0.75rem;
  }
}



.input-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.input-group.editable {
  background: rgba(251, 191, 36, 0.05);
  border: 1px solid rgba(251, 191, 36, 0.2);
  border-radius: 0.75rem;
  padding: var(--spacing-md);
}

.input-group.readonly {
  background: rgba(107, 114, 128, 0.1);
  border: 1px solid rgba(107, 114, 128, 0.2);
  border-radius: 0.75rem;
  padding: var(--spacing-md);
}

.input-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #fbbf24;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--spacing-xs);
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.modern-input {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(229, 231, 235, 0.5);
  border-radius: 0.5rem;
  padding: var(--spacing-sm) var(--spacing-md);
  color: #ffffff;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.2s ease;
  width: 100%;
  box-sizing: border-box;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.modern-input:focus {
  outline: none;
  border-color: #fbbf24;
  box-shadow: 0 0 0 3px rgba(251, 191, 36, 0.1);
}

.modern-input.readonly {
  background: rgba(107, 114, 128, 0.2);
  color: #9ca3af;
  cursor: not-allowed;
}

.input-info {
  margin-top: var(--spacing-xs);
}

.info-badge {
  display: inline-block;
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.info-text {
  color: #9ca3af;
  font-size: 0.75rem;
}

.gold-price-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xs);
}

/* Mobile Responsive for Gold Price Controls */
@media (max-width: 768px) {
  .gold-price-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .live-price-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    width: 100%;
    text-align: center;
  }

  .live-price-display {
    font-size: 0.7rem;
    align-self: center;
  }
}

@media (max-width: 480px) {
  .gold-price-controls {
    gap: var(--spacing-xs);
  }

  .live-price-btn {
    padding: 0.625rem;
    font-size: 0.75rem;
    border-radius: 0.5rem;
  }

  .live-price-display {
    font-size: 0.65rem;
  }

  .error-text {
    font-size: 0.65rem;
    text-align: center;
  }
}

.live-price-btn {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: #ffffff;
  border: none;
  border-radius: 0.375rem;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.live-price-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  transform: translateY(-1px);
}

.live-price-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.live-price-display {
  color: #9ca3af;
  font-size: 0.75rem;
}

.error-text {
  color: #ef4444;
  font-size: 0.75rem;
}

/* Results Section */
.calculator-results-modern {
  background: rgba(17, 24, 39, 0.6);
  border: 1px solid rgba(107, 114, 128, 0.3);
  border-radius: 1rem;
  padding: var(--spacing-xl);
  backdrop-filter: blur(8px);
}

.results-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-md);
}

/* Mobile Responsive for Results */
@media (max-width: 768px) {
  .calculator-results-modern {
    padding: var(--spacing-lg);
    margin: 0 -var(--spacing-xs);
  }

  .results-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }

  .result-card {
    padding: var(--spacing-sm);
    gap: var(--spacing-sm);
  }

  .result-icon {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .calculator-results-modern {
    padding: var(--spacing-sm);
    margin: 0;
  }

  .results-header h3 {
    font-size: 1rem;
    line-height: 1.3;
  }

  .results-header p {
    font-size: 0.75rem;
    line-height: 1.4;
  }

  .result-card {
    padding: 0.75rem;
    gap: 0.5rem;
  }

  .result-icon {
    font-size: 1.25rem;
  }

  .result-label {
    font-size: 0.7rem;
  }

  .result-value {
    font-size: 0.9rem;
  }
}

@media (max-width: 360px) {
  .calculator-results-modern {
    padding: 0.75rem;
    margin: 0 -0.25rem;
  }

  .results-header h3 {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }

  .results-header p {
    font-size: 0.7rem;
    margin-bottom: 1rem;
  }

  .result-card {
    padding: 0.625rem;
    gap: 0.4rem;
  }

  .result-icon {
    font-size: 1.1rem;
  }

  .result-label {
    font-size: 0.65rem;
  }

  .result-value {
    font-size: 0.85rem;
  }

  .results-grid {
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .calculator-results-modern {
    padding: var(--spacing-md);
    margin: 0;
    border-radius: 0.75rem;
  }

  .results-header h3 {
    font-size: 1.125rem;
  }

  .results-header p {
    font-size: 0.8rem;
  }

  .result-card {
    padding: var(--spacing-xs);
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-xs);
  }

  .result-icon {
    font-size: 1.25rem;
  }

  .result-content {
    text-align: center;
  }

  .result-label {
    font-size: 0.75rem;
  }

  .result-value {
    font-size: 1rem;
  }

  .result-unit {
    font-size: 0.7rem;
  }
}

.result-card {
  background: rgba(31, 41, 55, 0.6);
  border: 1px solid rgba(107, 114, 128, 0.3);
  border-radius: 0.75rem;
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: all 0.3s ease;
}

.result-card:hover {
  border-color: rgba(251, 191, 36, 0.5);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}

.result-card.highlight {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(251, 191, 36, 0.05));
  border-color: rgba(251, 191, 36, 0.4);
}

.result-card.border-gold {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.15), rgba(251, 191, 36, 0.08));
  border: 2px solid rgba(251, 191, 36, 0.6);
  box-shadow: 0 0 20px rgba(251, 191, 36, 0.2);
}

.result-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  flex: 1;
}

.result-metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xs) 0;
}

.result-metric-label {
  font-size: 0.75rem;
  color: #9ca3af;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.result-metric-value {
  font-size: 0.875rem;
  color: #ffffff;
  font-weight: 600;
  letter-spacing: 0.05em;
}

.result-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.result-content {
  flex: 1;
}

.result-label {
  font-size: 0.75rem;
  color: #9ca3af;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin-bottom: var(--spacing-xs);
}

.result-value {
  font-size: 1.125rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: var(--spacing-xs);
  line-height: 1.2;
}

.result-unit {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}

/* Projection Section */
.projection-section-modern {
  background: rgba(17, 24, 39, 0.6);
  border: 1px solid rgba(107, 114, 128, 0.3);
  border-radius: 1rem;
  padding: var(--spacing-xl);
  backdrop-filter: blur(8px);
}

.projection-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .modern-inputs-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .results-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }

  .result-card {
    padding: var(--spacing-sm);
  }

  .result-value {
    font-size: 1rem;
  }

  .calculator-inputs-modern,
  .calculator-results-modern,
  .projection-section-modern {
    padding: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .projection-section-modern {
    padding: var(--spacing-sm);
    margin: 0;
  }

  .projection-header h3 {
    font-size: 1rem;
    line-height: 1.3;
  }

  .projection-header p {
    font-size: 0.75rem;
    line-height: 1.4;
  }
}

@media (max-width: 360px) {
  .projection-section-modern {
    padding: 0.75rem;
    margin: 0 -0.25rem;
  }

  .projection-header h3 {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }

  .projection-header p {
    font-size: 0.7rem;
    margin-bottom: 1rem;
  }
}

/* ===== PROJECTION TABS STYLES ===== */
.projection-tabs-container {
  width: 100%;
}

.tab-navigation {
  margin-bottom: var(--spacing-lg);
  border-bottom: 1px solid rgba(107, 114, 128, 0.3);
}

.tab-scroll-container {
  display: flex;
  gap: 2px;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  padding-bottom: 2px;
}

.tab-scroll-container::-webkit-scrollbar {
  display: none;
}

.tab-button {
  flex-shrink: 0;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.875rem;
  font-weight: 600;
  border: none;
  border-radius: 0.5rem 0.5rem 0 0;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  position: relative;
}

.tab-active {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.2), rgba(251, 191, 36, 0.1));
  color: #fbbf24;
  border-bottom: 2px solid #fbbf24;
}

.tab-inactive {
  background: rgba(255, 255, 255, 0.8);
  color: #6b7280;
  border-bottom: 2px solid transparent;
}

.tab-inactive:hover {
  background: rgba(229, 231, 235, 0.8);
  color: #374151;
}

.tab-content {
  padding: var(--spacing-lg);
  background: rgba(255, 255, 255, 0.9);
  border-radius: 0.75rem;
  backdrop-filter: blur(8px);
}

.year-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.projection-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.metric-card {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(229, 231, 235, 0.5);
  border-radius: 0.5rem;
  padding: var(--spacing-md);
  text-align: center;
  transition: all 0.3s ease;
}

.metric-card:hover {
  border-color: rgba(251, 191, 36, 0.5);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}

.metric-card.highlight {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(251, 191, 36, 0.05));
  border-color: rgba(251, 191, 36, 0.4);
}

.metric-label {
  font-size: 0.75rem;
  color: #9ca3af;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--spacing-xs);
}

.metric-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: var(--spacing-xs);
  line-height: 1.2;
}

.metric-unit {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .projection-metrics-grid {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-sm);
  }

  .metric-card {
    padding: var(--spacing-sm);
  }

  .metric-value {
    font-size: 1rem;
  }

  .tab-button {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .projection-metrics-grid {
    grid-template-columns: 1fr;
  }
}

/* ===== PROJECT ASSUMPTIONS SECTION STYLES ===== */
.project-assumptions-section {
  margin-top: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: rgba(17, 24, 39, 0.4);
  border: 1px solid rgba(107, 114, 128, 0.2);
  border-radius: 0.75rem;
  backdrop-filter: blur(4px);
}

.assumptions-header {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.assumptions-header h4 {
  color: #fbbf24;
  font-weight: 600;
  font-size: 1.125rem;
  margin-bottom: var(--spacing-xs);
}

.assumptions-header p {
  color: #9ca3af;
  font-size: 0.75rem;
  font-weight: 400;
}

.assumptions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.assumption-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  background: rgba(31, 41, 55, 0.3);
  border: 1px solid rgba(107, 114, 128, 0.2);
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.assumption-item:hover {
  border-color: rgba(251, 191, 36, 0.3);
  background: rgba(31, 41, 55, 0.5);
}

.assumption-label {
  font-size: 0.75rem;
  color: #d1d5db;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.assumption-value {
  font-size: 0.875rem;
  color: #ffffff;
  font-weight: 600;
  text-align: right;
}

/* Mobile Responsive for Project Assumptions */
@media (max-width: 768px) {
  .assumptions-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }

  .assumption-item {
    padding: var(--spacing-xs) var(--spacing-sm);
  }

  .assumption-label {
    font-size: 0.7rem;
  }

  .assumption-value {
    font-size: 0.8rem;
  }

  .project-assumptions-section {
    padding: var(--spacing-md);
    margin-top: var(--spacing-lg);
  }
}

@media (max-width: 480px) {
  .assumption-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .assumption-value {
    text-align: left;
    color: #fbbf24;
  }
}

/* ===== GALLERY SECTION STYLES ===== */
.gallery-container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
}

/* Gallery Grid Enhancements */
.gallery-container .space-y-6 > * + * {
  margin-top: var(--spacing-xl);
}

/* Gallery Image Styling - exclude lightbox images */
.gallery-container img:not(.fixed img):not(.fixed * img) {
  max-width: 350px;
  width: 100%;
  height: auto;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

/* Lightbox images should never be constrained by gallery rules */
.fixed img,
.fixed * img {
  max-width: calc(100vw - 100px) !important;
  max-height: calc(100vh - 200px) !important;
  width: auto !important;
  height: auto !important;
  object-fit: contain !important;
}

.gallery-container img:hover {
  transform: scale(1.02);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(251, 191, 36, 0.1);
}

/* Gallery Category Buttons */
.gallery-container button {
  transition: all 0.3s ease;
  border-radius: 0.5rem;
}

.gallery-container button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* ===== ENHANCED GALLERY LIGHTBOX STYLES ===== */

/* Lightbox images should not be constrained by gallery container rules */
.fixed.inset-0.z-50 img {
  max-width: none !important;
  width: auto !important;
  height: auto !important;
  min-width: unset !important;
  min-height: unset !important;
}

/* Lightbox backdrop with enhanced blur */
.gallery-lightbox-backdrop {
  backdrop-filter: blur(12px);
  background: rgba(0, 0, 0, 0.95);
  transition: all 0.3s ease-in-out;
}

/* Enhanced lightbox controls */
.gallery-lightbox-control {
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(245, 158, 11, 0.3);
  backdrop-filter: blur(8px);
  transition: all 0.2s ease;
}

.gallery-lightbox-control:hover {
  background: rgba(0, 0, 0, 0.8);
  border-color: rgba(245, 158, 11, 0.6);
  transform: scale(1.05);
}

/* Touch-friendly sizing for mobile */
@media (max-width: 768px) {
  .gallery-lightbox-control {
    min-height: 48px;
    min-width: 48px;
    padding: 12px;
  }

  .gallery-lightbox-nav {
    padding: 16px;
  }
}

/* Enhanced gallery card dark theme */
.gallery-image-card {
  background: #1f2937;
  border: 1px solid #374151;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.gallery-image-card:hover {
  border-color: rgba(245, 158, 11, 0.4);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4), 0 0 20px rgba(245, 158, 11, 0.15);
}

/* Featured badge styling */
.gallery-featured-badge {
  background: rgba(245, 158, 11, 0.2);
  border: 1px solid rgba(245, 158, 11, 0.3);
  color: #fbbf24;
  backdrop-filter: blur(4px);
}

/* Category badge styling */
.gallery-category-badge {
  background: rgba(245, 158, 11, 0.2);
  border: 1px solid rgba(245, 158, 11, 0.3);
  color: #fbbf24;
  backdrop-filter: blur(4px);
}

/* Responsive image scaling */
@media (max-width: 640px) {
  .gallery-container img {
    max-width: 100%;
  }
}

/* Smooth transitions for all gallery elements */
.gallery-container * {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Lightbox specific image styling - override all gallery constraints */
.fixed.inset-0.z-50 img,
.fixed.inset-0.z-50 .relative img {
  max-width: calc(100vw - 100px) !important;
  max-height: calc(100vh - 200px) !important;
  width: auto !important;
  height: auto !important;
  object-fit: contain !important;
  min-width: unset !important;
  min-height: unset !important;
}

/* Specific lightbox image class */
.lightbox-image {
  max-width: calc(100vw - 100px) !important;
  max-height: calc(100vh - 200px) !important;
  width: auto !important;
  height: auto !important;
  object-fit: contain !important;
  display: block !important;
}

/* Ensure lightbox images can scale properly */
@media (min-width: 768px) {
  .lightbox-image {
    max-width: calc(100vw - 200px) !important;
    max-height: calc(100vh - 300px) !important;
  }
}

/* Site Values Tabs - Modern Design */
.site-values-tabs {
  background: rgba(15, 23, 42, 0.95);
  border-radius: 20px;
  padding: var(--spacing-xl);
  margin: var(--spacing-lg) 0;
  border: 1px solid rgba(51, 65, 85, 0.3);
  backdrop-filter: blur(10px);
}

.site-values-tabs h3 {
  color: var(--gold);
  font-size: clamp(1.75rem, 3vw, 2.25rem);
  font-weight: 700;
  text-align: center;
  margin-bottom: var(--spacing-xs);
  letter-spacing: -0.02em;
}

.site-values-tabs .subtitle {
  color: #94a3b8;
  font-size: 1rem;
  text-align: center;
  margin-bottom: var(--spacing-xl);
  font-weight: 400;
}

.tabs-container {
  margin-bottom: var(--spacing-xl);
}

.tabs-nav {
  display: flex;
  gap: 4px;
  background: rgba(30, 41, 59, 0.6);
  padding: 6px;
  border-radius: 16px;
  border: 1px solid rgba(51, 65, 85, 0.4);
  flex-wrap: wrap;
  justify-content: center;
  max-width: 600px;
  margin: 0 auto;
}

.tab-button {
  background: transparent;
  border: none;
  color: #94a3b8;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  letter-spacing: 0.025em;
  flex: 1;
  min-width: 85px;
  text-align: center;
  white-space: nowrap;
}

.tab-button:hover {
  background: rgba(51, 65, 85, 0.5);
  color: #e2e8f0;
}

.tab-button.active {
  background: linear-gradient(135deg, var(--gold), #d97706);
  color: #1e293b;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(251, 191, 36, 0.3);
}

.tab-content {
  margin-top: var(--spacing-xl);
}

/* Modern Site Values Grid */
.site-values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.site-value-card {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.9), rgba(15, 23, 42, 0.95));
  border: 1px solid rgba(51, 65, 85, 0.4);
  border-radius: 16px;
  padding: var(--spacing-lg);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.site-value-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--gold), #d97706);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.site-value-card:hover {
  border-color: rgba(251, 191, 36, 0.5);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transform: translateY(-4px);
}

.site-value-card:hover::before {
  opacity: 1;
}

.site-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.site-title {
  color: #ffffff;
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  letter-spacing: -0.01em;
}

.site-badge {
  background: rgba(251, 191, 36, 0.15);
  color: var(--gold);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  letter-spacing: 0.05em;
  border: 1px solid rgba(251, 191, 36, 0.3);
}

.site-value {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.value-label {
  color: #94a3b8;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.value-amount {
  color: var(--gold);
  font-size: 1.5rem;
  font-weight: 700;
  letter-spacing: -0.02em;
}

/* Portfolio Summary */
.portfolio-summary {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(251, 191, 36, 0.05));
  border: 2px solid rgba(251, 191, 36, 0.3);
  border-radius: 20px;
  padding: var(--spacing-xl);
  position: relative;
  overflow: hidden;
}

.portfolio-summary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--gold), #d97706, var(--gold));
}

.summary-header {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.summary-title {
  color: var(--gold);
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 var(--spacing-xs) 0;
  letter-spacing: -0.02em;
}

.summary-subtitle {
  color: #94a3b8;
  font-size: 0.875rem;
  font-weight: 500;
}

.summary-metrics {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-xl);
  flex-wrap: wrap;
}

.summary-metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.summary-metric.primary .metric-value {
  color: var(--gold);
  font-size: 2rem;
  font-weight: 800;
}

.summary-metric.primary .metric-label {
  color: var(--gold);
  font-weight: 600;
}

.metric-value {
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 700;
  letter-spacing: -0.02em;
}

.metric-label {
  color: #94a3b8;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  text-align: center;
}

.summary-divider {
  width: 1px;
  height: 60px;
  background: linear-gradient(to bottom, transparent, rgba(251, 191, 36, 0.3), transparent);
}

/* Resource Estimates - Modern Design */
.resource-estimates-modern {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.9));
  border-radius: 20px;
  padding: var(--spacing-xl);
  margin: var(--spacing-lg) 0;
  border: 1px solid rgba(51, 65, 85, 0.3);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.resource-estimates-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--gold), #d97706, var(--gold));
}

.resource-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.resource-title {
  color: var(--gold);
  font-size: clamp(1.5rem, 3vw, 2rem);
  font-weight: 700;
  margin: 0 0 var(--spacing-xs) 0;
  letter-spacing: -0.02em;
}

.resource-subtitle {
  color: #94a3b8;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.resource-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: var(--spacing-lg);
}

.resource-card {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8), rgba(15, 23, 42, 0.9));
  border: 1px solid rgba(51, 65, 85, 0.4);
  border-radius: 16px;
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  position: relative;
  overflow: hidden;
}

.resource-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, rgba(51, 65, 85, 0.5), rgba(71, 85, 105, 0.5));
  transition: all 0.3s ease;
}

.resource-card:hover {
  border-color: rgba(251, 191, 36, 0.4);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transform: translateY(-4px);
}

.resource-card:hover::before {
  background: linear-gradient(90deg, var(--gold), #d97706);
}

.resource-card.highlight {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.15), rgba(251, 191, 36, 0.08));
  border: 2px solid rgba(251, 191, 36, 0.4);
  box-shadow: 0 4px 20px rgba(251, 191, 36, 0.2);
}

.resource-card.highlight::before {
  background: linear-gradient(90deg, var(--gold), #d97706, var(--gold));
  height: 3px;
}

.resource-icon {
  font-size: 2rem;
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(51, 65, 85, 0.3);
  border-radius: 12px;
  border: 1px solid rgba(71, 85, 105, 0.4);
}

.resource-card.highlight .resource-icon {
  background: rgba(251, 191, 36, 0.2);
  border-color: rgba(251, 191, 36, 0.4);
}

.resource-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.resource-label {
  color: #94a3b8;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.resource-value {
  color: #ffffff;
  font-size: 1.25rem;
  font-weight: 700;
  letter-spacing: -0.02em;
}

.resource-value.gold {
  color: var(--gold);
  font-size: 1.5rem;
  font-weight: 800;
}

/* Old large commission structure CSS removed - using compact design only */

/* Hide any remaining old commission sections */
.commission-header,
.commission-structure:not(.commission-overview-compact):not(.commission-v2-fixed) {
  display: none !important;
}

/* Ensure old commission cards are hidden */
.commission-card.direct-sales,
.commission-card.example-card,
.commission-card.total-pool {
  display: none !important;
}

/* Comprehensive Phase Breakdown Styles */
.comprehensive-phase-breakdown {
  padding: var(--spacing-xl) 0;
}

.comprehensive-phase-breakdown .timeline-view {
  max-width: 4xl;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.comprehensive-phase-breakdown .grid-view {
  max-width: 6xl;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* Timeline specific styles */
.comprehensive-phase-breakdown .timeline-view .relative {
  padding-left: var(--spacing-lg);
}

/* Phase card hover effects */
.comprehensive-phase-breakdown .bg-gray-900\/60:hover {
  background: rgba(17, 24, 39, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .comprehensive-phase-breakdown .timeline-view .ml-16 {
    margin-left: var(--spacing-lg);
  }

  .comprehensive-phase-breakdown .timeline-view .absolute.left-6 {
    left: var(--spacing-sm);
  }

  .comprehensive-phase-breakdown .timeline-view .absolute.left-8 {
    left: var(--spacing-sm);
  }

  .comprehensive-phase-breakdown .grid-view {
    grid-template-columns: 1fr;
  }
}

.reward-amount {
  font-size: 1.5rem;
  font-weight: 800;
  color: #ffffff;
  margin-bottom: var(--spacing-xs);
}

.reward-type {
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.example-reward.primary .reward-type {
  color: #10b981;
}

.example-reward.secondary .reward-type {
  color: #3b82f6;
}

.example-plus {
  font-size: 2rem;
  font-weight: 800;
  color: var(--gold);
}

.pool-details {
  text-align: center;
}

.pool-amount {
  font-size: 3rem;
  font-weight: 900;
  color: var(--gold);
  margin-bottom: var(--spacing-sm);
  letter-spacing: -0.02em;
}

.pool-description {
  color: #94a3b8;
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: var(--spacing-lg);
}

.pool-visual {
  margin-top: var(--spacing-lg);
}

.pool-bar {
  width: 100%;
  height: 8px;
  background: rgba(51, 65, 85, 0.5);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.pool-fill {
  width: 45%;
  height: 100%;
  background: linear-gradient(90deg, var(--gold), #d97706);
  border-radius: 4px;
  position: relative;
  animation: poolFillAnimation 2s ease-out;
}

@keyframes poolFillAnimation {
  from {
    width: 0%;
  }
  to {
    width: 45%;
  }
}

/* Responsive Design for Site Values */
@media (max-width: 768px) {
  .site-values-grid {
    grid-template-columns: 1fr;
  }

  .summary-metrics {
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  .summary-divider {
    width: 60px;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(251, 191, 36, 0.3), transparent);
  }

  .tabs-nav {
    grid-template-columns: repeat(3, 1fr);
    gap: 4px;
  }

  .resource-grid {
    grid-template-columns: 1fr;
  }

  .resource-card {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }

  .resource-icon {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }

  .commission-structure {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .commission-example {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .example-plus {
    transform: rotate(90deg);
  }

  .pool-amount {
    font-size: 2.5rem;
  }
}

/* Responsive Design for Charity Section */
@media (max-width: 1024px) {
  .projects-main-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }

  .projects-image-section {
    position: static;
    order: -1;
  }

  .humanitarian-image {
    max-height: 400px;
    object-fit: cover;
  }
}

@media (max-width: 768px) {
  .charity-commitment {
    margin: var(--spacing-lg) var(--spacing-md);
    padding: var(--spacing-lg);
  }

  .commitment-amount {
    font-size: 2rem;
  }

  .commitment-text {
    font-size: 1rem;
  }

  .charity-subtitle {
    font-size: 1.125rem;
    padding: 0 var(--spacing-md);
  }

  .projects-main-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .projects-cards-section {
    gap: var(--spacing-lg);
  }

  .project-card {
    padding: var(--spacing-lg);
  }

  .project-card-header {
    flex-direction: row;
    gap: var(--spacing-md);
  }

  .project-icon {
    width: 50px;
    height: 50px;
  }

  .project-title {
    font-size: 1.5rem;
  }

  .intro-text {
    font-size: 1.125rem;
    padding: 0 var(--spacing-md);
  }

  .footer-title {
    font-size: 1.5rem;
  }

  .footer-text {
    font-size: 1rem;
    padding: 0 var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .charity-title {
    font-size: 2rem;
  }

  .projects-main-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .project-card {
    padding: var(--spacing-md);
  }

  .projects-cards-section {
    gap: var(--spacing-md);
  }

  .project-feature {
    padding: var(--spacing-sm);
  }
}

/* Comprehensive Phase Display Styles */
.comprehensive-phase-display {
  grid-column: 1 / -1;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.9));
  border: 1px solid rgba(51, 65, 85, 0.4);
  border-radius: 20px;
  padding: var(--spacing-xl);
  backdrop-filter: blur(10px);
  min-height: 500px; /* Ensure component is always visible */
  width: 100%;
}

.phase-tabs-container {
  margin-bottom: var(--spacing-xl);
}

.phase-tabs-header {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.phase-tabs-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.phase-tabs {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-sm);
  max-width: 100%;
  overflow-x: auto;
  padding: var(--spacing-sm);
}

.phase-tab {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-md);
  border: 2px solid rgba(75, 85, 99, 0.3);
  border-radius: 12px;
  background: rgba(31, 41, 55, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 100px;
  justify-content: center;
}

.phase-tab:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.phase-tab.active {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

.phase-tab.presale {
  border-color: var(--gold) !important;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 193, 7, 0.05));
}

.phase-tab-number {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--gold);
  margin-bottom: 4px;
}

.phase-tab-price {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.phase-tab-status {
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.phase-tabs-more {
  text-align: center;
  padding: var(--spacing-sm);
}

.phase-details-card {
  background: linear-gradient(135deg, rgba(17, 24, 39, 0.9), rgba(30, 41, 59, 0.8));
  border: 2px solid rgba(251, 191, 36, 0.2);
  border-radius: 20px;
  padding: var(--spacing-xl);
  position: relative;
  overflow: hidden;
  animation: cardEntrance 0.6s ease-out;
}

.phase-details-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(251, 191, 36, 0.05), transparent, rgba(251, 191, 36, 0.05));
  border-radius: 20px;
  z-index: -1;
  animation: cardGlow 4s ease-in-out infinite;
}

@keyframes cardEntrance {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes cardGlow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

.phase-details-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.phase-title-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.phase-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.presale-badge {
  background: linear-gradient(135deg, var(--gold), #fbbf24);
  color: #ffffff;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.phase-status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.phase-pricing {
  text-align: right;
}

.price-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--gold);
  display: block;
}

.price-label {
  font-size: 0.875rem;
  color: var(--gold);
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.phase-details-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.phase-availability {
  background: rgba(55, 65, 81, 0.3);
  border-radius: 12px;
  padding: var(--spacing-lg);
}

.availability-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.stat {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--gold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(229, 231, 235, 0.5);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #10b981);
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* Capital Information Styles */
.phase-capital-info {
  background: rgba(55, 65, 81, 0.3);
  border-radius: 12px;
  padding: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.capital-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.capital-title::before {
  content: "💰";
  font-size: 1.25rem;
}

.capital-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.capital-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: var(--spacing-md);
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
  border: 1px solid rgba(75, 85, 99, 0.3);
}

.capital-label {
  font-size: 0.875rem;
  color: var(--gold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--spacing-xs);
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.capital-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
}

.capital-value.raised {
  color: #10b981;
}

.capital-value.remaining {
  color: #f59e0b;
}

.commission-structure {
  background: rgba(55, 65, 81, 0.3);
  border-radius: 12px;
  padding: var(--spacing-lg);
  position: relative;
}

.commission-structure.highlighted {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 193, 7, 0.05));
  border: 1px solid rgba(255, 215, 0, 0.3);
}

.exclusive-banner {
  background: linear-gradient(135deg, var(--gold), #fbbf24);
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
  margin-bottom: var(--spacing-md);
  animation: pulse 2s infinite;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

.commission-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.commission-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.commission-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
}

.commission-icon {
  font-size: 1.25rem;
}

.commission-label {
  flex: 1;
  color: var(--gold);
  font-size: 0.875rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.commission-value {
  font-weight: 700;
  color: var(--gold);
  font-size: 1rem;
}

.commission-value.disabled {
  color: #94a3b8;
  text-decoration: line-through;
  opacity: 0.7;
}

.commission-description {
  color: #fbbf24;
  font-size: 0.875rem;
  font-style: italic;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.purchase-section {
  text-align: center;
}

.purchase-btn {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  border-radius: 16px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.purchase-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.purchase-btn.presale {
  background: linear-gradient(135deg, var(--gold), #fbbf24, var(--gold));
  background-size: 200% 200%;
  color: #000000;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  animation: presalePulse 2s ease-in-out infinite;
}

.purchase-btn.presale:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 12px 35px rgba(255, 215, 0, 0.5);
}

.purchase-btn.presale:hover::before {
  left: 100%;
}

.purchase-btn.regular {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8, #3b82f6);
  background-size: 200% 200%;
  color: white;
  animation: regularGlow 3s ease-in-out infinite;
}

.purchase-btn.regular:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 12px 35px rgba(59, 130, 246, 0.5);
}

.purchase-btn.regular:hover::before {
  left: 100%;
}

@keyframes presalePulse {
  0%, 100% {
    background-position: 0% 50%;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
  }
  50% {
    background-position: 100% 50%;
    box-shadow: 0 6px 25px rgba(255, 215, 0, 0.5);
  }
}

@keyframes regularGlow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Expansion Overview Compact Styles */
.expansion-overview-compact {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.9), rgba(30, 41, 59, 0.8));
  border: 2px solid rgba(251, 191, 36, 0.2);
  border-radius: 20px;
  padding: var(--spacing-xl);
  margin-top: var(--spacing-xl);
  position: relative;
  overflow: hidden;
}

.expansion-overview-compact::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(251, 191, 36, 0.03), transparent, rgba(251, 191, 36, 0.03));
  border-radius: 20px;
  z-index: -1;
  animation: expansionGlow 5s ease-in-out infinite;
}

.expansion-compact-header {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.expansion-compact-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.expansion-compact-title h4 {
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.expansion-icon-small {
  font-size: 1.5rem;
  animation: bounce 2s ease-in-out infinite;
}

.expansion-compact-subtitle {
  color: #d1d5db;
  font-size: 1rem;
}

.expansion-compact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.expansion-compact-card {
  background: linear-gradient(135deg, rgba(31, 41, 55, 0.8), rgba(51, 65, 85, 0.6));
  border: 1px solid rgba(251, 191, 36, 0.2);
  border-radius: 16px;
  padding: var(--spacing-lg);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.expansion-compact-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(251, 191, 36, 0.1), transparent);
  transition: left 0.5s ease;
}

.expansion-compact-card:hover {
  transform: translateY(-5px);
  border-color: rgba(251, 191, 36, 0.4);
  box-shadow: 0 10px 30px rgba(251, 191, 36, 0.2);
}

.expansion-compact-card:hover::before {
  left: 100%;
}

@keyframes expansionGlow {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 0.8; }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

/* Expansion CTA Styles */
.expansion-cta-section {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(245, 158, 11, 0.05));
  border: 1px solid rgba(251, 191, 36, 0.3);
  border-radius: 16px;
  padding: var(--spacing-lg);
  text-align: center;
}

.expansion-cta-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #fbbf24;
  margin-bottom: var(--spacing-sm);
}

.expansion-cta-subtitle {
  color: #d1d5db;
  margin-bottom: var(--spacing-lg);
}

.expansion-cta-buttons {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
}

.expansion-cta-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.expansion-cta-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.expansion-cta-btn.primary {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: #000000;
}

.expansion-cta-btn.secondary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.expansion-cta-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.expansion-cta-btn:hover::before {
  left: 100%;
}

.cta-icon {
  font-size: 1.1rem;
}

.purchase-btn:disabled {
  background: #374151;
  color: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.purchase-note {
  margin-top: var(--spacing-sm);
  color: var(--gold);
  font-size: 0.875rem;
  font-weight: 500;
}

/* Commission Overview Section - Compact Design */
.commission-overview-compact,
.commission-v2-fixed {
  margin-top: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.8), rgba(30, 41, 59, 0.6));
  border: 1px solid rgba(51, 65, 85, 0.4);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  /* Force browser to recognize this as new CSS */
  position: relative;
  z-index: 1;
}

.commission-compact-header {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.commission-compact-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xs);
}

.commission-compact-title h4 {
  color: var(--gold);
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
  letter-spacing: -0.01em;
}

.commission-icon-small {
  font-size: 1.5rem;
}

.commission-compact-subtitle {
  color: #fbbf24;
  font-size: 0.875rem;
  font-weight: 500;
  margin: 0;
  opacity: 0.95;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.commission-compact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.commission-compact-card {
  background: rgba(31, 41, 55, 0.6);
  border: 1px solid rgba(75, 85, 99, 0.4);
  border-radius: 12px;
  padding: var(--spacing-md);
  position: relative;
  transition: all 0.3s ease;
}

.commission-compact-card:hover {
  transform: translateY(-2px);
  border-color: rgba(251, 191, 36, 0.4);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.commission-compact-card.current-phase {
  border-color: rgba(255, 215, 0, 0.5);
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 193, 7, 0.05));
}

.commission-compact-card.future-phases {
  border-color: rgba(59, 130, 246, 0.4);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.08), rgba(29, 78, 216, 0.04));
}

.commission-compact-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--spacing-sm);
}

.commission-compact-badge.presale {
  background: linear-gradient(135deg, var(--gold), #fbbf24);
  color: #ffffff;
  animation: pulse 2s infinite;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.commission-compact-badge.standard {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.commission-compact-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.commission-compact-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
}

.commission-compact-item.disabled {
  opacity: 0.5;
}

.commission-compact-label {
  color: var(--gold);
  font-size: 0.875rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.commission-compact-value {
  font-weight: 700;
  font-size: 1rem;
}

.commission-compact-value.primary {
  color: #10b981;
}

.commission-compact-value.secondary {
  color: #3b82f6;
}

.commission-compact-value.disabled {
  color: #94a3b8;
  text-decoration: line-through;
  opacity: 0.7;
}

.commission-compact-highlight {
  color: var(--gold);
  font-size: 0.75rem;
  font-weight: 600;
  text-align: center;
  padding: var(--spacing-xs);
  background: rgba(255, 215, 0, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(255, 215, 0, 0.2);
}

.commission-compact-note {
  color: #fbbf24;
  font-size: 0.75rem;
  font-weight: 500;
  text-align: center;
  padding: var(--spacing-xs);
  background: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Call to Action Section - Enhanced Prominence */
.commission-cta-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 193, 7, 0.1));
  border-radius: 16px;
  border: 2px solid rgba(255, 215, 0, 0.4);
  box-shadow: 0 8px 32px rgba(255, 215, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.commission-cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--gold), #fbbf24, var(--gold));
  animation: pulse 2s infinite;
}

.commission-cta-content {
  flex: 1;
}

.commission-cta-title {
  color: #ffffff !important;
  font-size: 1.25rem;
  font-weight: 800;
  margin: 0 0 var(--spacing-xs) 0;
  letter-spacing: -0.01em;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.9), 0 1px 3px rgba(0, 0, 0, 0.8);
  /* Remove gradient text effect */
  background: none;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: unset;
  background-clip: unset;
}

.commission-cta-subtitle {
  color: #ffffff !important;
  font-size: 1rem;
  font-weight: 700;
  margin: 0;
  line-height: 1.4;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.9), 0 1px 3px rgba(0, 0, 0, 0.8);
  opacity: 1 !important;
  background: rgba(0, 0, 0, 0.3);
  padding: 4px 8px;
  border-radius: 4px;
}

/* Additional specificity for the subtitle text */
.commission-cta-section .commission-cta-content .commission-cta-subtitle,
.commission-cta-section p.commission-cta-subtitle {
  color: #ffffff !important;
  font-weight: 700 !important;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.9), 0 1px 3px rgba(0, 0, 0, 0.8) !important;
}

.commission-cta-buttons {
  display: flex;
  gap: var(--spacing-sm);
  flex-shrink: 0;
}

.commission-cta-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: 12px;
  font-size: 0.95rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  min-width: 140px;
  justify-content: center;
}

.commission-cta-btn.primary {
  background: linear-gradient(135deg, var(--gold), #fbbf24);
  color: #ffffff;
  font-weight: 800;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
  border: 2px solid rgba(255, 215, 0, 0.5);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
}

.commission-cta-btn.primary:hover {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(251, 191, 36, 0.4);
  border-color: rgba(255, 215, 0, 0.8);
}

.commission-cta-btn.secondary {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(29, 78, 216, 0.1));
  color: #ffffff;
  border: 2px solid rgba(59, 130, 246, 0.5);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
}

.commission-cta-btn.secondary:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(29, 78, 216, 0.2));
  border-color: rgba(59, 130, 246, 0.8);
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(59, 130, 246, 0.3);
}

.cta-icon {
  font-size: 1.1rem;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

/* Add pulsing animation to primary CTA */
.commission-cta-btn.primary {
  animation: ctaPulse 3s infinite;
}

@keyframes ctaPulse {
  0%, 100% {
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
  }
  50% {
    box-shadow: 0 4px 25px rgba(255, 215, 0, 0.5);
  }
}

.commission-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.commission-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--gold);
  margin-bottom: var(--spacing-sm);
  background: linear-gradient(135deg, var(--gold), #fbbf24);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.commission-subtitle {
  font-size: 1.125rem;
  color: #fbbf24;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Old commission card styles removed - using compact design only */

/* Duplicate commission disabled styles removed */

/* Old commission note styles removed - using compact design only */

/* Responsive adjustments for phase display */
@media (max-width: 768px) {
  .phase-tabs {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }

  .phase-details-header {
    flex-direction: column;
    align-items: stretch;
  }

  .phase-pricing {
    text-align: left;
  }

  .availability-stats {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }

  .commission-structure {
    grid-template-columns: 1fr;
  }

  .capital-stats {
    grid-template-columns: 1fr;
  }

  .capital-stat {
    padding: var(--spacing-sm);
  }

  /* Compact Commission Responsive */
  .commission-compact-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }

  .commission-cta-section {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-lg);
    text-align: center;
    padding: var(--spacing-md);
  }

  .commission-cta-buttons {
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
  }

  .commission-cta-btn {
    flex: 1;
    min-width: 140px;
    padding: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .commission-overview-compact {
    padding: var(--spacing-md);
  }

  .commission-compact-card {
    padding: var(--spacing-sm);
  }

  .commission-cta-buttons {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .commission-cta-btn {
    padding: var(--spacing-sm);
    font-size: 0.8rem;
  }

  .commission-compact-title h4 {
    font-size: 1.125rem;
  }

  .commission-cta-title {
    font-size: 1rem;
  }
}

/* Gold Diggers Club Enhanced Styles */
.gold-diggers-club {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
  border-radius: 20px;
  border: 1px solid rgba(229, 231, 235, 0.5);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Hero Section */
.competition-hero {
  text-align: center;
  margin-bottom: 3rem;
  padding: 3rem 2rem;
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(245, 158, 11, 0.05));
  border-radius: 16px;
  border: 1px solid rgba(251, 191, 36, 0.3);
  position: relative;
  overflow: hidden;
}

.competition-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, rgba(251, 191, 36, 0.1) 0%, transparent 70%);
  pointer-events: none;
}

.hero-content {
  position: relative;
  z-index: 1;
}

/* Enhanced Hero Text Styling for Better Readability */
.hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 900;
  margin-bottom: 1.5rem;
  color: #1f2937;
  text-shadow: none;
  line-height: 1.1;
  letter-spacing: -0.02em;
}

.hero-title.gold-text {
  background: linear-gradient(135deg, #fbbf24, #f59e0b, #d97706);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: shimmer 3s ease-in-out infinite;
  text-shadow: none;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.8));
}

@keyframes shimmer {
  0%, 100% { filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.8)) brightness(1); }
  50% { filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.8)) brightness(1.3); }
}

.hero-subtitle {
  font-size: clamp(1.25rem, 3vw, 1.75rem);
  color: #4b5563;
  margin-bottom: 2rem;
  font-weight: 600;
  text-shadow: none;
  line-height: 1.4;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.hero-description {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2.5rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.hero-description p {
  font-size: 1.1rem;
  color: #6b7280;
  line-height: 1.6;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  border-left: 4px solid #fbbf24;
}

.hero-cta {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.join-club-btn {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: #000;
  font-size: 1.3rem;
  font-weight: 800;
  padding: 1.5rem 3rem;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 8px 25px rgba(251, 191, 36, 0.4);
  position: relative;
  overflow: hidden;
}

.join-club-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.join-club-btn:hover::before {
  left: 100%;
}

.join-club-btn:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 15px 35px rgba(251, 191, 36, 0.6);
}

.cta-note {
  font-size: 1rem;
  color: #9ca3af;
  font-style: italic;
}

/* Competition Stats Bar */
.competition-stats-bar {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
  padding: 2rem;
  background: rgba(17, 24, 39, 0.6);
  border-radius: 12px;
  border: 1px solid rgba(75, 85, 99, 0.3);
}

.competition-stats-bar .stat-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.competition-stats-bar .stat-item:hover {
  background: rgba(251, 191, 36, 0.1);
  border: 1px solid rgba(251, 191, 36, 0.3);
}

.stat-icon {
  font-size: 2rem;
  filter: drop-shadow(0 0 8px rgba(251, 191, 36, 0.5));
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.8rem;
  font-weight: 800;
  color: #fbbf24;
  text-shadow: 0 0 10px rgba(251, 191, 36, 0.5);
}

.stat-label {
  font-size: 0.9rem;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Phase Selector Section */
.phase-selector-section {
  margin-bottom: 3rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(229, 231, 235, 0.5);
}

.phase-selector-section h3 {
  font-size: 1.8rem;
  color: #fbbf24;
  margin-bottom: 0.5rem;
  text-align: center;
}

.phase-selector-section > p {
  text-align: center;
  color: #6b7280;
  margin-bottom: 2rem;
}

.phase-selector-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.phase-label {
  font-size: 1.1rem;
  color: #374151;
  font-weight: 600;
}

.phase-select {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(251, 191, 36, 0.3);
  border-radius: 8px;
  color: #374151;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  min-width: 300px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.phase-select:focus {
  outline: none;
  border-color: #fbbf24;
  box-shadow: 0 0 0 3px rgba(251, 191, 36, 0.2);
}

.phase-select:hover {
  border-color: rgba(251, 191, 36, 0.5);
}

.phase-details {
  display: flex;
  justify-content: center;
}

.phase-info-card {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(251, 191, 36, 0.3);
  border-radius: 12px;
  padding: 2rem;
  max-width: 500px;
  width: 100%;
  color: #374151;
}

.phase-info-card h4 {
  font-size: 1.5rem;
  color: #fbbf24;
  text-align: center;
  margin-bottom: 1.5rem;
}

.phase-stats {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.phase-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(17, 24, 39, 0.5);
  border-radius: 6px;
}

.phase-stat .label {
  color: #9ca3af;
  font-weight: 500;
}

.phase-stat .value {
  color: #374151;
  font-weight: 700;
  font-size: 1.1rem;
}

.phase-stat .value.highlight {
  color: #fbbf24;
  text-shadow: 0 0 8px rgba(251, 191, 36, 0.5);
}

.active-badge {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: #000;
  padding: 0.75rem;
  border-radius: 8px;
  text-align: center;
  font-weight: 700;
  margin-top: 1rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

/* Leaderboard Section */
.leaderboard-section {
  margin-bottom: 3rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(229, 231, 235, 0.5);
}

.leaderboard-section h3 {
  font-size: 1.8rem;
  color: #fbbf24;
  margin-bottom: 0.5rem;
  text-align: center;
}

.leaderboard-section > p {
  text-align: center;
  color: #6b7280;
  margin-bottom: 2rem;
}

.leaderboard-table {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(229, 231, 235, 0.5);
}

.leaderboard-header {
  display: grid;
  grid-template-columns: 80px 1fr 100px 120px 100px;
  gap: 1rem;
  padding: 1rem;
  background: rgba(251, 191, 36, 0.1);
  border-bottom: 1px solid rgba(251, 191, 36, 0.3);
  font-weight: 700;
  color: #fbbf24;
  text-transform: uppercase;
  font-size: 0.9rem;
  letter-spacing: 0.5px;
}

.leaderboard-row {
  display: grid;
  grid-template-columns: 80px 1fr 100px 120px 100px;
  gap: 1rem;
  padding: 1rem;
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
  transition: all 0.3s ease;
  align-items: center;
  color: #374151;
}

.leaderboard-row:hover {
  background: rgba(251, 191, 36, 0.05);
}

.leaderboard-row.top-three {
  background: rgba(251, 191, 36, 0.1);
  border-left: 4px solid #fbbf24;
}

.leaderboard-row .rank {
  font-size: 1.2rem;
  font-weight: 700;
  text-align: center;
}

.leaderboard-row .username {
  color: #374151;
  font-weight: 600;
}

.leaderboard-row .referrals,
.leaderboard-row .volume {
  color: #9ca3af;
  text-align: center;
}

.leaderboard-row .prize {
  color: #fbbf24;
  font-weight: 700;
  text-align: center;
}

.leaderboard-empty {
  text-align: center;
  padding: 3rem;
  color: #6b7280;
}

.leaderboard-empty p {
  font-size: 1.1rem;
  margin-bottom: 1rem;
}

/* Prize Tiers Section */
.prize-tiers {
  margin-bottom: 3rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(229, 231, 235, 0.5);
}

.prize-tiers h3 {
  font-size: 1.8rem;
  color: #fbbf24;
  margin-bottom: 0.5rem;
  text-align: center;
}

.prize-tiers > p {
  text-align: center;
  color: #9ca3af;
  margin-bottom: 2rem;
}

.tiers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.tier-card {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(229, 231, 235, 0.5);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  color: #374151;
}

.tier-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent, rgba(251, 191, 36, 0.05), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tier-card:hover::before {
  opacity: 1;
}

.tier-card:hover {
  transform: translateY(-5px);
  border-color: rgba(251, 191, 36, 0.5);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

.tier-card.champion {
  border-color: #fbbf24;
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(255, 255, 255, 0.9));
}

.tier-card.runner-up {
  border-color: #9ca3af;
  background: linear-gradient(135deg, rgba(156, 163, 175, 0.1), rgba(255, 255, 255, 0.9));
}

.tier-card.bronze {
  border-color: #cd7f32;
  background: linear-gradient(135deg, rgba(205, 127, 50, 0.1), rgba(255, 255, 255, 0.9));
}

.tier-card.elite {
  border-color: #8b5cf6;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(255, 255, 255, 0.9));
}

.tier-emoji {
  font-size: 3rem;
  margin-bottom: 1rem;
  filter: drop-shadow(0 0 10px rgba(251, 191, 36, 0.5));
}

.tier-name {
  font-size: 1.2rem;
  font-weight: 700;
  color: #374151;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.tier-prize {
  font-size: 2rem;
  font-weight: 900;
  color: #fbbf24;
  margin-bottom: 0.5rem;
  text-shadow: 0 0 15px rgba(251, 191, 36, 0.5);
}

.tier-rank {
  font-size: 1rem;
  color: #9ca3af;
  margin-bottom: 0.5rem;
}

.tier-description {
  font-size: 0.9rem;
  color: #6b7280;
  font-style: italic;
}

/* How It Works Section */
.how-it-works {
  margin-bottom: 3rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(229, 231, 235, 0.5);
}

.how-it-works h3 {
  font-size: 1.8rem;
  color: #fbbf24;
  margin-bottom: 2rem;
  text-align: center;
}

.steps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.step-card {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(229, 231, 235, 0.5);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  color: #374151;
}

.step-card:hover {
  transform: translateY(-3px);
  border-color: rgba(251, 191, 36, 0.5);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.step-number {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: #000;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 900;
  margin: 0 auto 1rem;
  box-shadow: 0 5px 15px rgba(251, 191, 36, 0.4);
}

.step-card h4 {
  font-size: 1.3rem;
  color: #374151;
  margin-bottom: 1rem;
  font-weight: 700;
}

.step-card p {
  color: #6b7280;
  line-height: 1.6;
}

/* Simple Footer Section */
.simple-footer {
  padding: 2rem 1rem;
  text-align: center;
  border-top: 1px solid rgba(229, 231, 235, 0.5);
  margin-top: 2rem;
}

.simple-footer p {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0;
}

/* Final CTA Section - Legacy (can be removed if not used elsewhere) */
.final-cta {
  padding: 3rem 2rem;
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(245, 158, 11, 0.05));
  border-radius: 16px;
  border: 2px solid rgba(251, 191, 36, 0.3);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.final-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, rgba(251, 191, 36, 0.1) 0%, transparent 70%);
  pointer-events: none;
}

.cta-content {
  position: relative;
  z-index: 1;
}

.final-cta h3 {
  font-size: 2.2rem;
  color: #fbbf24;
  margin-bottom: 1rem;
  font-weight: 900;
  text-shadow: 0 0 20px rgba(251, 191, 36, 0.5);
}

.final-cta > .cta-content > p {
  font-size: 1.2rem;
  color: #6b7280;
  margin-bottom: 2rem;
}

.cta-highlights {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 2.5rem;
}

.highlight {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(251, 191, 36, 0.3);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  color: #f59e0b;
  font-weight: 600;
  font-size: 0.9rem;
}

.final-join-btn {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: #000;
  font-size: 1.4rem;
  font-weight: 900;
  padding: 1.5rem 3rem;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 10px 30px rgba(251, 191, 36, 0.5);
  margin-bottom: 1rem;
  position: relative;
  overflow: hidden;
}

.final-join-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.final-join-btn:hover::before {
  left: 100%;
}

.final-join-btn:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 20px 40px rgba(251, 191, 36, 0.7);
}

.disclaimer {
  font-size: 0.9rem;
  color: #6b7280;
  font-style: italic;
  margin-top: 1rem;
}

/* Professional Dashboard Styles */
.professional-dashboard {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 1.5rem;
  margin: 1.5rem 0;
  border: 1px solid rgba(229, 231, 235, 0.5);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.metric-card {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(229, 231, 235, 0.5);
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
}

.metric-card:hover {
  border-color: rgba(251, 191, 36, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(251, 191, 36, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(251, 191, 36, 0.3);
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #fbbf24;
  display: block;
  line-height: 1.2;
}

.metric-label {
  font-size: 0.75rem;
  color: #9ca3af;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 0.25rem;
}

.phase-explorer {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid rgba(229, 231, 235, 0.5);
}

.phase-explorer-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.phase-explorer-header h3 {
  color: #fbbf24;
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.phase-explorer-header p {
  color: #9ca3af;
  font-size: 0.875rem;
}

.phase-details-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 1.25rem;
  margin-top: 1rem;
  border: 1px solid rgba(229, 231, 235, 0.5);
}

.phase-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.phase-card-header h4 {
  color: #fbbf24;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.phase-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.phase-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 6px;
  border: 1px solid rgba(229, 231, 235, 0.5);
}

.phase-stat .label {
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
}

.phase-stat .value {
  color: #fbbf24;
  font-weight: 700;
  font-size: 1rem;
}

.phase-stat .value.highlight {
  color: #10b981;
}

/* Mobile Responsive Styles for Gold Diggers Club */
@media (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.75rem;
  }

  .metric-card {
    padding: 0.75rem;
    gap: 0.5rem;
  }

  .metric-icon {
    width: 32px;
    height: 32px;
    font-size: 1.25rem;
  }

  .metric-value {
    font-size: 1rem;
  }

  .phase-stats-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .phase-card-header {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .gold-diggers-club {
    padding: 1rem;
    margin: 1rem;
  }

  .competition-hero {
    padding: 2rem 1rem;
    margin-bottom: 2rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .hero-description p {
    font-size: 1rem;
    padding: 0.75rem;
  }

  .join-club-btn {
    font-size: 1.1rem;
    padding: 1.2rem 2rem;
  }

  .competition-stats-bar {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1rem;
  }

  .phase-selector-container {
    flex-direction: column;
    gap: 0.5rem;
  }

  .phase-select {
    min-width: 100%;
  }

  .phase-info-card {
    padding: 1.5rem;
  }

  .leaderboard-header,
  .leaderboard-row {
    grid-template-columns: 60px 1fr 80px 100px 80px;
    gap: 0.5rem;
    padding: 0.75rem;
    font-size: 0.8rem;
  }

  .tiers-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .tier-card {
    padding: 1.5rem;
  }

  .tier-prize {
    font-size: 1.5rem;
  }

  .steps-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .step-card {
    padding: 1.5rem;
  }

  .final-cta {
    padding: 2rem 1rem;
  }

  .final-cta h3 {
    font-size: 1.8rem;
  }

  .final-join-btn {
    font-size: 1.1rem;
    padding: 1.2rem 2rem;
  }

  .cta-highlights {
    flex-direction: column;
    align-items: center;
  }

  .highlight {
    width: 100%;
    text-align: center;
  }

  /* Business Metrics Mobile */
  .business-metrics-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .metric-item {
    padding: 1rem;
    gap: 0.75rem;
  }

  .metric-item .metric-icon {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }

  .metric-content .metric-value {
    font-size: 1.25rem;
  }

  .phase-progress {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .join-club-btn,
  .final-join-btn {
    font-size: 1rem;
    padding: 1rem 1.5rem;
  }

  .leaderboard-header,
  .leaderboard-row {
    grid-template-columns: 50px 1fr 60px 80px 60px;
    font-size: 0.7rem;
    padding: 0.5rem;
  }

  .tier-emoji {
    font-size: 2rem;
  }

  .tier-prize {
    font-size: 1.3rem;
  }

  .step-number {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
}

/* Professional Business Metrics Styles */
.business-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(251, 191, 36, 0.2);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.metric-item:hover {
  border-color: rgba(251, 191, 36, 0.4);
  background: rgba(0, 0, 0, 0.6);
}

.metric-item .metric-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(251, 191, 36, 0.1);
  border-radius: 50%;
  flex-shrink: 0;
}

.metric-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.metric-content .metric-label {
  font-size: 0.875rem;
  color: #9ca3af;
  font-weight: 500;
}

.metric-content .metric-value {
  font-size: 1.5rem;
  color: #fbbf24;
  font-weight: 700;
}

.metric-content .metric-subtitle {
  font-size: 0.75rem;
  color: #6b7280;
}

.metric-content .metric-description {
  font-size: 0.875rem;
  color: #d1d5db;
  line-height: 1.5;
  margin-top: 0.5rem;
}

/* Executive Hero Styles */
.executive-hero {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.9));
  border-radius: 20px;
  padding: 3rem 2rem;
  margin: 2rem 0;
  border: 2px solid transparent;
  background-clip: padding-box;
  backdrop-filter: blur(15px);
  position: relative;
  overflow: hidden;
}

.executive-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(251, 191, 36, 0.1), rgba(245, 158, 11, 0.05), rgba(251, 191, 36, 0.1));
  border-radius: 20px;
  z-index: -1;
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.7; }
}

.executive-content {
  max-width: 1000px;
  margin: 0 auto;
}

.company-header {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid rgba(251, 191, 36, 0.2);
}

.company-logo img {
  border-radius: 8px;
  border: 2px solid rgba(251, 191, 36, 0.3);
  width: 64px !important;
  height: 64px !important;
  max-width: 64px !important;
  max-height: 64px !important;
  object-fit: contain;
}

.company-name {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #fbbf24, #f59e0b, #fbbf24);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 0.5rem 0;
  letter-spacing: -0.025em;
  animation: goldGlow 2s ease-in-out infinite;
}

@keyframes goldGlow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.company-tagline {
  font-size: 1.125rem;
  color: #d1d5db;
  margin: 0 0 1rem 0;
}

.company-credentials {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.credential {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.value-proposition {
  margin-bottom: 2rem;
}

.value-title {
  font-size: 2rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.value-description {
  font-size: 1.125rem;
  color: #d1d5db;
  line-height: 1.6;
  max-width: 800px;
}

.investment-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.metric-item {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
  padding: 1.5rem;
  border-radius: 16px;
  text-align: center;
  border: 1px solid rgba(251, 191, 36, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metric-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(251, 191, 36, 0.1), transparent);
  transition: left 0.5s ease;
}

.metric-item:hover {
  transform: translateY(-5px);
  border-color: rgba(251, 191, 36, 0.5);
  box-shadow: 0 10px 30px rgba(251, 191, 36, 0.2);
}

.metric-item:hover::before {
  left: 100%;
}

.metric-number {
  font-size: 2rem;
  font-weight: 700;
  color: #fbbf24;
  margin-bottom: 0.5rem;
}

.metric-label {
  font-size: 0.875rem;
  color: #9ca3af;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.primary-cta {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-primary {
  background: linear-gradient(135deg, #fbbf24, #f59e0b, #fbbf24);
  background-size: 200% 200%;
  color: #000000;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 1.125rem;
  position: relative;
  overflow: hidden;
  animation: buttonGlow 3s ease-in-out infinite;
}

.cta-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.cta-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(251, 191, 36, 0.4);
}

.cta-primary:hover::before {
  left: 100%;
}

@keyframes buttonGlow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.cta-secondary {
  background: transparent;
  color: #fbbf24;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  border: 2px solid #fbbf24;
  transition: all 0.3s ease;
  font-size: 1.125rem;
}

.cta-secondary:hover {
  background: rgba(251, 191, 36, 0.1);
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .company-header {
    flex-direction: column;
    text-align: center;
  }

  .company-name {
    font-size: 2rem;
  }

  .value-title {
    font-size: 1.5rem;
  }

  .investment-metrics {
    grid-template-columns: repeat(2, 1fr);
  }

  .primary-cta {
    flex-direction: column;
  }
}

/* Executive Summary Styles */
.executive-summary {
  background: rgba(15, 23, 42, 0.95);
  border-radius: 16px;
  padding: 3rem 2rem;
  border: 1px solid rgba(251, 191, 36, 0.2);
  backdrop-filter: blur(10px);
}

.summary-header {
  text-align: center;
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid rgba(251, 191, 36, 0.2);
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #fbbf24;
  margin-bottom: 1rem;
  letter-spacing: -0.025em;
}

.section-subtitle {
  font-size: 1.25rem;
  color: #d1d5db;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
}

.operations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.operation-card {
  background: rgba(31, 41, 55, 0.8);
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid rgba(75, 85, 99, 0.3);
}

.card-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #fbbf24;
  margin-bottom: 1.5rem;
  text-align: center;
}

.operation-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.operation-metric {
  text-align: center;
  padding: 1rem;
  background: rgba(15, 23, 42, 0.6);
  border-radius: 8px;
  border: 1px solid rgba(251, 191, 36, 0.1);
}

.metric-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #fbbf24;
  margin-bottom: 0.5rem;
}

.metric-label {
  font-size: 0.875rem;
  color: #9ca3af;
  margin-bottom: 0.25rem;
}

.metric-status {
  font-size: 0.75rem;
  color: #22c55e;
  background: rgba(34, 197, 94, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  display: inline-block;
}

.operation-details {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(75, 85, 99, 0.3);
}

.detail-text {
  color: #d1d5db;
  font-size: 0.875rem;
  line-height: 1.6;
  margin: 0;
}

.expansion-timeline {
  space-y: 1rem;
}

.timeline-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(15, 23, 42, 0.6);
  border-radius: 8px;
  border-left: 4px solid #fbbf24;
  margin-bottom: 1rem;
}

.timeline-year {
  font-size: 1.25rem;
  font-weight: 700;
  color: #fbbf24;
  min-width: 60px;
}

.timeline-content {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 1rem;
  align-items: center;
}

.timeline-target {
  font-weight: 600;
  color: #ffffff;
}

.timeline-area {
  color: #d1d5db;
  font-size: 0.875rem;
}

.timeline-status {
  font-size: 0.75rem;
  color: #60a5fa;
  background: rgba(96, 165, 250, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  text-align: center;
}

@media (max-width: 768px) {
  .operations-grid {
    grid-template-columns: 1fr;
  }

  .operation-metrics {
    grid-template-columns: 1fr;
  }

  .timeline-content {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .timeline-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .section-title {
    font-size: 2rem;
  }
}

/* Investment Highlights Styles */
.investment-highlights {
  margin-top: 3rem;
  padding-top: 3rem;
  border-top: 1px solid rgba(251, 191, 36, 0.2);
}

.highlights-header {
  text-align: center;
  margin-bottom: 2rem;
}

.highlights-title {
  font-size: 2rem;
  font-weight: 600;
  color: #fbbf24;
  margin-bottom: 0.5rem;
}

.highlights-subtitle {
  font-size: 1.125rem;
  color: #9ca3af;
}

.highlights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.highlight-card {
  background: rgba(31, 41, 55, 0.6);
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid rgba(75, 85, 99, 0.3);
  text-align: center;
  transition: all 0.3s ease;
}

.highlight-card:hover {
  border-color: rgba(251, 191, 36, 0.4);
  transform: translateY(-4px);
}

.highlight-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.highlight-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 1rem;
}

.highlight-description {
  color: #d1d5db;
  line-height: 1.6;
  font-size: 0.875rem;
}

@media (max-width: 768px) {
  .highlights-grid {
    grid-template-columns: 1fr;
  }

  .highlights-title {
    font-size: 1.5rem;
  }
}

/* Phase Progress Bar */
.phase-progress {
  margin-top: 2rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  border: 1px solid rgba(229, 231, 235, 0.5);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: #374151;
}

.progress-bar {
  width: 100%;
  height: 12px;
  background: rgba(229, 231, 235, 0.5);
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 0.75rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #fbbf24, #f59e0b);
  border-radius: 6px;
  transition: width 0.5s ease;
}

.progress-footer {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  color: #9ca3af;
}

/* Phase Selector Styles */
.phase-selector {
  margin-bottom: 2rem;
  text-align: center;
}

.phase-selector h3 {
  color: var(--primary-gold);
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.phase-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.phase-button {
  background: var(--bg-secondary);
  border: 2px solid rgba(75, 85, 99, 0.3);
  border-radius: 12px;
  padding: 1rem;
  min-width: 140px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-primary);
  position: relative;
}

.phase-button:hover:not(:disabled) {
  border-color: rgba(251, 191, 36, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.phase-button.active {
  border-color: var(--primary-gold);
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.15), rgba(251, 191, 36, 0.05));
  box-shadow: 0 0 20px rgba(251, 191, 36, 0.2);
}

.phase-button.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #fbbf24, #f59e0b);
  border-radius: 12px 12px 0 0;
}

.phase-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.phase-button .phase-name {
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.phase-button .phase-price {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--primary-gold);
  margin-bottom: 0.3rem;
}

.phase-button .phase-prize {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin-bottom: 0.3rem;
}

.phase-button .phase-status {
  font-size: 0.75rem;
  color: #9ca3af;
  font-style: italic;
}

/* Mobile responsive for phase selector */
@media (max-width: 768px) {
  .phase-buttons {
    flex-direction: column;
    align-items: center;
  }

  .phase-button {
    width: 100%;
    max-width: 300px;
  }
}

/* Simple Phase Selector Styles */
.phase-selector-simple {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.phase-label {
  color: #fbbf24;
  margin-right: 0.5rem;
}

.phase-select {
  background: rgba(255, 255, 255, 0.95);
  color: #374151;
  border: 1px solid #fbbf24;
  border-radius: 4px;
  padding: 0.5rem;
}

.prize-tiers {
  margin-bottom: 2rem;
}

.prize-tiers h3 {
  text-align: center;
  color: var(--primary-gold);
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
}

.tiers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.tier-card {
  background: var(--bg-secondary);
  border: 1px solid var(--primary-gold);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: transform 0.3s ease;
}

.tier-card:hover {
  transform: translateY(-5px);
}

.tier-emoji {
  font-size: 3rem;
  margin-bottom: 0.5rem;
}

.tier-name {
  font-size: 1.2rem;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.tier-prize {
  font-size: 1.5rem;
  color: var(--primary-gold);
  font-weight: bold;
  margin-bottom: 0.5rem;
}

/* Broken CSS classes removed - reverting to original design system */

.tier-rank {
  font-size: 0.9rem;
  color: var(--text-muted);
}

.competition-actions {
  text-align: center;
}

.join-competition-btn {
  display: inline-block;
  background: var(--gradient-gold);
  color: var(--bg-primary);
  padding: 1rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: bold;
  font-size: 1.1rem;
  transition: transform 0.3s ease;
  margin-bottom: 1rem;
}

.join-competition-btn:hover {
  transform: translateY(-2px);
}

.competition-note {
  color: var(--text-muted);
  font-size: 0.9rem;
}

.loading-container, .error-container {
  text-align: center;
  padding: 3rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 215, 0, 0.3);
  border-top: 4px solid var(--primary-gold);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retry-button {
  background: var(--primary-gold);
  color: var(--bg-primary);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: bold;
  margin-top: 1rem;
}
